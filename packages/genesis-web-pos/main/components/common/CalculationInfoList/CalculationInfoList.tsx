import { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Divider } from 'antd';

import { sortBy } from 'lodash-es';

import { Types } from 'genesis-web-shared';

import { CalculationView } from '@components/common/CalculationInfo/CalculationView';
import { PaymentPlan } from '@components/common/CalculationInfo/PaymentPlan';
import { CurrentCollectionAndPaymentView } from '@components/dataEntry/CurrentCollectionAndPayment';

interface CalculationInfoListProps {
  list: any[];
  loading?: boolean;
  updateNetPremium?: () => void;
  afterEffectiveDate?: string;
  afterExpiryDate?: string;
}

export const CalculationInfoList: FC<CalculationInfoListProps> = props => {
  const { t } = useTranslation(['pos']);
  const { list, loading = false, updateNetPremium, afterEffectiveDate, afterExpiryDate } = props;

  const isMultiplePolicy = !!list?.length;
  const sortList = useMemo(() => sortBy(list, 'caseNo'), [list]);
  return (
    <div id={'section_calculation_info'} className={'bg-white'}>
      {sortList?.map(listItem => {
        if (Types.isEmptyObject(listItem.installmentCalculationInfo)) return null;
        const { posBillInfo } = listItem;
        return (
          <Card
            key={listItem.caseNo}
            title={
              <div className="text-light">
                {t('Calculation Info')}
                <span className="ml-2 text-text text-big">{`${t('Policy No.')} ${listItem.policyNo}`}</span>
              </div>
            }
            className="m-4"
          >
            <div>
              <CalculationView
                {...listItem.installmentCalculationInfo}
                caseNo={listItem.caseNo}
                effectiveDate={
                  afterEffectiveDate ?? listItem.installmentCalculationInfo?.effectiveDate
                }
                expiryDate={afterExpiryDate ?? listItem.installmentCalculationInfo?.expiryDate}
              />
              <PaymentPlan
                {...listItem.installmentCalculationInfo}
                caseNo={listItem.caseNo}
                policyNo={listItem.policyNo}
                isNoShowSearch={false}
                refundGreenCardFeeAmount={
                  listItem.refundGreenCardFeeAmount ?? posBillInfo?.refundGreenCardFeeAmount
                }
                baseRefundGreenCardFeeAmount={
                  listItem.baseRefundGreenCardFeeAmount ?? posBillInfo?.baseRefundGreenCardFeeAmount
                }
                updateNetPremium={updateNetPremium}
                loading={loading}
                multiplePolicy={isMultiplePolicy}
              />
            </div>
            <div className="mx-4">
              <Divider className="mb-6 mt-10 border-lineSecondary" />
              <CurrentCollectionAndPaymentView
                feeAmount={posBillInfo?.sourceTotalFee ?? listItem.feeAmount}
                baseFeeAmount={posBillInfo?.baseTotalFee ?? listItem.baseFeeAmount}
                payFeeType={posBillInfo?.payFeeType ?? listItem.payFeeType}
                targetCurrency={posBillInfo?.sourceCurrency ?? listItem.currency}
                targetBaseCurrency={posBillInfo?.baseCurrency ?? listItem.baseCurrency}
                uncollectedFeeAmount={
                  posBillInfo?.sourceTotalUncollectedFee ?? listItem.uncollectedFeeAmount
                }
                refundFeeAmount={posBillInfo?.sourceTotalRefundFee ?? listItem.refundFeeAmount}
                baseRefundFeeAmount={
                  posBillInfo?.baseTotalRefundFee ?? listItem.baseRefundFeeAmount
                }
                baseUncollectedFeeAmount={
                  posBillInfo?.baseTotalUncollectedFee ?? listItem.baseUncollectedFeeAmount
                }
                uncollectedDeductionFeeAmount={
                  posBillInfo?.sourceTotalUncollectedDeductionFee ??
                  listItem.uncollectedDeductionFeeAmount
                }
                baseUncollectedDeductionFeeAmount={
                  posBillInfo?.baseTotalUncollectedDeductionFee ??
                  listItem.baseUncollectedDeductionFeeAmount
                }
                enableMultiCurrency={
                  posBillInfo?.enableMultiCurrency ?? listItem.enableMultiCurrency
                }
              />
            </div>
          </Card>
        );
      })}
    </div>
  );
};
