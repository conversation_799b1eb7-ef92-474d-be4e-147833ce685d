{"- -": "--", "--": "--", "'Charges Deducted From Policy Value' includes the following types of charges: Policy Admin Fee, Account Management Fee，ETF Broker Fee, ETF Management Fee, Premium Holiday Charge, Cost of Insurance, Rider Premium, Partial Surrender Charge": "“从账户价值中扣除的费用“中包含了以下几种费用类型：保单管理费、账户管理费、ETF 经纪费、ETF 管理费、保费假日费用、保障费用、附加险费用、部分退保费用。", "{{filename}} will be uploaded, the data is {{count}} records, please make sure to upload.": "{{filename}}将会被上传。文件中包含{{count}}条数据，请确认。", "%": "", "• Accumulate each liability SA -> Product SA is 2,000": "• 累加每个责任保额 -> 产品保额则为 2,000", "• Accumulate federated liability SA -> Product SA is 1,000": "• 累加联合保额 -> 产品保额则为 1,000", "Abbreviation": "", "Accelerating/Restoration": "1终止后对2的影响", "Accident Index Formula": "", "Account Lockout Period": "帐户锁定期", "Account Lockout Period (minimum value is 15 min)": "帐户锁定期 (最小值为15分钟)", "Accumulated Fund Dividend (Mock)": "", "Accumulated ILPBonus (Mock)": "", "Accumulated ILPCharges (Mock)": "", "Accumulated POS Withdrawal (Mock)": "", "Accumulated Premium (Mock)": "", "Accumulated Withdrawal (Mock)": "", "Actual Arrival Time": "实际到达时间", "Actual Departure Time": "实际出发时间", "Add": "新增", "Add a Condition": "增加条件", "Add a set of conditions": "新增一组条件", "Add Amount": "", "Add Component": "", "Add Condition": "", "Add Enumerated Fields Dependency": "", "Add Factor": "新增因子", "Add Formula": "新增公式", "Add New": "新增", "Add New as the Sub Level": "新增子集字段", "Add New Charge": "新增费用", "Add New Claim Stack Template": "新增理赔累加器模板", "Add New Condition": "新增条件", "Add New Formula": "新增公式", "Add New Liability": "新增责任", "Add New Product": "新建产品", "Add New Ratetable": "导入新费率表", "Add New Risk Sub Category": "新增风险子类型", "Add New Set": "新增分组", "Add New Version": "", "Add Ratetable": "新增费率表", "Add Strategy": "新增策略", "Add successfully": "添加成功", "Add Transportation Data": "新增交通数据", "Additional Clause": "扩展条款", "Additional Clause Name": "新增条款名称", "Additional Condition Factor": "", "Additional Condition Type": "附加条件类型", "Additional Grace Period": "额外宽限期", "Additional MB Formula": "额外的满期金公式", "Additional Participation Conditions": "额外参与条件", "Additional Trigger Conditions": "附加触发条件", "Addresses at the selected level and below can be selected in the drop-down box and support free editing.": "", "Adjust to 1st if Effective Day Not in Expiry Month": "起期日不在止期月份时顺延至1号", "Adjusted Market Value Floating Ratio": "车辆协商价值浮动比例", "Advanced Configuration": "高级设定", "After": "之后", "After (days)": "", "After ETI": "", "After Paid Up": "", "After(Days)": "后（天）", "Age": "岁", "Age Calculation": "年龄计算", "Age Calculation Basis": "年龄计算规则", "Age Validation": "年龄校验", "Age Validation Type": "年龄校验类型", "ageCalcBasis": "", "Agent Info": "代理人信息", "All Creations": "", "Allow Auto ETI": "允许自动展期", "Allow Auto ETI Tooltip": "当您允许自动展期保险时，系统将在宽限期结束后触发自动展期流程。如果产品还允许其他的不丧失现金价值选择权，则请在“默认ANFO部分”设置。", "Allow Auto Paid Up": "允许自动减额缴清", "Allow Auto Paid Up Tooltip": "当您允许自动减额缴清时，系统将在宽限期结束后触发减额缴清流程。如果产品还允许其他的不丧失现金价值选择权，则请在“默认ANFO部分”设置。", "Allow Automatic Premium Loan": "允许自动垫交保费", "Allow claim reserve equal 0": "允许准备金金额为0", "Allow Claim Reserve Equal 0": "", "Allow collect outstanding premium after policy termination": "允许保单终止后补缴保费", "Allow CPF Payment": "允许中央公积金支付", "Allow Flexible Premium Payment": "允许灵活缴费", "Allow Minimum Investment Period": "允许最小投资期", "Allow Minimum Reinstatement?": "允许复效时支付的保费不足?", "Allow Parital APL": "允许部分提前保单贷款", "Allow Partial Filling the ILP Premium": "允许部分交ILP产品的保费", "Allow Policy Loan": "是否允许保单贷款", "Allow POS Effective Without Collection": "非见费保全生效", "Allow Premium Decrease": "允许减少保费", "Allow Premium Increase": "允许增加保费", "Allow Renewal": "", "Allow SA Decrease": "允许削减保额", "Allow SA Increase": "允许增加保额", "Allow to apply waive the premium liability": "允许豁免保费责任", "allow to change premium": "允许改保费", "allow to change SA": "允许改保额", "Allow to Waive Interest": "允许豁免利息", "allow to withdraw CV": "允许录入现价提取金额", "Allow Uncollected Premium Deduction from POS Refund": "允许从保全退费中扣除未缴保费", "Allow Vesting": "允许归属", "Allowance Agreement": "津贴协议", "Allowance Amount": "津贴额", "Allowance Amount Type": "津贴金额类型", "Allowance Configuration": "", "Allowance Type": "津贴类型", "Allowance Unit Type": "津贴单位类型", "Allowance Unit Value": "单位津贴值", "Allowance Value Type": "津贴数值类型", "Allowed Investment Strategy": "允许的投资策略", "Allowed Range for Admission Time Compare to Incident Date (Hospitalization)": "允许入院时间范围与事故日期比较（住院）", "Allowed Range for Admission Time to Incident Date (Medical Bill)": "允许入院时间范围与事故日期比较（医疗账单）", "Allowed Range for Death Date Compare to Incident Date": "允许死亡日期范围与事故日期比较", "Allowed Range for End Date Compare to Incident Date(Allowance)": "允许津贴结束日期范围与事故日期比较", "Allowed Range for Outpatient Date to Incident Date (Medical Bill)": "允许门诊日期范围与事故日期比较（医疗账单）", "Allowed Range for Start Date Compare to Incident Date(Allowance)": "允许津贴开始日期范围与事故日期比较", "Allowed Vesting Age": "允许的归属年龄", "Amount Per Period": "每期金额", "Amount Per Time": "每次金额", "Amount Range": "金额范围", "AND": "和", "Annual Interest Rate": "年利率", "Annuity": "年金", "Annuity Leading Days": "年金提前生成天数", "Any modification of the current work shifts will impact the work schedules used, please check": "", "Applicable Business Type": "适用的业务分类", "Applicable Goods": "适用商品", "Applicable only to deductible and co-pay types of stacks": "仅适用于可抵扣和共付类型的理赔累加器", "Applicable Target": "适用对象", "Applied Goods": "", "Applied Unit": "应用单位", "Approve": "批准", "Are you sure delete this record?": "", "Are you sure to clear all records?": "确认要删除所有记录吗？", "Are you sure to clear this record?": "", "Are you sure to clear this section?": "确认要删除这个模块吗?", "Are you sure to delete this record?": "请确认是否删除该条记录？", "Are you sure to delete this template?": "", "Are you sure to delete this version?": "", "Are you sure to enable this feature? Once enabled, it cannot be turned off. Please confirm to proceed.": "", "Are you sure to export all configurations?": "", "Are you sure to import all configurations?": "", "Arrival Airport": "到达机场", "Arrival City/Place": "到达城市/地点", "Attach to Policy": "记录在保单上", "Auto Deduction Date": "自动扣款日", "Auto Deduction Date Compare to Due Date": "自动扣款日与缴费日的比较", "Auto Deduction Days Before Due Date": "到期日前多少天作为自动扣款日", "Auto Repay APL/PL": "自动重新支付提前保单贷款/保单贷款", "Auto Termination Rule": "自动终止规则", "Auto Trigger Premium Holiday": "自动触发保费假期", "Automatic Adjustment": "", "Available DCA Frequency": "允许的DCA频率", "Available Option": "可选计划", "Back": "返回", "Back to Search": "返回查询", "Bank": "银行", "Bank Branch Address": "支行地址", "Bank City": "银行所在城市", "Basic": "", "Basic Component": "基本组件", "Basic Information": "", "Batch Test": "批量测试", "Before (days)": "", "Before(Days)": "前（天）", "Benefit Calculation": "利益计算", "Benefit Illustration": "利益演示", "Benefit Illustration Configuration": "利益演示配置", "Benefit Illustration Test": "利益演示测试", "Benefit Option": "福利选项", "Benefit Option  Name": "福利选项名称", "Benefit Option Code": "福利选项代码", "Benefit Option Details": "", "Benefit Option Name": "", "Benefit Options cannot be repeated.": "福利选项不能重复", "Benefit Payment": "利益支付", "Benefit Schedule & Rate": "利益计划&费率", "Bill Conditions": "账单相关条件", "Bill Item": "票据项目", "Bill Item Sort": "", "Bill Object": "比尔对象", "Birthday": "生日", "Bonus Allocation Date": "分红日期", "Bonus Code": "分红编码", "Bonus Date": "分红日", "Bonus Formula": "红利公式", "Bonus Frequency": "分红频率", "Bonus Handling After ETI": "展期后的红利处理", "Bonus Handling After Paid Up": "减额缴清后的红利处理", "Bonus Name": "分红名称", "Bonus Option": "红利选择权", "Bonus Period": "分红期间", "Bonus Period Factor": "分红期间因子", "Bonus Period(In Years)": "分红周期", "Bonus Rate Declaration": "分红率申报", "Bonus Rate Declaration List": "分红率申报列表", "Bonus Rate Type": "分红率类型", "Bonus Start from": "红利开始于", "Bonus Type": "分红类型", "Bonus/Malus Table": "无赔优等级表", "bonusRateType": "分红率类型", "Both: Compliance decision can be provided by user or external triggered, system will take the previous one between the two values.": "", "browse": "浏览", "Browse": "", "Bundle Rules": "", "Business": "", "Business Component": "业务组件", "Business Date": "业务时间", "Business Element Group": "", "Business Interruption Loss Configuration": "", "Business Scenario": "", "Business Time": "", "Business Transaction": "业务交易", "Business Type": "业务类型", "Buy/Sell Unit Price": "买/卖基金单位价格", "By": "", "Caculation": "计算", "Calculate from Standard Premium": "", "Calculate Product SA by Liability SA": "根据责任保额计算产品保额", "Calculate Success": "计算成功", "Calculation": "计算", "Calculation Accuracy": "计算结果精度", "Calculation Accuracy List": "计算结果精度组", "Calculation Basis": "计算基准", "Calculation Basis Of Max Insured’s Entry Age": "被保人最大保险期间计算规则", "Calculation Basis Of Min Insured’s Entry Age": "被保人最小保险期间计算规则", "Calculation Details": "计算细节", "Calculation Direction": "计算方向", "Calculation Frequency": "计算频率", "Calculation Level": "费用层级", "Calculation Management": "计算管理", "Calculation Method": "计算方法", "Calculation Order": "计算顺序", "Calculation Priority Order": "优先计算", "Calculation Type": "计算类型", "Calendar Date (per year)": "日历日期（每年）", "Can,t find stack": "找不到累加器", "Cancel": "取消", "Cancel pin": "取消固定", "Cancellation Reason": "", "Cancellation Sub Reason": "", "Cancellation Type": "取消类型", "Cancellation Type & Cancellation Reason": "", "Cancellation Type Reason": "", "Cascade": "级联", "Cash Bonus": "现金红利", "Cash Bonus Allowed": "是否有现金红利", "Cash Refund Formula": "现金退款公式", "Cash Value Calculation Level": "现金价值计算等级", "cashValueTypes": "现金价值类型", "Change installment premium calculation method will clear the existing configuration, please confirm.": "切换续期保费计算方式，会清空现有配置信息。", "Change the switch will clear the current configured data, are you sure to do it?": "", "Changes have not been saved.": "修改的内容还未保存。", "Changes made after submission will not be revoked": "", "Changing vehicle database model or fields will clear the existing configuration, please confirm.": "更改车型库模式或字段将清除现有配置，请确认。", "Channel Enumerated Values": "渠道枚举值", "Channel Info": "渠道信息", "Charge": "费用", "Charge Amount After": " ", "Charge Code": "费用编码", "Charge code is duplicated, please check.": "费用编码重复，请确认", "Charge Code should start with ILPC and followed by 3 digits": "", "Charge For Investment Product": "投资类产品的费用", "Charge Formula": "费用计算公式", "Charge Free Month": "费用免除保单月", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value.": "当前产品已存在费用免除保单月，费用免除至少应从第一个保单月开始。请填写“费用免除起始日期”。", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value. ": "", "Charge Free Period": "免费用的时间段", "Charge Free Start From": "费用免除起始日期", "Charge Handling": "扣费期间因子", "Charge Period Factor": "扣费期间因子", "Charge Type": "费用类型", "Checking whether there are any products using this template...": "", "Child Field": "", "Child Field Name": "", "Cited Times": "被引用次数", "Claim Agreement": "理赔约定", "Claim Bill Item Setting": "索赔项目设置", "Claim Calculation": "理算方式", "Claim Clawback": "理赔收回", "Claim Compensation Amount": "索赔金额", "Claim Eligibility and Policy Matching": "理赔资格与匹单", "Claim Incident Type": "理赔事件类型", "Claim Limit": "理赔上限", "Claim Reserve Setting": "索赔准备金设置", "Claim Scenario": "理赔场景", "Claim Section": "理赔", "Claim Stack": "理赔累加器", "Claim Stack Correlation": "累加器绑定", "Claim Stack Correlation Info": "理赔累加器相关信息", "Claim Stack Definition": "理赔累加器定义", "Claim Stack Info": "理赔累加器信息", "Claim Stack Management": "理赔配置管理", "Claim Stack Query": "理赔累加器查询", "Claim Stack Template": "理赔累加器模板", "Claim Stack Template Management": "理赔累加器模板管理", "Claim Status": "理赔状态", "Claim Type": "理赔类型", "Claim type of the selected liability is not supported, please Confirm.": "所选责任的理赔类型暂不支持，请确认。", "Claimable Loss Party": "可赔付对象", "Claimable Loss Party Type": "可赔付损失方类型", "claimHistoryTimes（Mock）": "", "Claims Ratio": "索赔比率", "claimStatus": "理赔状态", "Clear": "重置", "Clear All": "清除全部", "Clear Factor": "", "Clear successfully": "清空成功", "clear the all datas will delete this version, are you sure to delete all?": "", "Clear the diagnosis configuration": "清空诊断配置", "Clear the surgery configuration": "清除手术相关配置", "Clear the surgery tag1 configuration": "", "Clear the surgery tag2 configuration": "", "Click Edit on the right to configure multiple-language for the name": "单击右侧的按钮，为名称配置多种语言", "Click here to upload": "点击这里上传", "Click or drag the file here to upload": "点击或拖到文件到此进行上传", "Click to the right of the title to edit the name format value": "点击标题的右边来编辑名称格式的值。", "Close": "关闭", "code": "", "Code": "代码", "Collect": "收取", "Collect Extra Installment Premium?": "是否收取额外期缴保费？", "Collect Overdue Premium Interest": "收取欠缴保费利息", "Collect the entire premium when a claim is made": "", "Collection Payment Info": "收付款信息", "Combination Relationship": "关联关系", "Combined liability cash value calculation could not contain optional liability.": "参与现金价值计算的责任必须为必选责任。", "Commission Clawback": "佣金召回", "Common Enumerated Values": "通用枚举值", "Comparison Type": "", "Compensation Bill Item": "补偿账单项目", "Compensation Claim Info": "补偿相关理赔信息", "Compliance Enumerated Values": "合规枚举值", "Component": "组件", "Component Code": "", "Component Description": "", "Component Instruction": "组件说明", "Component Name": "", "Component Query": "", "Condition": "条件", "Conditional UW Agreement": "核保条件配置", "Conditions": "条件", "Configuration History": "配置历史", "Configure": "配置", "Configure Benefit Factors": "", "Configure Benefit Option Detail": "配置福利选项详细信息", "Configure Payment & Collection Method": "配置收付款方式", "Configure product categories for the tenant.": "为租户配置险种类型", "Configure the association between fields": "配置字段间的关联关系", "Confirm": "确定", "Confirm Export": "", "Confirm Import": "", "Confirm to submit the product?": "请确认是否提交该产品？", "Confirmation Period": "确认期间", "Conversion Date": "换算日期", "Copy Formula": "复制公式", "Copy Link": "复制链接", "Copy Reminder": "复制提示", "Copy Successfully": "复制成功", "Copy to New Product": "复制产品", "Copy to New Version": "复制生成新营销商品版本", "Coverage Data Change Type": "保障期间变更类型", "Coverage Date Change Type": "", "Coverage Period": "保障期间", "Coverage Period Fields to Revise": "", "Coverage Period Range": "保障期间范围", "Coverage Period Type": "保险期间类型", "Coverage Period Value Type": "保障期间", "Coverage Type": "保障类型", "Create": "创建", "Create successfully": "", "Create_time": "创建时间", "Creator": "创建人", "Critical illness name": "重疾名称", "Critical illness stage": "重疾阶段", "Customer Enumerated Values": "客户枚举值", "Customized Data": "", "CV Formula of Basic SA": "现价公式（基础保额）", "CV Formula of Campaign Free SA": "现价公式（免费赠送保额）", "CV Formula of Reversionary Bonus": "现价公式（保额红利）", "CV is sufficient for one period APL": "现金价值可以满足一期保单自动垫缴保费贷款", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "按日计算：系统按日计算日均保费，再根据每期账单对应天数计算每期保费。(对于整年期保单，每期保费可能不同)", "Data filling": "输入数据", "Data has existed. please check!": "数据已存在，请检查。", "Data Type": "数据类型", "Day of Fixed Due Date": "固定日", "Day of Usage Premium Start Date": "基于使用的保险保费计算开始日", "Day(s)": "天", "Day(s) After Due Date": "超过约定缴费日", "Day(s) Before Due Date": "比约定缴费日提前", "Days": "天", "Days Type": "天数类型", "DCA Amount": "DCA金额", "DCA Frequency": "DCA频率", "DCA frequency cannot be lower than premium frequency.": "DCA频率不能低于缴费频率", "DCA Frequency Type": "DCA频率类型", "Deactivate Insured": "失效的被保人", "Deactivate Insured After Fully Claimed": "保额全部赔付完成后的被保人失效", "Deduction cut-off date upon policy terminates": "保单终止时的扣费截止日期", "Deduction Date": "", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "如果在缴费日当天扣款，请输入0.如果在缴费日之前扣款，请输入1,2,3等", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "如果在生效日当天扣款，请输入0.如果在生效日之前扣款，请输入1,2,3等", "Deduction Source": "扣费来源", "Default ANFO Setting Up": "默认不丧失现金价值选择权设置", "Default as Reinvestment if dividend amount per fund is less than": "当基金分红金额小于{xxx}，默认分红再投资。", "Default as tenant": "默认为租户配置", "Default logic for refunded premium is confirmed instalment premium after incident date. If not, refund calculation formula needs to be configured here.": "退还保费金额默认为事故日后已收款确认的期交保费。若不是，请在这里配置退还保费公式。", "Default Option": "默认选项", "Default Time": "", "Default Time Editable": "", "Defer Period": "等待期间", "Deferred Interest": "递延利息", "Define": "定义", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "", "Define Benefit Options": "定义福利选项", "Define Factor & Ratetable by Scenario": "按场景定义因子和费率表", "Define Fund Calculation Basis for Each Transaction": "定义交易对应的基金价格计算依据", "Define Illustration Sequence": "设定利益演示顺序", "Define the day before policy expired date and days after expired date to extract renewal policy": "定义提取续保保单的日期（与保单失效日相比的天数）", "Define the times of product sa and planned premium.": "定义险种保额和计划保费之间的倍数关系", "Define whether it is the normal policy insure date or effective date should be within relevant master policy effective period when normal policy issuance.": "定义当普通保单出单时是否限制其投保日或生效日应在相关的团体保单有效期间之内。", "Defined Application Elements": "固定投保要素", "Delay Type": "延误类型", "delete": "删除", "Delete Failed": "", "Delete Successfully": "删除成功", "Delete successfully.": "删除成功", "Delete will affect the associated work scheduling. Are you sure to delete it?": "", "Deleted successfully": "删除成功", "Deleting this record will affect the associated rider relationships. Please delete the related rider relationship records first.": "删除此记录将影响关联的附加险关系，请先删除相关的附加险关系记录。", "Departure Airport": "出发机场", "Departure City/Place": "出发城市/地点", "Departure Date": "出发日", "Description": "描述", "Deselect this goods will remove it from related bundle rules in step 2. Please confirm.": "", "Diagnosis": "诊断", "Diagnosis Code": "诊断编号", "Diagnosis Configuration Method": "诊断配置方式", "Diagnosis Description": "诊断描述", "Diagnosis Set": "", "Digit": "", "Disability Category": "残疾分类", "Disability Classification": "残疾分类", "Disability Grade": "残疾等级", "Disability Set": "残疾分组", "Discount Sequence": "折扣计算顺序", "Discount Type": "", "Disease Agreement": "疾病协定", "Disease Classification": "疾病分类", "Distribution Method": "分红方式", "DMS": "", "Dollar Cost Averaging Arrangement": "美元成本平均法的安排", "Down Sell Indicator": "减额/减保", "Download Result": "下载结果", "Download Successfully": "下载成功", "Download Template": "下载模板", "Draft": "", "Drag to adjust the calculation order": "拖动调整计算顺序", "Due Date": "约定缴费日", "Due Date Rules": "截止日期规则", "Dunning Rule": "催缴规则", "Duplicate data": "重复数据", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "相同的公式类型及子类型下已有重复的公式配置，请编辑已配置的公式。", "Duplicate formula configuration for same formula sub category and tax type. Please edit original one.": "相同的公式子类型及税类型下已有重复的公式配置，请编辑已配置的公式。", "Duplicated data exists, please check the Original Currency and Target Currency.": "重复数据，请检查原始币种和目标币种。", "Duplicated Interest!": "", "E.g.: If there exists multiple liabilities for one insured. User could define whether to deactivate this insured when one of the liability has been fully claimed.": "如：当一个被保人有多项被保责任，您可以定义当其中一项责任完全理赔后是否失效该被保人。", "E.g.: If there exists multiple liabilities under one product. User could define whether to terminate this liability/Product when one of the liability has been fully claimed.": "如：当一个险种下有多条责任，您可以定义当其中一项责任完全理赔后是否终止该责任或险种。", "Each premium frequency can only configure 1 DCA frequency record.": "每个缴费频率只能定义一条DCA频率。", "EARLIEST OF": "", "Edit": "编辑", "Edit Charge": "编辑费用", "Edit Claim Stack Template": "编辑理赔累加器模板", "Edit Component": "", "Edit Enumerated Fields Dependency": "", "Edit Factor": "", "Edit Formula": "编辑公式", "Edit Liability": "编辑责任", "Edit Ratetable": "编辑费率表", "Edit Reminder": "编辑提醒", "Edit Tag Definition": "", "Editing": "编辑中", "EDITING": "", "Editing any formula or ratetable within the new or original product will impact its calculations.": "", "Editing any formula or ratetable within the new or original version will impact its calculations.": "", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "编辑福利选项因子将会清空已上传的福利选项详细信息，请确认。", "EFFECTIVE": "", "Effective Date Month End to Expiry Date Month End": "", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "起期月末对止期月末：如果起期是所在月的最后一天且起期加保险期间不是止期所在月的最后一天，则调整止期为月末日。（如：20230228 ~ 20240228 需将止期调整为 20240229 -> 另外，若选择了减 1 秒 则为  20230228 00:00:00 ~ 20240228 23:59:59)", "Effective Date Rule": "保单生效日期规则设置", "Effective Period": "期限协议", "Effective Time": "", "Element": "要素", "Embedded Ratetable": "原费率表", "Enable Green Card Issuance Logic for Non-EU Countries": "", "Enable Non-Fleet Grade and Accident Index Configuration": "", "End date": "截止日期", "Enumerated Fields Dependency": "", "Error": "错误", "Error Description": "", "error format, eg: 1/1.0/1.00": "输入格式错误、例1/1.0/1.00", "Estimated Lapse Date Formula": "预期失效日期公式", "Every": "每个", "Example": "例子", "Example: V01": "", "Exception Output": "异常结果", "Excess Premium Handling": "剩余保费的处理方式", "Exchange Rate": "汇率", "Exchange Rate (Buying)": "买入汇率", "Exchange Rate (Middle)": "中间汇率", "Exchange Rate (Selling)": "卖出汇率", "Exchange Rate Query": "汇率潮汛", "Exclusion Indicator": "是否可以限制保额", "Existing Stack in the Product": "产品中的现有累加器", "Exists duplicate value": "存在重复的值", "Exit Full Screen": "退出全屏", "Expand": "展开", "Expiry Date Adjustment": "止期调整规则", "Expiry Date Calculation Method": "止期计算方法", "Expiry Date Rule": "满期日规则", "Expiry Time Agreement Type": "止期计算方式", "Export": "", "Export All": "", "Export failed, please try again!": "", "Export Operation Fail": "", "Export Range": "", "Export successfully!": "", "Export to CSV": "导出csv文件", "Extended Term": "展期", "Extended Term Formula": "展期公式", "External: Compliance decision can only be provided by external triggered.": "", "Extra Grace Period": "额外宽限期天数", "Extra Loading": "加费", "Extra Loading Calculation Method": "加费计算方法", "Extra Loading Indicator": "是否可以加费", "Extra Loading Type": "加费类型", "Extra Premium Formula Code": "加费公式编码", "Extract Bill Date": "提取账单日期", "Extract Bill Days Before Due Date": "到期日前多少天为提取账单日", "Extract day(s) must be greater than offset day(s)": "提取账单日必须早于账单冲销日", "Extract premium from X days prior to due date": "抽档日比约定缴费日提前X天", "Extraction Method": "", "Factor": "因子", "Factor Code": "因子代码", "Factor Definition": "因子定义", "Factors": "因子", "Fee Limit": "费用/次数限额", "Fee Type": "医疗费用类型", "Field Value": "字段值", "File": "文件", "File Declaration": "文件说明", "File Management": "", "File Name": "", "File Type": "文件类型", "FILE TYPE": "文件类型", "file upload failed": "上传文件失败", "file uploaded successfully": "上传文件成功", "File Version": "文件版本", "Filter": "筛选", "Financial Anniversary Date": "财务周年日", "First": "", "First Date": "", "first.": "", "Fixed Age": "", "Fixed Amount": "固定金额", "Fixed Amount or Fixed Period": "固定金额或固定期间", "Fixed due date rule：Fixed due date or period end date": "固定日规则：设定的固定日期、或者期末日", "Fixed Due Date Value": "固定到期日", "Fixed Exchange Rate": "固定汇率", "Fixed Time": "", "Fixed Value": "固定值", "Fixed Value is required": "固定值为必填", "Flexible Premium Allocation": "灵活缴费分配", "Flexible Premium and SA Agreement": "弹性保费与保额协议", "Flexible Premium Relationship": "弹性保费关系", "Flexible Premium Type 1": "弹性保费类型1", "Flexible Premium Type 2": "弹性保费类型2", "Floating Exchange Rate": "浮动汇率", "Follow the same as Planned Premium": "跟随基础保费", "For Fixed Day of Month, please input number from 1 and 28": "", "For formula(s) or ratetable(s) created inside one specific product, the editing function is not supported.": "产品内部创建的公式/费率表，不支持在此入口进行编辑。", "For formulas that use the list function, Batch Test is not supported yet.": "使用了列表函数的公式暂不支持批量测试。", "For liabilities without combination relationship configured, it will be settled as \"Attachable\" by default. If you configure liability combination relationship as \"Compulsory\", it means liability 2 depends on liability 1; And if you configure as \"Non-attachable\", it means liability 1 and liability 2 can not be sold in combination.": "为配置组合关系的责任默认关系为可附加关系。\n强制关系：选择责任2时必选责任1；\n不可附加关系：责任1与责任2互斥。", "For New-Biz": "用于新保", "For policy annual illustration": "用于保单年度利益演示", "For POS": "用于保全/批改", "For POS triggered illustration": "用于保全/批改触发的利益演示更新", "For step {{index}}, nested calculation components within the ifElse component are not allowed. Please create a separate step before ifElse for calculations.": "对于步骤{{index}}，请将计算组件前置到上一步，以避免在条件判断中同时使用比较符号和计算组件。", "For Usage based insurance, accrued premium is calculated according to actual usage during certain period. Usage Premium Start date represents the first day of usage period. And the end date will be calculated based on Usage Premium Start Date and payment frequency.": "对于基于使用的保险，应付保费是基于特定时间的实际使用计算而来的。基于使用的保费开始日期代表使用区间的第一天。结束日期是基于开始时间和支付频率计算得到。", "Format": "格式调整", "formula": "公式", "Formula": "公式", "Formula Category": "公式分类", "Formula Code": "公式代码", "Formula Code could not start with \"G_\".": "公式代码不能以\"G_\"开头", "Formula Code could not start with numbers.": "公式code不能以数字开头。", "Formula Description": "公式描述", "Formula Detail": "公式详情", "Formula in Specific Scenarios": "自定义场景中的公式", "Formula Level": "公式层级", "Formula Management": "公式管理", "Formula Name": "公式名称", "Formula Sub Category": "公式子类型", "formulas": "公式", "Free Switch Times": "免费转换次数", "Free Withdrawal Time(s)": "账户提领免费次数", "Freelook Period": "犹豫期", "Freelook Period Base Date": "犹豫期基准日", "Freelook Period Expiration Rule": "犹豫期到期规则", "Freelook Refund Account Rule": "犹豫期退款账户规则", "Freelook Reverse Rules": "犹豫期退保回退规则", "Frequency": "频率", "Full Screen": "全屏", "Fund": "基金", "Fund Appointment Rate for Rebalance": "投资再平衡的基金分配比例", "Fund Calculation Basis Details": "基金计算依据详情", "Fund Calculation Basis of Product": "产品的基金计算依据", "Fund Code": "基金代码", "Fund Deduction Method": "基金扣除方式", "Fund Deduction Sequence": "基金扣除顺序", "Fund Info": "基金信息", "Fund Trad Type": "基金交易类型", "fund_name": "基金名称", "gender": "性別", "Gender": "性別", "Generate": "生成", "Generate Cash Value Saving Account": "生成现金价值储存账户", "Generate new policy number for renewal": "为续保保单生成新的保单号", "Generate Result": "生成结果", "Generate Test Excel": "生成测试用Excel", "Generate Vehicle Databases Fields": "生成车型库字段", "goods": "商品", "Goods / Package / Product not found during export process. Please check if the data exists and is correctly configured in the system.": "", "Goods A": "", "Goods allowed to be sold together": "", "Goods B": "", "Goods Code": "营销商品代码", "Goods Code/Goods Name": "商品代码/商品名称", "GOODS CONFIGURATION": "", "Goods Name": "营销商品名称", "GOODS RELATED CONFIGURATION": "", "Goods V2": "", "Got it": "", "Grace Period": "犹豫期期间", "Grace Period Rule": "宽限期规则", "Green Card Fee": "", "Green Card(Non-EU)": "", "Guarantee Period": "保证收益期", "Guarantee Period Value Type": "保证收益期值类型", "Handling of Charge Deduction within or out of NLG": "费用扣除处理方式（当保单在保证不失效期间内或超出该期间）", "Handling of Premium Unpaid": "未付保费的处理方式", "Handling of TIV Insufficient": "投资账户余额不足处理", "Has Cash Value": "是否有现金价值", "Have SA": "是否有保额", "Health Claim Info": "健康索赔信息", "HH:mm": "", "Holiday": "", "Hospital/Clinic": "医院/诊所", "Hospitalization Allowance Amount": "住院津贴额度", "Hospitalization Allowance Value Type": "住院津贴类型", "Hospitalization start date be within x days/months/years after Incident date. E.g: if hospitalization start date should be within 1 year after Incident date, configure as 1, Year(s)).": "住院开始日期应在事故日后多少天/月/年内。如：住院开始日需在事故日的一年内，这里就定义为 1，年。", "Hour": "小时", "ID": "ID", "if calculation sequence is needed，please make sure all discount have sequence definition.": "如果折扣计算需要顺序，请确保每一条折扣公式都配置了计算顺序。", "if calculation sequence is needed，please make sure all loading have sequence definition.": "如果加费计算需要顺序，请确保每一条加费公式都配置了计算顺序。", "If cash value have surplus after ETI": "如果展期后现金价值有盈余", "If cash value have surplus after paid up": "如果在减额缴清后现金价值有盈余", "If choose 'Refund', premium collection will be reserved in suspense and refunded later. If choose 'Fill in the ILP premium plan or installment premium', system will settle the installment premium or partially fill the ILP premium plan.": "如果选择”退费“，保费收入会被放入悬帐以作退费。如果选择”缴ILP的保费或期缴保费“，系统会冲销续期保费或部分缴ILP产品的保费。", "If choose No, RTU will share same payment frequency with Planned Premium. If choose Yes, RTU is allowed to use different payment frequency with Planned Premium, which could be defined below.": "如果选择“否”，定期追加投资会与期缴保费使用相同的缴费频率。如果选择“是”，定期追加投资可以在下方定义并使用不同的缴费频率。", "if condition": "", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "if illustration sequence is needed，please make sure all illustration items have sequence definition.": "如果利益演示顺序是必须的，请确保所有的利益演示项定义了顺序值。", "If liability could not be found here, please check whether liability has configured claim type in liability management module": "如果此处找不到责任，请检查责任管理模块中是否配置了理赔类型", "If no parameter could be found, you could create middle factor by using the format Middle_XXX.": "如果找不到想要的参数，您可以创建中间因子，名称以\"Middle_\"开头。", "If not defined on the product, the rules defined on the package will be used (allowing for unified definition directly when packaging the package)": "", "If someone planning to drive in non-EU countries that recognize the Green Card, he/she must request the card from auto insurance provider before traveling. This function covers the Green Card logic for purchasing auto insurance policies in non-EU countries, including fees, issuance, endorsements, refunds, printing, etc.": "", "If surgery classification needed, please configure 'Surgery Level' in advance!": "如果需要手术分类，请提前配置‘手术标准’！", "If the customer is responsible for 10%, the value is set to 10/.": "如果客户需要承担10%，则该值配置为10", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "当起期是一个在止期月份中不存在的日期，系统会将止期定为当月最后一天。选择此选项后，止期将自动调整到下个月的 1 号。（例：2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1）", "If the premium is insufficient to pay any outstanding billing or ILP premium plan, then": "如果保费收入不足任意一个账单或不足缴满一期ILP产品保费,那么", "If this product has waiver liability, all liabilities must be mandatory.": "如果本险种含有豁免责任，则所有的责任为必选。", "If value of the factors are different across period.": "如果不同演示期数因子的值不一致", "If value of the factors are same across period.": "如果不同演示期数因子的值一致", "If you select yes，system support to add insurable interest under liability.": "如果您选择\"是\"，系统支持在责任下添加可保利益信息。", "Illustration Item": "利益演示项目", "Illustration Item Code": "利益演示项代码", "Illustration Item List": "演示项列表", "Illustration Item Value cannot starts with 'G_'": "利益演示项的值不能以\"G_\"开头", "Illustration Item(PSI)": "利益演示项（PSI）", "Illustration Level": "演示层级", "Illustration Period": "利益演示时间区间", "Illustration Period Rule": "演示期间规则", "Illustration Scenario": "演示场景", "Illustration Sequence": "演示顺序", "ILP Bonus": "ILP分红", "ILP Charge": "投连险费用", "ILP Fund Calculation Basis": "ILP基金计算依据", "ILP Lapse": "ILP失效", "ILP Premium Holiday": "ILP保费假期", "ILP Premium Limit": "ILP保费限额", "ILP TIV Account Type": "ILP账户价值类型", "ILPC + 3 digits": "", "Import": "", "Import / Export": "", "Import All": "", "Import Operation Fail": "", "Import Successful!": "", "In Japan's auto insurance system, Non-Fleet Grade and Accident Index are two key factors that affect policy rating and premium adjustments. This function is used to define the calculation formulas for Non-Fleet Grade and Accident Index.": "", "In Progress": "", "In use:": "", "Inactive": "失效", "Incident Date": "事故日期", "Incident Reason": "事故原因", "Including Tax": "是否含税", "Increase Interest By(%)": "", "Individual Test": "单笔测试", "Inforce Together": "必须同时生效", "Initial Premium Period": "初始保费期间", "input is illegal": "输入是非法的", "Insert fields": "", "Installment Calculation Method": "分期计算方式", "Installment Detail": "续期详情", "Installment Info": "分期信息", "Installment Payment Plan for Claims": "理赔款分期支付计划", "Installment Payment Type": "分期给付类型", "Installment Premium Calculation Basis": "分期保费计算方式", "Installment Premium Calculation Method": "续期保费计算方式", "Installment Standard Premium": "分期标准保费", "Installment Standard Premium (Inc Tax)": "分期含税标准保费", "Installment(s) Premium": "期期缴保费", "Insurable Interest ID": "可保利益ID", "Insurable Interest Info": "可保利益信息", "Insurable Interest Name": "可保利益名称", "Insurable Interest/Liability Rate Setting": "可保利益/责任费率设置", "Insured Age Comparison": "被保人年龄比较", "Insured Elements Relationship Matrix": "被保要素关系矩阵", "Insured Info": "被保人信息", "Insured Occupation Class": "被保人职业分级", "Insured Occupation Risk Category": "", "Insured's Entry Max Age": "被保人最大年龄", "Insured's Entry Min Age": "被保人最小年龄", "Insured‘s Entry Max Age": "被保人最大年龄", "Insured’s Age Range(NB)": "被保人年龄范围（新保）", "Insured’s Age Range(Renewal)": "被保人年龄范围（续保）", "Insured’s Entry Min Age": "被保人最小年龄", "Interest": "", "Internal: Compliance decision can only be provided by compliance user.": "", "Investment Agreement": "", "Investment Delay": "延迟投资", "Investment Delay Option": "延迟投资方式", "Is switch charge a fixed amount?": "账户转换费用是固定金额吗？", "is the default condition.": "是默认的条件", "Is there a need to make a lump sum payment before executing the installment plan?": "", "Issue with Green Card (Non-EU)": "", "It is also supported entering the exchange rate unit. You can open \"Component Instruction\" for more details.": "同时支持直接输入汇率单位，您可以查看”组件说明“获取更多信息。", "It will take a certain time for batch test. A platform message will be sent to you once the batch test is completed. And you could download test result through the message.": "批量测试需要一定的时间。完成后，将向您发送一条平台消息。您可以通过消息下载测试结果。", "itemExtend1": "", "itemExtend2": "", "itemExtend3": "", "itemExtend4": "", "itemExtend5": "", "itemExtend6": "", "Japan Motor": "", "LANGUAGE": "语言", "Lapse Date": "失効期日", "Lapse/Termination": "失效/终止", "Last Modifier": "最近修改人", "LATEST OF": "", "Less": "", "Liability": "责任", "Liability <{{liabilityId}}><{{liabilityName}}> has been created successfully.": "责任<{{liabilityId}}><{{liabilityName}}>创建成功。", "Liability <{{liabilityId}}><{{liabilityName}}> has been edited successfully": "责任<{{liabilityId}}><{{liabilityName}}>修改成功。", "Liability Agreement": "责任协议", "Liability Category": "责任分类", "Liability Code": "责任代码", "Liability Coverage Period": "责任保障期间", "Liability Coverage Period defaults to the same as Product Coverage Period. If it differs from Product Coverage Period, you can define an independent coverage period for the liability here.": "责任保障期间默认与产品保障期间一致。如果与产品保障期间不一致，可以在此处定义责任独立的保障期间。", "Liability Description": "责任描述", "Liability Handling": "责任处理", "Liability Handling After ETI": "展期后的责任处理", "Liability Handling After Paid Up": "减额缴清后的责任处理", "Liability ID": "责任ID", "Liability Info": "责任信息", "Liability information and POS items can not be configurated at the virtual main benefit. Do you want to delete them?": "虚拟主险不能配置责任信息和保全项，是否删除？", "Liability is required": "责任为必填项", "Liability Level": "责任层级", "Liability Name": "责任名称", "Liability Name(Liability1)": "责任名称（责任1）", "Liability Name(Liability2)": "责任名称（责任2）", "Liability or Interest is required": "至少填写一个责任或可保利益", "Liability Relationship Matrix": "责任关系矩阵表", "Liability Remark": "责任备注", "Liability SA": "责任保额", "Liability Tag": "责任标签", "Liability Termination": "责任终止", "Liability Termination by Claim Setting": "理赔导致的责任终止", "Lien Indicator": "", "Life Event": "生命事件", "Linked Formulas": "关联的公式", "Linked Goods": "关联的商品", "Linked Package": "", "Linked Packages": "关联的组合", "Linked Product / Package / Goods": "关联的险种 / 组合 / 商品", "Linked Products": "关联的险种", "Linked Ratetables": "", "Linked Rules": "", "Load More": "加载更多", "Loading Method": "加费方式", "Loading Sequence": "加费计算顺序", "Loading Type": "", "Loan Amount": "贷款金额", "Loan Term Period": "贷款期限", "Loan Term Type": "贷款期限类型", "Low Risk Fund": "低风险基金", "Lump Sum Amount": "一次性支付金额", "Main / Rider": "主险/附加险", "Main Benefit (Virtual)": "", "Main Condition Type": "主要条件类型", "Main or Rider": "主险或附加险", "Main Product Code": "主险代码", "Main Product Name": "主险名称", "Main Relationship with Rider": "", "Make sure at least one of the diagnosis tag has been configured.": "至少需要配置一个疾病标签。", "Make sure at least one of the disabilities has been configured.": "至少需要配置一个残疾组。", "Manual Adjustment": "", "Marketing Enumerated Values": "营销枚举值", "Matrix Table": "", "Matrix Table Code": "", "Maturity Agreement": "满期约定", "Maturity Benefit": "满期金", "Maturity Benefit Leading Days": "满期金提前生成天数", "Maturity Reminder Date Compare to Policy Expiry Date": "满期通知日（与保单满期日比较）", "Maturity Reminder Rule": "满期通知规则", "Maturity Reminder Rule (Legacy)": "", "Maturity Reminder Rule cannot be repeated.": "满期通知规则不能重复", "Max": "最大", "Max Claim Limit": "最高索赔限额", "Max Claim Percentage (%)": "最大索赔百分比%", "Max Claim Times": "最大理赔次数", "Max Down-Regulation Ratio": "最大下调比例", "Max Guaranteed Renewable": "最大可续保次数", "Max SA Multiplier": "最大保额系数", "Max SA Multiplier Type": "最大保额系数类型", "Max Up-Regulation Ratio": "最大上调比例", "Max value cannot be less than min value": "", "Maximum Age for Whole Life": "至终身年龄上限", "Maximum Loanable Amount Formula": "最大可贷款金额公式", "Maximum Loanable Percentage": "最大可贷款比例", "Maximum Premium Holiday": "最大保费假期", "Maximum Premium Holiday Period Type": "保険料収納一時停止最長期間", "Medical Bill Configuration": "", "Medical Bill Item": "", "Medical Bill Type": "", "Medical Bill Type and Bill Item": "医疗费用清单类型和明细项", "Medical Billing Type": "账单类型", "Medical Expense": "治疗费用", "Medical Fee Rules": "体检费用规则", "MENU": "菜单", "Middle factor format is not correct. Please follow format: Middle_XXX.": "中间因子名称格式不对，需以\"Middle_\"开头。", "Min": "最小", "Min / Max Times": "最小/最大次数", "Min SA Multiplier": "最小保额系数", "Min SA Multiplier Type": "最小保额系数类型", "Min Value of Policy": "提领后保单最小剩余价值", "Min Withdrawal Amount": "最小提领金额", "Minimum Investment Period": "最小投资期", "Minimum Investment Period Applied to": "扩展投资期适用于", "Minimum Investment Period Type": "MIP期限类型", "Minimum Investment Period Value": "MIP期限值", "Minimum Net Premium (Standard Premium+Extra Premium-Premium Discount) for coverage period if Net premium calculated from formula is below minimum net premium.If regular payment, minimum premium cap needs to be multiplied by modal factor.": "", "Minimum Premium Cap": "最小保费", "Minute(s)": "分钟", "Missing parameter": "缺少参数", "Missing required parameter": "缺少需要的参数", "Modal Factor": "要素因子", "Modifier": "", "Modifying the diagnosis tag info will auto clear the Diagnosis configuration. Please confirm whether to continue?": "修改诊断标签信息将自动清除“诊断”配置。请确认是否继续?", "Modifying the Surgery level info will auto clear the surgery configuration. Please confirm whether to continue?": "修改手术标准的信息将会导致手术有关的配置，请确认是否要继续？", "Modifying the Surgery tag1 info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Modifying the Surgery tag2 info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Modifying the tag definition info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Month": "月", "Month Value": "月度值", "Month Value for Advance Filling": "提前缴费的月数", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "按月计算：系统按月计算月均保费，再根据每期账单对应月数计算每期保费。(对于整年期保单，每期保费相同)", "Months": "月", "Multi Language": "", "Multi-language": "多语言", "My Creations": "", "NA": "", "Name": "名称", "Name Connection Method": "姓名拼接方式", "Name Format": "姓名格式", "Name Sequence Definition": "姓名顺序定义", "NCD Code": "NCD 代码", "NCD Formula Code": "NCD 公式代码", "NCD Management": "NCD 管理", "Need To Attach Advanced Criteria": "需要附加高级条件", "Net Premium Adjustment": "", "New Category": "", "New Claim Stack Structure": "", "New Product after ETI": "展期后的新产品", "New Product after Paid Up": "减额缴清后的新产品", "New Version": "", "Next": "下一步", "NLG Condition": "猶予期間の条件", "NLG Period": "不失效保证期", "NLG Period Factor": "NLG 期间因子", "NLG Period Type": "猶予期間の種類", "NLG Period Type is required": "NLG 期间类型未必填", "NLG Period Value": "不失效保证期间值", "NLG Period Value is required": "NLG 期间值未必填", "no": "否", "No Claim Bonus": "无理赔奖金", "No Claim Discount": "无理赔折扣", "No component is selected. Please modify.": "", "No Content": "", "No Data": "暂无数据", "No impact on original formula when editing formula copies": "编辑公式/费率表副本不会影响原产品", "No Lapse Guarantee": "不失效保证", "No match result.": "未匹配到结果", "No Valid Data": "无有效数据", "No.": "序号", "No. Claim Times": "理赔次数", "No. of Completed Policy Years for Bonus Vesting on Claim": "理赔可给付红利的保单年度", "No. of Completed Policy Years for CB Becoming Payable": "可开始给付现金红利的保单年度", "No. of Completed Policy Years for Surrender Bonus Allowed": "允许退保奖金的保单年数", "No.of Installment": "分期期数", "Non-Fleet Grade and Accident Index of Japan Motor": "", "Non-Fleet Grade Formula": "", "Nonforfeiture Option Type": "不丧失现金价值选择权类型", "Normal Policy Date Check Rule Compare to Master Policy": "普通保单与团体保单间的日期校验规则", "Normal Policy Expiry Date Rule": "普通保单保险止期规则", "Not Applicable": "", "Not Cascade": "", "Note: The relationship defined here is only applicable when the package is configured as single-main-benefit.If the package is marked as supporting multiple main benefits, this relationship will be ignored and have no effect.": "", "Notice": "", "Notice Date Compare with Due Date": "通知日在到期日的前多少天", "Notice Rule": "通知规则", "Notice Rule cannot be repeated": "通知规则不能重复", "Notification": "", "number": "序号", "Number": "", "Number Item": "", "Number Items": "", "Number of Files/URLs": "文件/链接总数", "Number1": "", "Number2": "", "Object": "标的", "Object Category": "标的物分类", "Object Category & Object": "", "Object Category & Type": "", "Object Category Code": "", "Object Category Name": "", "Object Component": "", "Object Element-Auto-Vehicle": "", "Object Enumerated Values": "标的枚举值", "Object Info": "标的信息", "Object Sub Category": "标的子类型", "Object.etc. Driver": "标的，如：司机", "Obtain Template": "获取模板", "Occupation Risk Category": "职业风险类型", "Occurrence Type（Accommodation)": "发生类型（住宿）", "Occurrence Type（Transportation)": "发生类型（交通）", "Occurrence Type（Transportation）": "发生类型（交通）", "Offset Date": "保费冲销日", "Offset from X Days Compare to Expired Date": "与保单止期相比较", "Offset from X days prior to due date": "到期日前多少天冲销", "Once allowed, the client don't need to pay full amount for total overdue premium bills during reinstatement. Only a minimum premium payment is required to pay all the charges and keep the policy effective. The POS formula needs to be defined under this condition.": "一旦被允许，客户在复效期间不需要全额支付逾期的保费账单。只需要支付最低的保费来支付所有的费用并保持保单的有效性。在这种情况下，需要定义POS公式。", "Once opened, system allow policyholder to rebalance their fund account (TIV) based on specific ratio regularly.": "一旦开通，系统允许保单持有人根据特定比例定期重新平衡其基金账户（TIV）。", "Once opened, system allow user to trigger adanced DCA arrangement. Premium will put into a pre-defined low risk fund first, and then switch out and buy target fund on a regular basis.": "", "Only components with the same stack type can be in one template. Different stack types are detected in your submission. Please check the configuration.": "", "Only display formulas that are directly defined and associated under Package Management (e.g., Tax Setting, Premium Adjustment...)": "", "Only for Commission & Service Fee formula, it is supported to query linked Goods": "只有佣金和服务费公式可以查到关联的商品。", "Only for Commission & Service Fee formula, it is supported to query linked Goods.": "只有佣金和服务费公式可以查到关联的商品。", "Only for Levy and Stamp duty formula, it is supported to query linked package": "只有征税和印花税公式可以查到关联的组合。", "Only one formula allowed for each fund, no duplicated allowed.": "同一基金仅能定义1条公式，不可重复添加", "only one formula type either rate or premium could be configured for one product.": "不能在一个产品内，既用费率公式定义，又有保费公式定义。", "Only one modal factor allowed for each payment frequency, no duplicate allowed. Please check.": "每个给付频率只能定义一个因子，请检查。", "Only one record is allowed for each calculation method. No duplicate allowed.": "每个计算方式只能有一条记录。", "Only perform rebalancing when the variance exceeds": "仅当基金价值与预定值偏差超过如下值时再进行投资再平衡", "operation failure": "操作失败", "Optional": "可选", "Optional / Required": "可选/必选", "Order": "顺序", "Original Currency": "原币种", "Out of No Lapse Guarantee": "在不失效保证期间外", "Overdue Auto Deduction": "不付解除前自动代扣", "Overdue Handling": "逾期处理规则", "Overdue Status": "过期状态", "Package": "", "Package Code": "产品组合代码", "PACKAGE CONFIGURATION": "", "Package Name": "产品组合名称", "PACKAGE RELATED CONFIGURATION": "", "Package V2": "", "packages": "组合", "Paid Up": "减额缴清", "Paid Up SA Formula": "减额缴清保额公式", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "参数已更改，已上传的关系矩阵表将被删除，请确认。", "Parameter or result code have been changed. The uploaded ratetable will be cleared. Please confirm.": "参数或结果代码已更改，已上传的费率表将被删除，请确认。", "Parameters relevant to multiple object can not be used directly in steps without list function.": "多标的相关的因子不能在非list函数的步骤中直接使用。", "Parent Field": "", "Parent Field Name": "", "Partial": "", "Partial withdrawal allowed": "允许部分领取", "Participating Agreement": "分红协议", "Payment & Collection Method": "付费&收费方式", "Payment Defer Period": "延迟给付期间", "Payment Defer Period Type": "延迟给付期间类型", "Payment Frequency": "缴费频率", "Payment Frequency & Modal Factor": "给付频率和系数因子", "Payment Option": "领取方式", "Payment Period": "给付期间", "Payment Period Type": "给付期间类型", "Payment Period Value Type": "给付期间值类型", "Payment/Collection Method & Account": "付费/收费方式 & 账户", "Pealse select Formula Code": "", "Pending Case Enumerated Values": "照会枚举值", "Percentage": "", "Period": "期间", "Period Limit Matrix": "期间限制关系矩阵表", "Period Type": "期间种类", "Period Value": "期间值", "Permium Handling Method Within Premium Holiday": "保费假期内保费处理方式", "Pin": "固定", "Place of Incurred(Allowance)": "发生地点（津贴）", "Place of Incurred(Medical Bill)": "发生地点（医疗费用明细）", "Plan Info": "方案信息", "Planned Premium": "基础保费", "Planned Premium by Layer": "计划保费分层", "Planned Premium Layer Info": "计划保费分层信息", "Please": "请", "Please add at least one formula step": "请至少添加一个公式步骤", "Please Add Premium Notice Rule": "请添加一条收费通知规则", "Please add stack correlation": "请添加累加器关系", "Please add Terminal Bonus formula.": "请添加终了红利公式。", "Please at least select one Option": "请至少选择一个计划", "Please check the required name set first": "请首先勾选需要的姓名组。", "Please check whether you need to modify the following Formula:": "请确认是否需要修改以下公式：", "Please choose at least one type of name connection method & name.": "请至少选择一种拼接方式&姓名。", "Please complete coverage period agreement": "请填写保障期间约定", "Please config Enumeration Value": "请配置枚举值", "Please Configure a Formula for Vehicle Market Value": "请配置车辆实际价值的计算公式", "Please configure at least one of the agreement.": "请至少配置一个协议", "Please configure at least one record.": "请至少配置一条数据。", "Please configure before submit.": "提交前请先配置", "Please configure calculation logic for risk sub-category.": "请配置子风险类别的计算逻辑。", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration -> Premium Notice Reminder\".": "", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration\".": "", "Please configure this agreement, otherwise insured will not be default deactivated after fully claimed of the selected liability.": "请配置此协议，否则完全理赔后此协议也不会失效。", "Please configure this agreement, otherwise liability will not be default terminated after claim.": "如不配置此约定，系统默认配完此责任会终止。", "Please configure transaction type.": "请配置交易类型。", "Please confirm changing the default option from": "请确认覆盖原默认选项：", "Please confirm setting the default option to": "请确认修改默认选项为：", "Please confirm to wipe out all uploaded benefit details and all versions.": "", "Please continue to finish Premium holiday related configuration in the \"ILP Premium Holiday\" Section.": "请在”投连产品保费假期“区块完成剩余保费假期配置。", "Please copy your File URL first.": "请先复制文件链接。", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "请在表格中定义数字数值，不包括百分号。例如，如果您希望定义10%，请在表格中直接输入10。", "Please define the allowed investment strategy.": "请定义允许的投资策略", "Please do not enter a space in result code": "结果代码请不要输入空格", "Please ensure at least one record is entered in Factor Definition or Ratetable Replacement.": "请确保在因子定义或费率表替换中至少添加一条记录。", "Please fill in the mandatory items before submitting.": "请填写必填项后提交.", "Please first define the Object Category and Object under Configuration Center → Tenant Data Configuration → Basic Rule → Object Category & Object.": "", "Please input": "请输入", "Please input at least one field.": "请至少输入一个字段。", "Please input at least three characters.": "请至少输入三个字母！", "Please input condition": "请输入条件", "Please input end number": "请输入终止数值", "Please input expression": "请输入表达式", "Please input in this format: 'HH:mm'. e.g. 12:00": "", "Please input liability handling.": "请录入责任处理", "Please input number": "请输入数字。", "Please input or paste the URL here": "请在此输入或粘贴链接。", "Please input positive value": "请输入正数。", "Please input result": "请输入结果。", "Please input the range in [{{minimum}}, {{maximum}}]": "", "Please input variable": "请输入变量", "Please input variable and start number": "请输入变量和初始值", "Please input Vehicle DataBase Name": "请输入车型库名称。", "Please make sure that both medical bill type and medical bill item have been configured.": "请确保医疗账单类型和医疗账单科目都已配置。", "Please make sure you define how to calculate this middle factor when using in formula configuration.": "请确保您已在公式中定义了此中间因子的计算过程。", "Please note: After premium holiday, cash rider will be terminated from next premium due date (of first unpaid bill), while unit deducting rider (if any) will keep effecitve.": "", "Please note: Current detail information of Recurring Single Top Up will be overwritten by Premium Frequency & Installment Detail of Planned Premium.": "请注意:当前周期性单次追加投资的详细信息将被期缴保费的缴费频率及缴费详情覆盖", "Please note: There is no refund after termination and policy is not allowed to be reinstated.If there are any active riders, they will also be terminated.": "注意：保单终止后没有退款，并且不允许复效。如果有有效的附加险，也会被终止。", "please retain at least two branches": "请至少保持2个分支", "Please save the changes to cancellation reason first.": "", "Please save the changes to cancellation type first.": "", "please select": "请选择", "Please select": "请选择", "Please select at least one advance criteria for ANFO setting.": "默认不丧失现金价值选择权设置区块，请至少选择一个附加高级条件", "Please select at least one related liability.": "请至少选择一条关联责任。", "Please select at least two fields.": "请选址至少2个字段", "Please select effective date": "", "Please select Formula Code": "请选择公式代码", "Please select formula existed in steps": "请选择步骤中存在的公式", "Please select language": "请选择语言", "Please select one record at least!": "请至少选择一条记录！", "Please select one reocrd": "请选择一条记录", "Please select parameter": "", "Please select ratetable existed in steps": "请选择步骤中存在的费率表", "Please select related Formula for POS": "请选择保全/批改对应的公式", "Please select the sections to export:": "", "Please select the sections to import:": "", "Please select to:": "", "Please set the deduction sequence if corresponding premium type is insufficient for charge deduction.": "请设置扣款顺序以防当相应的保费类型不足以进行费用扣除。", "Please submit change before test": "请在测试前先提交保存已修改的内容。", "Please tick the additional condition that should be met before enjoy no lapse guarantee": "请勾选享受不失效保证必须要满足的附加条件", "please upload excel or csv": "请上传excel或csv文件", "Please upload the file in XLSX or XLX format!": "请上传的文件格式为xlsx,xlx的文件!", "Please* configure at least one set of name rules.": "", "Policy Currency": "保单币种", "Policy Effective Without Collection (NB)": "非见费出单（新保）", "Policy Effective Without Collection (Renewal)": "", "Policy Enumerated Values": "保单枚举值", "Policy Holder Info": "投保人信息", "Policy Illustration": "投保利益演示", "Policy Info": "保单信息", "Policy Loan": "保单贷款", "Policy Month": "保单月份", "Policy No.": "保单号", "Policy Type": "保单类型", "Policy will be lapsed after grace period ends.": "宽限期后，保单会失效", "Policy will be terminated after grace period ends.": "宽限期后，保单会终止", "Policyholder Age Comparison": "投保人年龄比较", "Policyholder Elements Relationship Matrix": "投保人关系矩阵表", "Policyholder’s Age Range(NB)": "投保人年龄范围（新保）", "Policyholder’s Age Range(Renewal)": "投保人年龄范围（续保）", "PolicyStatusEnum.POLICY_EFFECT": "生效", "policySuspense（Mock）": "", "Portfolio Rebalancing": "投资组合再平衡", "POS": "保全/批改", "POS additional/refund net premium = (product net premium after POS - product net premium before) * unearned period / coverage period": "", "POS Item": "保全项", "Post-sale Illustration": "保单售后利益演示", "Post-sale Illustration Trigger Point": "保单售后利益演示触发时间点", "Pre-definition": "", "Pre-set Data Setting": "", "Premium Adjustment": "保费调整", "Premium Adjustment Level": "保费调整等级", "Premium Adjustment Type": "保费调整类型", "Premium Agreement": "保费限定", "Premium Calculation Method": "保费计算方式", "Premium Discount": "保费折扣", "Premium Discount Type": "保费折扣类型", "Premium Due Date Rule": "保单基准日规则", "Premium End Date Calculation Rule": "缴费终止日期计算规则", "Premium Frequency": "缴费频率", "Premium Holiday": "保费假期", "Premium Investment Strategy": "保费投资策略", "Premium Limit": "保费限额", "Premium Limit Type": "保费限额类型", "Premium Notice Date Compare with Due Date": "收费通知日与约定缴费日比较", "Premium Notice Rule": "收费通知规则", "Premium Notice Rule cannot be repeated": "不允许存在重复的保费通知规则", "Premium Notice Rule(Legacy)": "", "Premium Period": "缴费期间", "Premium Period & Installment": "缴费频率和续期", "Premium Period Type": "缴费期间约定类型", "Premium Period Value Type": "保费期间值类型", "Premium Period(title)": "缴费期间约定", "Premium Type": "保费类型", "Premium Type Deduction Method": "保费类型扣除方式", "Premium Type Deduction Sequence": "扣费保费类型的顺序", "Premium Unpaid Handling": "未付保费处理方式", "Preview Ratetable": "", "Previous": "", "Pro Rata Calculation Basis": "按比例计算方式", "Product": "产品", "Product & Liability Relationship Matrix": "产品&责任搭配关系", "Product Agreement": "产品协议", "Product Category": "产品分类", "Product Center": "产品中心", "Product Center - Product Management": "", "Product Class": "产品类型", "Product Code": "产品代码", "Product Code already exists": "产品代码已存在", "PRODUCT CONFIGURATION": "", "Product Enumerated Values": "险种枚举值", "Product Info": "险种信息", "Product Label": "险种标签", "Product Label Configuration": "险种标签配置", "Product Liability": "产品理赔责任", "Product Line": "产品线", "Product Management": "险种管理", "Product Name": "产品名称", "Product Overdue Status": "险种逾期状态", "PRODUCT RELATED CONFIGURATION": "", "Product SA": "险种保额", "Product SA Priority": "", "Product SA Setting": "险种保额设置", "Product Status": "产品状态", "Product Template": "险种模板", "Product V2": "", "Product Validation": "产品校验", "products": "险种", "Progression Table": "进度表", "Property Bill Item": "财物清单项", "Property Bill Object": "财物清单标的", "Property Bill Objects": "财物清单标的", "Property Claim Info": "财物理赔信息", "Publish": "发布", "Publish Successfully": "发布成功", "Published": "", "Quick Menu": "快捷菜单", "Range": "", "Rate Formula Code": "费率公式编码", "Rate Range": "费率区间", "Rate Source": "费率来源", "Rate Table": "", "Ratetable": "费率表", "Ratetable Category": "费率表类型", "Ratetable Code": "费率表代码", "RateTable Code": "", "Ratetable Code could not start with \"G_\".": "费率表代码不能以“G_\"开头。", "Ratetable Code should not include \"_ProductCorrelationMatrix、_PeriodLimitMatrix、G_BenefitScheduleMatrix\".": "费率表代码不能包含 \"_ProductCorrelationMatrix、_PeriodLimitMatrix、G_BenefitScheduleMatrix\"。", "Ratetable Description": "", "Ratetable Introduction": "费率表简介", "Ratetable Management": "费率表管理", "Ratetable Name": "费率表名称", "Ratetable Replacement": "费率表替换", "Ratetable Sub Category": "费率表子类型", "ratetables": "", "Re-Define Effective Date Rule": "重定义保险起期规则", "Rebalance Date": "再平衡日期", "Rebalance Frequency": "再平衡频率", "Recalculate SA or Premium": "是否重算保额或保费", "Recalculate SA or Premium Except for Change Reason": "除变更理由外的重算保额或保费", "record": "记录", "record(s) has been uploaded successfully.": "上传成功", "records": "条记录", "Recount Result": "重算结果", "Recurring Single Top Up Detail": "周期性单次追加投资详情", "Reduce cash value to zero": "减少现金价值至0", "Refund Confirmed Premium from": "", "Refund Formula": "退费公式", "Regional Function Management": "", "Regular Top Up Frequency": "定期追加投资的缴费频率", "Regular Top Up Frequency configure at least one": "至少配置一个定期追加投资频率", "Regular Top Up payment needs to use separated frequency": "定期追加投资需要使用不同的缴费频率", "Regular withdrawal allowed": "允许定期领取", "Reinstatement Period": "复效期间", "Reinstatement Period Unit": "复效期间单位", "Related Stacks": "相关的累加器", "Related Stacks within the Same Order": "", "Related Termination": "相关终止", "Related to Bone Fracture/Dislocation Item and Bone Fracture/Dislocation Level": "骨折关键脱位项目及骨折关键脱等级", "Relation Policy Info": "", "Relation Policy Template": "关系单模板", "Relational Type": "", "Relationship": "", "Rename": "重命名", "Renewable or Not": "是否可续保", "Renewal": "续保", "Renewal Agreement": "续保协议", "Renewal Extraction Period": "续保抽档期间", "Renewal Grace Period": "续保宽限期", "Renewal Policy Effective Date Rule": "续保保单生效日规则", "Renewal Policy Effective Without Collection": "非见费出单（续保）", "Renewal Proposal Submit Date": "", "Renewal Reminder Date Compare to Policy Expiry Date": "续保通知日与保单满期日的比较", "Renewal Reminder Rule": "续保提醒规则", "Renewal Reminder Rule cannot be repeated": "续保通知发送规则不允许重复", "Renewal Together": "必须同时续保", "Replacement Ratetable": "新费率表", "Replacement Ratetable is required": "新费率表必填", "Required": "必选", "Required / Optional": "必选/可选", "Reserve Provision trigger point": "", "Reserve Provision Trigger Point": "准备金提取节点", "Reset": "重置", "Result Code": "结果代码", "Result Details": "", "Retirement Age (Insured)": "退休年龄（被保人）", "Retirement Option": "退休计划", "Retirement Option Start Date": "退休计划开始日期", "Reversionary Bonus": "保额红利", "Reversionary Bonus Allowed": "是否有保额红利", "Rider Code": "", "Rider Handling After ETI": "展期后附加险的处理", "Rider Handling After Paid Up": "减额缴清后的附加险处理", "Rider Handling After Triggering Premium Holiday": "进入保费假期后附加险的处理", "Rider Name": "附加险名称", "Rider Name(Rider1)": "附加险名称（附加险1）", "Rider Name(Rider2)": "附加险名称（附加险2）", "Rider Relationship Matrix": "附加险关联关系矩阵表", "Rider Relationship with Main Product": "附加险与主险的关联关系", "Rider Type": "附加险类型", "Risk Category": "风险分类", "Risk SA for Risk Category": "风险类型下的风险保费", "Risk SA for Risk Sub-category": "风险子类型下的封信保额", "Risk Sub-category": "风险子分类", "Room Level": "病房等级", "Round": "", "Rounding Digits": "", "Rounding Type": "", "Rule Category": "规则类型", "Rule Code": "规则代码", "Rule Name": "", "Rule/Rule Set": "", "Rule/Rule Set Code": "规则/规则集代码", "Rule/Rule Set Name": "规则/规则组名称", "Rule1：Corresponding to effective date or period end date": "规则1：保单开始日对应的相同日期、或者期末日", "Rule2：Corresponding to effective date or next date of period end date": "规则2：保单开始日对应的相同日期、或者期末日次日", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "规则3：保单开始日对应的相同日期、或者期末日且2月固定是28日", "rules": "", "SA Agreement": "保额约定", "SA by Layer": "保额分层", "SA Calculation Method": "保额计算方式", "SA Decreasing Order": "减保顺序", "SA Limit": "保额限制", "SA Limit Type": "保额限制类型", "SA Multiplier": "保额系数", "SA Unit": "保额单位", "SA Unit Content": "单位保额", "SA Valuation": "", "SA Valuations": "", "Same Premium Adjustment strategy for All Goods": "所有商品使用相同的保费调整策略", "Same TB type Formula has duplicate record. Please check.": "同类型的终了红利公式存在重复记录，请检查。", "Save": "保存", "Save as Draft": "", "Save Illustration Sequence": "保存利益演示顺序", "Save Sort": "保存排序", "Save successfully": "保存成功", "Save Successfully": "保存成功", "Saved successfully": "保存成功", "Scenario Code": "场景代码", "Scenario Code & Scenario Name": "场景代码和场景名称", "Scenario code can not be duplicated": "不能重复使用相同的场景代码", "Scenario Name": "场景名称", "Scheduled Arrival Time": "计划到达时间", "Scheduled Departure Time": "计划出发时间", "Search": "查询", "Search by Policy": "根据保单查询", "Search by Transportation": "基于交通出行信息搜索", "Search Result": "查询结果", "Second Date": "第二个日期", "Select": "选择", "Select All": "", "Select Benefit Factors": "", "Select Cancellation Type": "", "Select Claim Stack(Template)": "选择理赔累加器（模板）", "Select Component": "", "Select Factor": "", "Select Formula": "选择公式", "Select Liability": "选择责任", "Select Parameter": "选择因子", "Select Ratetable": "", "Select the enumeration value you want.": "选择你想要的枚举值", "Select the Format you want": "请选择需要的格式", "Selection": "", "Sequence": "序列", "Serial No.": "", "Service Fee Clawback": "服务费召回", "Set": "组", "Set Default Time": "", "Set Fixed Time": "", "Set Name": "组名称", "Set1": "姓名组1", "Set2": "姓名组2", "Set3": "姓名组3", "Severity": "严重程度", "Severity Definition": "严重程度定义", "Sex": "", "Short Term Premium": "短期单保费", "Short Term Premium Calculation Method": "短期单保费计算方法", "Single Top Up Type": "单次追加投资类型", "Source": "来源", "Specify the calculation order": "", "Stack Code": "累加器编号", "Stack Component Name": "", "Stack Name": "累加器名字", "Stack Template Code": "", "Stack Template Name": "", "Stack Type": "累加器类型", "Stack Unit": "单位", "Stack Unit Type": "", "Stack Value": "值", "Stack Value Type": "值类型", "Stacks Related to Special Agreements": "", "Standard Premium": "标准保费", "Start date": "开始日期", "Start Date": "开始如期", "Starting Scenario": "开始场景", "Status": "状态", "Step": "步骤", "Step 1": "步骤一", "Step 1 Download Test Data Template": "步骤一：下载测试数据模板", "Step 1: Input Value for Factors in the Page": "步骤一： 请在本页面输入因子值", "Step 2": "步骤二", "Step 2 Upload Test Data File": "步骤二：上传测试数据文件", "Step 2: Input Value for Factors in the Excel": "步骤二：请在Excel里输入因子值", "Step 3 Download Test Result": "步骤三：下载测试结果", "Step 3: Testing Result": "步骤三：得到测试结果", "Step result is duplicated with paramater, please modify.As follows": "步骤结果与参数重复，请修改如下", "Strategy": "策略", "Strategy Code": "策略代码", "Strategy Name": "策略名称", "Sub Formula": "子公式", "Sub POS Effective Date": "子保全/批改生效日期", "Submit": "提交", "Submit Product": "产品提交", "Submit successfully": "提交成功", "Submit Successfully": "提交成功", "Summary": "", "Support entering a regular expression to control the input rules for Account Number by each Bank. For example, if Account Number should only allow 10 to 12 digits(number), you can configure it as ^\\d{10,12}$ or ^[0-9]{10,12}$.": "", "support list calculation including +,-,*,/": "列表计算支持  +,-,*,/", "Surgery": "手术", "Surgery Configuration Method": "手术配置方式", "Surgery Customized Data Details": "", "Surgery Level": "手术等级", "Surgery Level cannot be empty": "手术等级不能为空", "Surgery Tag1 cannot be empty": "", "Surgery Tag2 cannot be empty": "", "Survival Benefit": "生存金", "Survival Benefit Leading Days": "生存金提前生成天数", "Switching the method of diagnosis configuration will auto clear the configured data.": "更改疾病配置会自动清空现有的配置数据。", "Switching the method of surgery configuration will auto clear the configured surgery data.": "切换手术配置方式会自动清除已配置的手术明细数据。", "System Data": "", "System Data Value": "", "System error": "系统错误", "System is abnormal, please try again later.": "系统异常，请稍后再试。", "System pre-set data": "系统预置数据", "System support to configure linkage between following fields": "系统支持配置以下字段间的级联关系", "System will automatically conduct batch test when test data is uploaded successfully.": "测试数据上传成功后，系统自动进行批量测试。", "System will generate a test data template automatically based on parameters used in the formula.": "系统将根据公式中使用的参数自动生成测试数据模板。", "tables": "", "Tag Definition": "标签定义", "Tag definition cannot be empty": "", "Tag1": "标签1", "Tag2": "标签2", "Target Currency": "目标汇率", "Target Date": "目标日期", "Tax Collection Rule": "税费缴费规则", "Tax Rate": "税率", "Tax Setting": "税设定", "Tax Type": "税种", "Tax Value Type": "税计算类型", "TB Payable for Claim from": "因理赔应付终期红利开始于", "TB Payable for Surrender from": "因退保应付终期红利开始于", "TB Type": "终了红利类型", "Template change will influence object and liability setup.": "模板的变更会影响标的和责任的设定。", "Template Code": "", "Template Code / Name": "", "Template Content": "", "Template Correlation Info": "模板关联信息", "Template Description": "模板描述", "Template Name": "模板名称", "Template Status": "", "tenant": "租户", "Tenant Data Configuration": "租户数据配置", "Terminal Bonus": "终期红利", "Terminal Bonus Indicator": "终了红利指标", "Terminate Liability After Claim Waiver": "赔完终止豁免责任", "Terminate Policy After Terminate Liability": "责任终止后终止保单", "Terminate Product After Terminate Liability": "终止责任后终止关联产品", "Terminate Related Liability": "终止相关责任", "Terminated Reason": "终止理由", "Test": "测试", "Test Result": "测似乎结果", "The “DCA frequency” table should at least have one record.": "DCA频率表至少含有一条数据。", "The automatic fund rebalancing will only occur when the portfolio variance from the pre-specified premium allocationt exceeds certain range.": "当投资组合与预先指定的保费分配的偏差超过某个范围时，系统才会自动执行基金再平衡。", "The code could only contain letters, numbers, and underscores (_).": "", "The component code could only contain letters, numbers, and underscores (_).": "", "The content of the page has been modified, are you sure you want to leave": "修改的内容还未保存，请确认是否要离开。", "The content of the page has been modified, are you sure you want to leave without saving?": "", "The coverage period agreement will be deleted": "设置的保险期间约定将被删除", "The current configuration cannot be modified after submission, confirm to continue?": "点击提交后不能修改当前配置，确认继续？", "The fixed due date refers to the same day of each month. If certain month doesn't have the date, it will be the last day of that month.": "指定每个月的某一天为固定到期日。如果某个月没有这一天，则指定这个月的月末日。", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "指定每个月的某一天为固定到期日。如果某个月没有这一天，则指定上月月末日。", "The following benefit option(s) have been configured. The uploaded template will overwrite the data. Please confirm to proceed.": "", "The following calculation factors are not found in structure.": "以下计算因子无法在因子树内找到。", "The following products have used this formula. Editing formula and the following product will be affected. Please check before change:": "", "The formula code could only contain letters, numbers, and underscores (_).": "公式code仅包括字母，数字，下划线。", "The General category can only be referenced in formula definitions and is not limited to any specific formula category.": "", "The indicator of include or exclude is mandatory for diagnosis code configuration.": "包含/除外相关的标识，对于疾病配置是必须的。", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "系统会基于自然月进行计算。例：11/16 00:00:00~12/15 23:59:59；12/16 00:00:00~1/15 23:59:59", "The name format will follow the sequence below, the sequence can be manully adjusted.": "名称格式将遵循以下的顺序，顺序可以任意调整。", "The object configured in {{liabilityId}} {{liabilityName}} does not exist in the product.": "", "The operator used for exponentiation. It raises a number to the power of another number. For example: 2*4 = 2*2*2*2 = 16": "", "The policy of GroupEB does not support the process of deactivating the insured": "", "The product code could only contain letters, numbers, and underscores (_).": "产品code仅可以包含字母，数字，下划线", "The ratetable code could only contain letters, numbers, and underscores (_).": "费率表代码只能包含字母、数字和下划线（_）。", "The rateTable is used and affected in several places below. Please confirm changes.": "", "The result of formula is repeated naming(case insensitive)": "公式的结果是重复命名（不区分大小写）", "The second value should be greater than or equal to the first value.": "第二个值应大于或等于第一个值。", "The second value should greater than the first value": "第二个值必须大于第一个值", "The selected formula has been updated. Please reselect.": "", "The selected rate table has been updated. Please reselect.": "", "The stack code could only contain letters, numbers, and underscores (_).": "累加器代码只能包含字母、数字和下划线（_）。", "The table contains an extensive amount of data and cannot be previewed here. Please use the download button to obtain and review the file locally": "", "The template code could only contain letters, numbers, and underscores (_).": "", "The template has been referenced and cannot be deleted.": "该模板已被引用，不能删除。", "The template has been referenced and cannot be edited.": "该模板已被引用，不能编辑。", "The template has been used by following products. Please confirm if any deletion is required.": "该模板已被以下产品使用，确认是否需要删除？\n{{productCodes}}", "The template has been used by following products. Please confirm if any modification is required.": "该模板已被以下产品使用，确认是否需要修改？\n{{productCodes}}", "The Usage Premium Start Date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "指定每个月的某一天为按使用量计算保费的起始日。如果某个月没有这一天，则指定为这个月的末日。", "The value of the last step must be 'final', and the value of other steps cannot be 'final', 'final' is case insensitive": "最后一步的值必须为“final”，其他步骤的值不能为“final”，“final”不区分大小写", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "", "There no found.": "", "Third Party Agreement": "第三方协议", "This agreement is replaced by \"Claim Eligibility and Policy Matching\". Please use the new one if needed.": "此配置项已被“ 理赔资格与匹单”模块替代。如有需要，请使用新的配置项。", "This component has already been used and cannot be modified or deleted.": "该组件已被使用，无法修改或删除。", "This conditional factor's value already exists, please modify it.": "", "This configuration can only be configured once, and cannot be modified after the configuration is completed.": "此配置项只允许配置一次，配置提交后不可修改。", "This formula is referenced as a sub-formula in below. Please update the following parent formulas after saving this formula.": "", "This Illustration Scenario is already in use under a Benefit Illustration Item. Please disassociate it from the Benefit Illustration Item first.": "此演示场景已在演示项中使用。请先删除关联的演示项。", "This insurable interest has been used in the following package: [{{packages}}], please delete insurable interest in Package first.": "此可保利益已被组合 [{{packages}}] 使用，请先在上述组合中删除此可保利益。", "This liability has been used in the following package: [{{packages}}], please delete liability in Package first.": "此责任已被组合 [{{packages}}] 使用，请先在上述组合中删除此责任。", "This product is read only.": "", "This ratetable is relied on": "", "This record has been deleted from premium period configuration. Please manually delete this record to ensure data consistency.": "此记录已从保费期间配置中删除，请手动删除这条记录已保证数据的一致性。", "This record was configured based on old version. Currently, viewing and editing are not supported.": "此配置数据版本过旧，暂不支持查看跟编辑。", "This rider product has been used in the following package: [{{packages}}], please delete rider product in Package first.": "此附加险已被组合 [{{packages}}] 使用，请先在上述组合中删除此附加险。", "This section configures the system rules when installment premium is not paid on time.": "本区块用于配置续期保费未及时缴纳时系统的处理规则。", "This section configures the system rules when the total investment value (policy value) is insufficent to pay charges.": "本区块用于配置保单账户余额（保单价值）不足以支付保单相关费用时系统的处理规则。", "This section is required for regular paid product. No need for single premium product.": "本区块配置仅针对于期交保单。对于趸交保单无需配置。", "Time Zone": "时区", "Times Type": "次数类型", "Tip1： Support calculation sign including": "提示1：支持使用以下计算符号", "Tip2： Support embedded calculation method including": "提示2：支持使用以下计算方法", "Tip3： Date Format only accept": "提示3：日期格式仅支持", "Titile": "", "Tiv（Mock）": "", "to": "到", "To define the premium allocation strategy.": "定义保费分配策略。", "To define whether claim reserve equals 0 is allowed for claim acceptance.": "配置是否允许理赔准备金为0", "To reduce duplicate configurations, for POS Items applying to the entire policy, please go to [Package Management - Policy Change] section.": "", "Top Up Frequency": "追加投资的频率", "Trace": "轨迹", "Transaction Type": "交易类型", "Transportation Data": "交通出行数据", "Transportation Date": "交通出行日期", "Transportation Facility": "交通工具", "Transportation No.": "交通出行流水号", "Transportation Number": "交通出行流水号", "Transportation Status": "交通出行状态", "Transportation Type": "交通出行方式", "Treatment Condition": "治疗情况", "Trigger Additional Maturity Benefit": "触发额外的满期金", "Trigger Cash Refund": "触发现金退款", "Trigger Type": "触发类型", "Try a different keyword.": "", "Type": "类型", "Type of Amount Per Period": "", "Type of Loss": "", "Uncollected Premium Deduction": "扣除未缴保费", "Under this scenario, the policy Lapse process will be trigger when TIV is un-sufficient. Please make related configuration at \"Handling of TIV Insufficient\" section.": "在当前配置下，保单将会在保单投资账户余额不足时触发中止。请在”投资账户余额不足处理”区块完成剩余配置。", "Under this scenario, the policy will keep effective when enter premium holiday. And once the allowed premium holiday period is used up, system will lapse policy when premium is unpaid.": "在当前配置下，保单在进入保费假期内时将保持有效。当保费假期结束后，依旧未缴纳续期保费，系统将中止保单。", "Underwriting Enumerated Values": "核保枚举值", "Unit Type": "", "Unpaid Bill Cancellation Rule": "上传账单取消规则", "Upload check failed, please download the error file to check!": "上传校验失败，请下载失败文件检查！", "Upload Data": "", "Upload Failed": "上传失败", "Upload File": "", "Upload File Infomation": "文件上传确认", "Upload Files": "", "Upload Ratetable": "上传费率表", "Upload Success": "上传成功", "Upload successfully": "上传成功", "Upload Successfully": "上传成功", "Uploading, please refresh the page to check the results later.": "上传中，请稍后刷新当前页面确认结果。", "URL": "链接", "Usage Based": "", "Usage Based Insurance Agreement": "基于使用的保险约定", "Usage Based Product": "基于使用的产品", "Usage Premium Start Date": "使用保费开始日", "Use customized data": "用户自定义数据", "Using Insurable Interest": "", "UW Risk SA Setting": "核保风险保额配置", "Validation Details": "校验明细", "value": "", "Value": "值", "Value Assignment": "", "Value Type": "值类型", "Vehicle Claim Info": "车辆理赔信息", "Vehicle Database Model": "车型库模式", "Vehicle Database Model needs to be configured as “Cascade” if there exists multiple vehicle databases.": "如果存在多套车型库，则车型库模式需要配置成“级联”。", "Vehicle Database Name": "车型库名称", "Vehicle Database Name has already existed.": "存在相同的车型库名称。", "Vehicle Databases": "车型库", "Vehicle Elements Relationship Matrix": "车辆要素关联关系表", "Vehicle Market Value": "车辆实际价值", "Version": "版本定义", "Version Time": "", "Vesting Agreement": "归属协议", "View": "查看", "View Claim Stack Template": "", "View Component": "", "View Detail": "", "View Enumerated Fields Dependency": "", "View Formula": "公式查看", "View Liability": "查看责任", "View My Creations": "", "View Ratetable": "查看费率表", "View Reference": "", "View Structure": "", "VIRTUAL MAIN": "", "Virtual Main Benefit": "虚拟主险", "Wait Until Grace Period Expires": "直到宽限期结束", "Waiting Period": "等待期", "Waiting Period of New Business": "", "Waiting Period of Reinstatement": "", "Warning Notification": "", "Week": "周", "When add a record for Liability A and Liability B as 1,000": "已添加了一条记录为：责任A和责任B，保额为1,000", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month)": "当产品提供免费费用月份时，它可能不会从保单生效日开始。免费费用月份将从此处定义的X保单月份开始计算。（至少从第一个保单月份开始）", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month) ": "", "When customer choose pre-defined investment strategy, the fund appointment rate for both premium direction and portfolio rebalance will follow the configuration on strategy.": "当顾客选择预先定义的投资策略，保费和投资组合再平衡的基金分配率都将遵循策略的配置。", "When issue a policy, save illustration data in the policy.": "当保单签发时，将计算的利益演示结果数据作为附件保存在保单中。", "When it is closed,  system will buy target fund chosen by customer directly after policy issuance. No delay of the investment.": "当开关关闭时，系统将在保单出单后立即按照客户选择的基金分配比率进行投资。不会延迟投资。", "When premium comes in, system will fill in the installment premium due": "当收到保费后，系统将以如下方式分配至每期保费", "Whether After Campaign Discount": "是否在营销折扣后计算", "Whether to use premium adjustment": "是否使用保费调整", "With Extra Premium": "有额外保费的产品", "With Guarantee Period": "带有保证期限", "With No Lapse Guarantee": "有不失效保证", "Within No Lapse Guarantee": "在不失效保证期间内", "Work Time": "", "Year": "年", "Year(s)": "年", "yes": "是", "Yes，Charge Amount is": "是，费用是", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "您只能上传以下格式的文件：PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG", "You can only upload xls, xlsx": "只允许上传xls, xlsx", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "只允许上传xls, xlsx, doc, docx, pdf, jpg 或 png 格式的文件.", "You can only upload XSL": "", "You don't have to configure it if you don't need it": "请不要配置此项，如果您不需要它", "You have unsubmitted information. Do you want to discard it?": "还有尚未提交的信息，你确认删除吗？", "You haven,t configured your bank information yet": ""}