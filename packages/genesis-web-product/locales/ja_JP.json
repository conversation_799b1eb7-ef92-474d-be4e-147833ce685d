{"- -": "--", "--": "--", "'Charges Deducted From Policy Value' includes the following types of charges: Policy Admin Fee, Account Management Fee，ETF Broker Fee, ETF Management Fee, Premium Holiday Charge, Cost of Insurance, Rider Premium, Partial Surrender Charge": "「契約口座残高から差し引かれる料金」には、以下の種類の料金が含まれます：保険契約管理料、アカウント管理料、ETFブローカージェーム、ETF管理料、保険料支払い一時停止費用、補償費用、特約保険料、部分的な解約費用。", "{{filename}} will be uploaded, the data is {{count}} records, please make sure to upload.": "{{filename}} はアップロードされます。{{count}} 件のデータがありますので、ご確認ください。", "%": "", "• Accumulate each liability SA -> Product SA is 2,000": "•各補償責任金額を累積させ -> 商品保証金は2,000です。", "• Accumulate federated liability SA -> Product SA is 1,000": "•連帯補償金額を累積させ -> 商品保証金は2,000です。", "Abbreviation": "", "Accelerating/Restoration": "Accelerating/Restoration", "Accident Index Formula": "", "Account Lockout Period": "アカウントのロックアウト期間", "Account Lockout Period (minimum value is 15 min)": "アカウントのロックアウト期間 (最小値は15分です)", "Accumulated Fund Dividend (Mock)": "累積ファンド配当金（モック）", "Accumulated ILPBonus (Mock)": "累積ILPボーナス（モック）", "Accumulated ILPCharges (Mock)": "累積ILPチャージ（モック）", "Accumulated POS Withdrawal (Mock)": "", "Accumulated Premium (Mock)": "蓄積純保険料", "Accumulated Withdrawal (Mock)": "累積引き出し（モック）", "Actual Arrival Time": "実際の到着時間", "Actual Departure Time": "実際の出発時間", "Add": "追加", "Add a Condition": "条件を追加", "Add a set of conditions": "他の条件を追加する", "Add Amount": "", "Add Component": "", "Add Condition": "", "Add Enumerated Fields Dependency": "", "Add Factor": "ファクターの追加", "Add Formula": "計算式を追加する", "Add New": "追加", "Add New as the Sub Level": "サブレベルを追加する", "Add New Charge": "控除を追加する", "Add New Claim Stack Template": "クレームスタックテンプレートの追加", "Add New Condition": "条件の追加", "Add New Formula": "計算式追加", "Add New Liability": "補償内容を新規追加", "Add New Product": "新保険商品登録", "Add New Ratetable": "保険料率追加", "Add New Risk Sub Category": "リスクサブカテゴリーの追加", "Add New Set": "セットの追加", "Add New Version": "", "Add Ratetable": "レートテーブルの追加", "Add Strategy": "Add Strategy", "Add successfully": "追加完了", "Add Transportation Data": "交通データの追加", "Additional Clause": "その他特約", "Additional Clause Name": "特約名称", "Additional Condition Factor": "", "Additional Condition Type": "条件タイプの追加", "Additional Grace Period": "保険料払込猶予期間の追加", "Additional MB Formula": "追加年金の計算式", "Additional Participation Conditions": "追加参加条件", "Additional Trigger Conditions": "トリガー条件の追加", "Addresses at the selected level and below can be selected in the drop-down box and support free editing.": "", "Adjust to 1st if Effective Day Not in Expiry Month": "", "Adjusted Market Value Floating Ratio": "車両取引価格の変動比率", "Advanced Configuration": "詳細設定", "After": "のち", "After (days)": "", "After ETI": "", "After Paid Up": "", "After(Days)": "後（日）", "Age": "歳", "Age Calculation": "年齢計算", "Age Calculation Basis": "年齢計算ルール", "Age Validation": "年齢チェック", "Age Validation Type": "バリデーションタイプの追加", "ageCalcBasis": "", "Agent Info": "募集人情報", "All Creations": "", "Allow Auto ETI": "延期フラグ", "Allow Auto ETI Tooltip": "延長を許可すると、システムは猶予期間の終了後に延長プロセスをトリガーします。商品で他の不可没収オプションも許可されている場合は、「デフォルト不可没収オプション」でデフォルトを設定してください。", "Allow Auto Paid Up": "自動払済を許可する", "Allow Auto Paid Up Tooltip": "自動払済を許可すると、システムは猶予期間の終了後に自動払済のプロセスをトリガーします。他の不可没収オプションを許可されたい場合は、「デフォル不可没収オプション」で設定してください。", "Allow Automatic Premium Loan": "自動契約貸付の許可", "Allow claim reserve equal 0": "保険金請求準備金が0になってもよろしい", "Allow Claim Reserve Equal 0": "", "Allow collect outstanding premium after policy termination": "契約終了後未収保険料徴収可否", "Allow CPF Payment": "CPF支払いの許可", "Allow Flexible Premium Payment": "柔軟な保険料払込可", "Allow Minimum Investment Period": "最小投資期間を許可しますか？", "Allow Minimum Reinstatement?": "保険料滞納復活許可?", "Allow Parital APL": "部分自動契約貸付の許可", "Allow Partial Filling the ILP Premium": "ILP商品保険料の一部支払い", "Allow Policy Loan": "契約者貸付制度可否", "Allow POS Effective Without Collection": "決済せず異動有効", "Allow Premium Decrease": "保険料の減額を許可する", "Allow Premium Increase": "保険料の増加を許可する", "Allow Renewal": "", "Allow SA Decrease": "保険金額減額", "Allow SA Increase": "保険金額増額", "Allow to apply waive the premium liability": "保険料免除許可", "allow to change premium": "保険料の変更を許可", "allow to change SA": "保険金額の変更を許可", "Allow to Waive Interest": "利息の免除を許可する", "allow to withdraw CV": "現金減額を許可", "Allow Uncollected Premium Deduction from POS Refund": "異動返戻金から保険料の相殺をしますか", "Allow Vesting": "ベスティングを許可", "Allowance Agreement": "手当約定", "Allowance Amount": "手当額", "Allowance Amount Type": "手当額のタイプ", "Allowance Configuration": "", "Allowance Type": "手当タイプ", "Allowance Unit Type": "手当ユニットタイプ", "Allowance Unit Value": "手当ユニット値", "Allowance Value Type": "手当値のタイプ", "Allowed Investment Strategy": "可能な投資戦略", "Allowed Range for Admission Time Compare to Incident Date (Hospitalization)": "入院時間の許可範囲は、事故日と比較します（入院）", "Allowed Range for Admission Time to Incident Date (Medical Bill)": "入院時間の許可範囲は、事故日と比較します（医療費）", "Allowed Range for Death Date Compare to Incident Date": "死亡日付の許容範囲は、事故日と比較します（入院）", "Allowed Range for End Date Compare to Incident Date(Allowance)": "給付終了日付の許容範囲は、事故日と比較します", "Allowed Range for Outpatient Date to Incident Date (Medical Bill)": "外来日付の許容範囲は、事故日と比較します (医療費)", "Allowed Range for Start Date Compare to Incident Date(Allowance)": "給付開始日付の許容範囲は、事故日と比較します", "Allowed Vesting Age": "許可されるベスティング年齢", "Amount Per Period": "1回あたりの金額", "Amount Per Time": "毎回の金額", "Amount Range": "金額範囲", "AND": "と", "Annual Interest Rate": "年利", "Annuity": "年金", "Annuity Leading Days": "N日前に年金を作成する", "Any modification of the current work shifts will impact the work schedules used, please check": "", "Applicable Business Type": "適用可能なビジネスタイプ", "Applicable Goods": "該当商品", "Applicable only to deductible and co-pay types of stacks": "控除金額および自己負担額のあるクレームスタックにのみ適用されます。", "Applicable Target": "申請対象", "Applied Goods": "", "Applied Unit": "応用単位", "Approve": "承認", "Are you sure delete this record?": "", "Are you sure to clear all records?": "すべての記録をクリアしてもよろしいですか？", "Are you sure to clear this record?": "", "Are you sure to clear this section?": "この部分の内容をクリアしてもよろしいでしょうか", "Are you sure to delete this record?": "該当記録を削除してよろしいでしょうか。", "Are you sure to delete this template?": "", "Are you sure to delete this version?": "", "Are you sure to enable this feature? Once enabled, it cannot be turned off. Please confirm to proceed.": "", "Are you sure to export all configurations?": "", "Are you sure to import all configurations?": "", "Arrival Airport": "到着空港", "Arrival City/Place": "到着都市/場所", "Attach to Policy": "契約に添付します。", "Auto Deduction Date": "自動引き落とし日", "Auto Deduction Date Compare to Due Date": "自動引き落とし日と応当日の比較", "Auto Deduction Days Before Due Date": "応当日前の自動控除日数", "Auto Repay APL/PL": "自動契約貸付または契約貸付を自動返済", "Auto Termination Rule": "自動終了ルール", "Auto Trigger Premium Holiday": "保険料払込一時停止自動トリガー", "Automatic Adjustment": "", "Available DCA Frequency": "選択できるドルコスト平均法頻度", "Available Option": "利用可能なオプション", "Back": "戻る", "Back to Search": "検索に戻る", "Bank": "銀行", "Bank Branch Address": "銀行支店所在地", "Bank City": "銀行所在地", "Basic": "", "Basic Component": "基本コンポーネント", "Basic Information": "", "Batch Test": "バッチテスト", "Before (days)": "", "Before(Days)": "前（日）", "Benefit Calculation": "受益計算", "Benefit Illustration": "利益のデモンストレーション", "Benefit Illustration Configuration": "利益のデモンストレーション設定", "Benefit Illustration Test": "利益デモンストレーションのテスト", "Benefit Option": "利益オプション", "Benefit Option  Name": "利益オプション名称", "Benefit Option Code": "利益オプションコード", "Benefit Option Details": "", "Benefit Option Name": "", "Benefit Options cannot be repeated.": "利益オプションは重複できません。", "Benefit Payment": "受益支払", "Benefit Schedule & Rate": "受益スケジュール&受益率", "Bill Conditions": "請求書条件", "Bill Item": "帳票明細", "Bill Item Sort": "", "Bill Object": "帳票対象", "Birthday": "生年月日", "Bonus Allocation Date": "配当分配日", "Bonus Code": "配当金コード", "Bonus Date": "配当分配日", "Bonus Formula": "配当金の計算式", "Bonus Frequency": "ボーナス頻度", "Bonus Handling After ETI": "延長後の配当金処理", "Bonus Handling After Paid Up": "払済後配当金の処理", "Bonus Name": "配当金名", "Bonus Option": "配当オプション", "Bonus Period": "配当金分配期間", "Bonus Period Factor": "配当金分配期間パラメータ", "Bonus Period(In Years)": "ボーナス期間", "Bonus Rate Declaration": "配当レートテーブル告知", "Bonus Rate Declaration List": "配当レートテーブル告知", "Bonus Rate Type": "配当レートタイプ", "Bonus Start from": "配当開始日", "Bonus Type": "ボーナスタイプ", "Bonus/Malus Table": "ボーナス/マルステーブル", "bonusRateType": "ボーナス率タイプ", "Both: Compliance decision can be provided by user or external triggered, system will take the previous one between the two values.": "", "browse": "ブラウズ", "Browse": "閲覧", "Bundle Rules": "", "Business": "", "Business Component": "ビジネスコンポーネント", "Business Date": "ビジネス日付", "Business Element Group": "", "Business Interruption Loss Configuration": "", "Business Scenario": "", "Business Time": "ビジネスタイム", "Business Transaction": "ビジネス取引s", "Business Type": "ビジネスタイプ", "Buy/Sell Unit Price": "ファンド口数価格の購入/売出", "By": "担当者", "Caculation": "計算", "Calculate from Standard Premium": "", "Calculate Product SA by Liability SA": "商品の保険金額は、賠償金額に基づいて計算されます。", "Calculate Success": "計算成功", "Calculation": "計算", "Calculation Accuracy": "計算結果の精度", "Calculation Accuracy List": "計算結果の精度リスト", "Calculation Basis": "計算基準", "Calculation Basis Of Max Insured’s Entry Age": "被保険者の最大補償期間の計算規則", "Calculation Basis Of Min Insured’s Entry Age": "被保険者最低加入年齢の計算方法", "Calculation Details": "計算詳細", "Calculation Direction": "計算説明", "Calculation Frequency": "計算頻度", "Calculation Level": "計算レベル", "Calculation Management": "計算管理", "Calculation Method": "計算方法", "Calculation Order": "計算順位", "Calculation Priority Order": "計算優先順位", "Calculation Type": "計算タイプ", "Calendar Date (per year)": "カレンダー日付（年間）", "Can,t find stack": "スタックが見つかりません。", "Cancel": "キャンセル", "Cancel pin": "ピンのキャンセル", "Cancellation Reason": "", "Cancellation Sub Reason": "", "Cancellation Type": "キャンセルタイプ", "Cancellation Type & Cancellation Reason": "", "Cancellation Type Reason": "", "Cascade": "カスケード接続", "Cash Bonus": "現金配当", "Cash Bonus Allowed": "現金配当有無", "Cash Refund Formula": "現金返戻金計算式", "Cash Value Calculation Level": "現金価値計算レベル", "cashValueTypes": "現金価値タイプ", "Change installment premium calculation method will clear the existing configuration, please confirm.": "分割払保険料計算方法を変更しますと、既存の設定がクリアされますので、ご確認ください。", "Change the switch will clear the current configured data, are you sure to do it?": "", "Changes have not been saved.": "変更した内容は保存されていません。", "Changes made after submission will not be revoked": "", "Changing vehicle database model or fields will clear the existing configuration, please confirm.": "車両データベースのモデルまたはフィールドを変更すると、既存の設定内容がクリアされますので、ご確認ください。", "Channel Enumerated Values": "チャネル選択肢", "Channel Info": "チャネル情報", "Charge": "控除", "Charge Amount After": "です", "Charge Code": "費用コード", "Charge code is duplicated, please check.": "控除コードは重複しました、確認してください", "Charge Code should start with ILPC and followed by 3 digits": "チャージコードはILPCから3桁数字が必要です。", "Charge For Investment Product": "投資型商品料金", "Charge Formula": "保険料計算式", "Charge Free Month": "無料月", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value.": "無料月は存在します。保険料控除は、少なくとも保険月の1日から利用可能でなければなりません。「無料開始」の値を入力してください", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value. ": "無料月は存在します。保険料控除は、少なくとも保険月の1日から利用可能でなければなりません。「無料開始」の値を入力してください", "Charge Free Period": "無料期間", "Charge Free Start From": "無料期間の開始日", "Charge Handling": "费用处理", "Charge Period Factor": "払込期間因子", "Charge Type": "払込タイプ", "Checking whether there are any products using this template...": "", "Child Field": "", "Child Field Name": "", "Cited Times": "被引用回数", "Claim Agreement": "保険金請求契約", "Claim Bill Item Setting": "保険金項目設定", "Claim Calculation": "支払金額の算出方法", "Claim Clawback": "損害賠償請求", "Claim Compensation Amount": "支払金額", "Claim Eligibility and Policy Matching": "保険金請求対象と契約のマッチング", "Claim Incident Type": "事故種類", "Claim Limit": "保険金請求の限度額", "Claim Reserve Setting": "保険金備金設定", "Claim Scenario": "保険金請求のシナリオ", "Claim Section": "請求セクション", "Claim Stack": "クレームスタック", "Claim Stack Correlation": "クレームスタックの紐づけ", "Claim Stack Correlation Info": "クレームスタックの紐づけ情報", "Claim Stack Definition": "クレームスタックの定義", "Claim Stack Info": "クレームスタックの情報", "Claim Stack Management": "クレームスタック管理", "Claim Stack Query": "クレームスタックの検索", "Claim Stack Template": "クレームスタックテンプレート", "Claim Stack Template Management": "クレームスタックテンプレート管理", "Claim Status": "保険金請求ステータス", "Claim Type": "保険金請求タイプ", "Claim type of the selected liability is not supported, please Confirm.": "選択された補償内容の保険金請求タイプはまだ使えません、ご確認ください。", "Claimable Loss Party": "請求可能な対象", "Claimable Loss Party Type": "損害賠償請求可能な当事者タイプ", "claimHistoryTimes（Mock）": "保険金請求履歴（モック）", "Claims Ratio": "損害率", "claimStatus": "保険金請求状態", "Clear": "クリア", "Clear All": "すべてクリア", "Clear Factor": "", "Clear successfully": "クリアしました。", "clear the all datas will delete this version, are you sure to delete all?": "", "Clear the diagnosis configuration": "診断の設定をクリアする", "Clear the surgery configuration": "手術関連の設定をクリアする", "Clear the surgery tag1 configuration": "", "Clear the surgery tag2 configuration": "", "Click Edit on the right to configure multiple-language for the name": "名前を多言語で設定するには、右の「編集」をクリックしてください。", "Click here to upload": "アップロードするにはここをクリックしてください。", "Click or drag the file here to upload": "クリックまたはファイルをこのエリアにドラックしてアップロードします", "Click to the right of the title to edit the name format value": "タイトルの右側をクリックし、氏名フォーマットをを編集することができます。", "Close": "閉じる", "code": "", "Code": "スタックコード", "Collect": "集金", "Collect Extra Installment Premium?": "追加の分割払保険料を請求しますか？", "Collect Overdue Premium Interest": "保険料の未払い利息の徴収", "Collect the entire premium when a claim is made": "", "Collection Payment Info": "入出金情報", "Combination Relationship": "Combination Relationship", "Combined liability cash value calculation could not contain optional liability.": "賠償責任現金価値計算にはオプション賠償責任を含めることはできません。", "Commission Clawback": "手数料払い戻し", "Common Enumerated Values": "共通の列挙値", "Comparison Type": "", "Compensation Bill Item": "補償項目", "Compensation Claim Info": "補償請求情報", "Compliance Enumerated Values": "コンプライアンス列挙値", "Component": "コンポネント", "Component Code": "", "Component Description": "", "Component Instruction": "コンポーネント説明", "Component Name": "", "Component Query": "", "Condition": "条件", "Conditional UW Agreement": "保険商品査定条件設定", "Conditions": "条件設定", "Configuration History": "設定履歴", "Configure": "設定", "Configure Benefit Factors": "", "Configure Benefit Option Detail": "給付オプションの詳細を設定します", "Configure Payment & Collection Method": "入出金方法を設定", "Configure product categories for the tenant.": "テナント用の商品カテゴリを設定します。", "Configure the association between fields": "フィールド間の関連関係を設定", "Confirm": "確定", "Confirm Export": "", "Confirm Import": "", "Confirm to submit the product?": "商品を登録してよろしいでしょうか。", "Confirmation Period": "確認期間", "Conversion Date": "換算日", "Copy Formula": "計算式をコピー", "Copy Link": "リンクコピー", "Copy Reminder": "コピー確認", "Copy Successfully": "正常にコピーされました", "Copy to New Product": "商品のコピー", "Copy to New Version": "新しい販売商品バージョンをコーピー作成", "Coverage Data Change Type": "保険期間変更種類", "Coverage Date Change Type": "", "Coverage Period": "保険期間", "Coverage Period Fields to Revise": "", "Coverage Period Range": "保険期間の範囲", "Coverage Period Type": "保険期間の種類", "Coverage Period Value Type": "設定方法", "Coverage Type": "補償タイプ", "Create": "作成", "Create successfully": "", "Create_time": "作成日時", "Creator": "作成担当者", "Critical illness name": "重病名称", "Critical illness stage": "重病段階", "Customer Enumerated Values": "顧客列挙値", "Customized Data": "", "CV Formula of Basic SA": "現価計算式(基本保険金額)", "CV Formula of Campaign Free SA": "現価計算式(贈る保険金額)", "CV Formula of Reversionary Bonus": "現価計算式(増額配当金)", "CV is sufficient for one period APL": "保険契約の現金価値は一回目の保険料自動貸付の費用に当たれる", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "日額ベース: システムは日払保険料を計算し、次に毎回保険料の日数に基づいて保険料を計算します。（年契約の場合、毎回保険料は異なる）", "Data filling": "データ入力", "Data has existed. please check!": "データが既存しております。確認してください！", "Data Type": "データタイプ", "Day of Fixed Due Date": "固定応当日", "Day of Usage Premium Start Date": "利用開始日", "Day(s)": "日数", "Day(s) After Due Date": "支払満期日以降", "Day(s) Before Due Date": "支払満期日より前", "Days": "日", "Days Type": "日タイプ", "DCA Amount": "ドルコスト平均金額", "DCA Frequency": "ドルコスト平均法頻度", "DCA frequency cannot be lower than premium frequency.": "DCA頻度は保険料頻度よりも低いことはできません", "DCA Frequency Type": "ドルコスト平均法頻度タイプ", "Deactivate Insured": "被保険者を無効にする", "Deactivate Insured After Fully Claimed": "保険金を全部請求した後、被保険者を無効にする", "Deduction cut-off date upon policy terminates": "保険契約終了時の控除締切日", "Deduction Date": "控除日", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "応当日に控除する場合、0を入力してください。応当日の前に控除する場合、1，2，3などを入力してください。", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "契約始期日に控除する場合、0を入力してください。契約始期日の前に控除する場合、1，2，3などを入力してください。", "Deduction Source": "控除根拠", "Default ANFO Setting Up": "デフォル不可没収オプション", "Default as Reinvestment if dividend amount per fund is less than": "ファンドの配当金額が{xxx}未満の場合、デフォルトで再投資されます。", "Default as tenant": "テナントとしてデフォルト設定", "Default logic for refunded premium is confirmed instalment premium after incident date. If not, refund calculation formula needs to be configured here.": "保険料の返金に関するデフォルトのロジックは、事故発生日以降の分割払い保険料です。そうでない場合は、返金計算式をここで設定する必要があります。", "Default Option": "デフォルトのオプション", "Default Time": "", "Default Time Editable": "", "Defer Period": "延期期間", "Deferred Interest": "遅延利息", "Define": "定義", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "", "Define Benefit Options": "利益選択の定義", "Define Factor & Ratetable by Scenario": "シナリオ別で因子とレートテーブルを設定する", "Define Fund Calculation Basis for Each Transaction": "取引に対応するファンド価格計算のベースを設定する", "Define Illustration Sequence": "デモンストレーションシーケンスを設定する", "Define the day before policy expired date and days after expired date to extract renewal policy": "契約満了日の前日と後日を定義し、更新契約を抽出します。", "Define the times of product sa and planned premium.": "商品保険金額と計画保険料の回数を定義してください。", "Define whether it is the normal policy insure date or effective date should be within relevant master policy effective period when normal policy issuance.": "通常の保険契約の保険開始日または有効日が、関連する原契約の有効期間内であるべきかを定義します。", "Defined Application Elements": "固定的な申込項目", "Delay Type": "遅延タイプ", "delete": "削除", "Delete Failed": "", "Delete Successfully": "削除は成功しました。", "Delete successfully.": "削除しました。", "Delete will affect the associated work scheduling. Are you sure to delete it?": "", "Deleted successfully": "削除しました", "Deleting this record will affect the associated rider relationships. Please delete the related rider relationship records first.": "このレコードを削除すると、関連される特約との関係に影響します。まず、関連される特約との関係記録を削除してください。", "Departure Airport": "出発空港", "Departure City/Place": "出発都市/場所", "Departure Date": "出発日", "Description": "説明", "Deselect this goods will remove it from related bundle rules in step 2. Please confirm.": "", "Diagnosis": "診断", "Diagnosis Code": "診断コード", "Diagnosis Configuration Method": "診断設定方法", "Diagnosis Description": "診断内容", "Diagnosis Set": "", "Digit": "数字", "Disability Category": "障害分類", "Disability Classification": "後遺障害分類", "Disability Grade": "後遺障害レベル", "Disability Set": "障害者グループ", "Discount Sequence": "割引の計算順序", "Discount Type": "", "Disease Agreement": "疾病約定", "Disease Classification": "疾病種類", "Distribution Method": "配当方法", "DMS": "DMS", "Dollar Cost Averaging Arrangement": "ドルコスト平均法", "Down Sell Indicator": "減額", "Download Result": "結果をダウンロードする", "Download Successfully": "ダウンロードに成功しました", "Download Template": "テンプレートをダウロード", "Draft": "", "Drag to adjust the calculation order": "ドラッグして計算順序を調整します", "Due Date": "応当日", "Due Date Rules": "応当日ルール", "Dunning Rule": "督促設定", "Duplicate data": "重複したデータがあります。", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "同じカテゴリとサブカテゴリにで重複したフォミューラが設定されました。変更してください。", "Duplicate formula configuration for same formula sub category and tax type. Please edit original one.": "同じ計算式のサブカテゴリーと税タイプで計算式の設定が重複しています。元のものを編集してください。", "Duplicated data exists, please check the Original Currency and Target Currency.": "元通貨と対象通貨を確認してください。", "Duplicated Interest!": "重複した利益!", "E.g.: If there exists multiple liabilities for one insured. User could define whether to deactivate this insured when one of the liability has been fully claimed.": "例:1人の被保険者に複数の責任がある場合。ユーザーは、責任の1つが完全に請求されたときに、この被保険者を無効にするかどうかを定義できます。", "E.g.: If there exists multiple liabilities under one product. User could define whether to terminate this liability/Product when one of the liability has been fully claimed.": "例:1つの製品に複数の負債がある場合。ユーザーは、責任の1つが完全に請求されたときに、この責任/製品を終了するかどうかを定義できます。", "Each premium frequency can only configure 1 DCA frequency record.": "各プレミアム頻度は、1つのDCA頻度レコードのみを設定できます。", "EARLIEST OF": "", "Edit": "編集", "Edit Charge": "費用変更", "Edit Claim Stack Template": "クレームスタックテンプレート編集", "Edit Component": "", "Edit Enumerated Fields Dependency": "", "Edit Factor": "", "Edit Formula": "計算式編集", "Edit Liability": "補償内容の編集", "Edit Ratetable": "保険料率編集", "Edit Reminder": "リマインダーの編集", "Edit Tag Definition": "", "Editing": "編集中", "EDITING": "編集", "Editing any formula or ratetable within the new or original product will impact its calculations.": "", "Editing any formula or ratetable within the new or original version will impact its calculations.": "", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "利益要素を編集すると、既存のアップロードされた利益の詳細が消去されます。ご確認してください。", "EFFECTIVE": "", "Effective Date Month End to Expiry Date Month End": "", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "Effective Date Rule": "契約開始日ルール", "Effective Period": "有効期間", "Effective Time": "有効日時", "Element": "要素", "Embedded Ratetable": "エンベデッドレートテーブル", "Enable Green Card Issuance Logic for Non-EU Countries": "", "Enable Non-Fleet Grade and Accident Index Configuration": "", "End date": "終了日", "Enumerated Fields Dependency": "", "Error": "エラー", "Error Description": "", "error format, eg: 1/1.0/1.00": "入力形式エラ、例1/1.0/1.00", "Estimated Lapse Date Formula": "予定失効日計算式", "Every": "それぞれ", "Example": "例", "Example: V01": "例: V01", "Exception Output": "例外結果", "Excess Premium Handling": "残保険料の取り扱い", "Exchange Rate": "為替レート", "Exchange Rate (Buying)": "為替レート（TTB）", "Exchange Rate (Middle)": "為替レート（TTM）", "Exchange Rate (Selling)": "為替レート（TTS）", "Exchange Rate Query": "為替レート検索", "Exclusion Indicator": "保険金制限可否", "Existing Stack in the Product": "商品内の既存のスタック", "Exists duplicate value": "重複している値があります。", "Exit Full Screen": "全画面モードを抜け", "Expand": "追加", "Expiry Date Adjustment": "満期調整ルール", "Expiry Date Calculation Method": "満期日計算方法", "Expiry Date Rule": "満期日ルール", "Expiry Time Agreement Type": "満期計算方法", "Export": "エクスポート", "Export All": "", "Export failed, please try again!": "エクスポートに失敗しました。もう一度やり直してください。", "Export Operation Fail": "エクスポート操作の失敗", "Export Range": "", "Export successfully!": "", "Export to CSV": "CSVファイルへ出力", "Extended Term": "延期", "Extended Term Formula": "延期計算式", "External: Compliance decision can only be provided by external triggered.": "", "Extra Grace Period": "余分な猶予期間", "Extra Loading": "特別保険料", "Extra Loading Calculation Method": "特別保険料の計算方法", "Extra Loading Indicator": "保険料割増指標", "Extra Loading Type": "保険料割増の区分", "Extra Premium Formula Code": "特別保険料計算式コード", "Extract Bill Date": "請求書日付の抽出", "Extract Bill Days Before Due Date": "支払期日前の請求日数の抽出", "Extract day(s) must be greater than offset day(s)": "抽出日数はオフセット日より大きくなければなりません", "Extract premium from X days prior to due date": "未払契約抽出日は応当日よりX日前", "Extraction Method": "抽出方法", "Factor": "因子", "Factor Code": "要素コード", "Factor Definition": "因子定義", "Factors": "因子", "Fee Limit": "費用/回数制限", "Fee Type": "費用種類", "Field Value": "フィールド値", "File": "ファイル", "File Declaration": "ファイル説明", "File Management": "", "File Name": "ファイル名", "File Type": "ファイルタイプ", "FILE TYPE": "ファイルタイプ", "file upload failed": "ファイルアップロード失敗", "file uploaded successfully": "ファイルアップロード成功", "File Version": "ファイルバージョン", "Filter": "フィルター", "Financial Anniversary Date": "事業年度応当日", "First": "最初", "First Date": "開始日", "first.": "最初", "Fixed Age": "固定年齢", "Fixed Amount": "固定金額", "Fixed Amount or Fixed Period": "固定金額また固定期間", "Fixed due date rule：Fixed due date or period end date": "固定日ルール：固定期日または期間終了日", "Fixed Due Date Value": "固定満期日", "Fixed Exchange Rate": "固定為替レート", "Fixed Time": "", "Fixed Value": "固定值", "Fixed Value is required": "固定値が必須です。", "Flexible Premium Allocation": "柔軟な保険料払込および保険料配分", "Flexible Premium and SA Agreement": "柔軟性のある保険料と保険金額の約定", "Flexible Premium Relationship": "柔軟性のある保険料関係", "Flexible Premium Type 1": "柔軟性のあるのタイプ１", "Flexible Premium Type 2": "柔軟性のあるのタイプ２", "Floating Exchange Rate": "変動為替レート", "Follow the same as Planned Premium": "計画された保険料と同じ方法で追加入金を行う", "For Fixed Day of Month, please input number from 1 and 28": "每月の固定日は、1～28の数字を入力してください。", "For formula(s) or ratetable(s) created inside one specific product, the editing function is not supported.": "特定の商品内で作成された計算式やレートテーブルの編集機能はサポートされていません。", "For formulas that use the list function, Batch Test is not supported yet.": "リスト関数を使用する数式については、バッチテストはまだサポートされていません。", "For liabilities without combination relationship configured, it will be settled as \"Attachable\" by default. If you configure liability combination relationship as \"Compulsory\", it means liability 2 depends on liability 1; And if you configure as \"Non-attachable\", it means liability 1 and liability 2 can not be sold in combination.": "For liabilities without combination relationship configured, it will be settled as \"Attachable\" by default. If you configure liability combination relationship as \"Compulsory\", it means liability 2 depends on liability 1; And if you configure as \"Non-attachable\", it means liability 1 and liability 2 can not be sold in combination.", "For New-Biz": "新規契約向け", "For policy annual illustration": "保険年間イラストレーション", "For POS": "異動向け", "For POS triggered illustration": "異動をトリガーとしたイラストレーション", "For step {{index}}, nested calculation components within the ifElse component are not allowed. Please create a separate step before ifElse for calculations.": "ステップ{{index}}については、計算コンポーネントを前のステップに前置して、条件判断の中で比較演算子と計算コンポーネントを同時に使用することを避けてください。", "For Usage based insurance, accrued premium is calculated according to actual usage during certain period. Usage Premium Start date represents the first day of usage period. And the end date will be calculated based on Usage Premium Start Date and payment frequency.": "利用ベース自動車保険では、一定期間内に実際の利用量に応じて発生した保険料が計算されます。保険料開始日は、利用期間の初日を表します。終了日は、保険料の開始日と支払い頻度に基づいて計算されます。", "Format": "フォーマット", "formula": "計算式", "Formula": "計算式", "Formula Category": "計算式カテゴリー", "Formula Code": "計算式コード", "Formula Code could not start with \"G_\".": "計算式コードは \"G_\"で始めることはできません。", "Formula Code could not start with numbers.": "計算式コードを数字で始めることができません。", "Formula Description": "計算式説明", "Formula Detail": "計算式の詳細", "Formula in Specific Scenarios": "特定のシナリオにおけるフォーミュラ", "Formula Level": "計算式レベル", "Formula Management": "計算式管理", "Formula Name": "計算式名", "Formula Sub Category": "計算式のサブカテゴリ", "formulas": "計算式", "Free Switch Times": "無料でスイッチ回数", "Free Withdrawal Time(s)": "無料口座引き出し回数", "Freelook Period": "クーリングオフ期間", "Freelook Period Base Date": "クーリングオフ期間の基準日", "Freelook Period Expiration Rule": "フリールック期間満了ルール", "Freelook Refund Account Rule": "クーリングオフ期間の払い戻し口座ルール", "Freelook Reverse Rules": "フリールックの返金ルール", "Frequency": "頻度", "Full Screen": "全画面モード", "Fund": "ファンド", "Fund Appointment Rate for Rebalance": "投資リバランスのファンド配分比率", "Fund Calculation Basis Details": "ファンド計算ベースの詳細", "Fund Calculation Basis of Product": "商品のファンド計算ベース", "Fund Code": "ファンドコード", "Fund Deduction Method": "ファンド控除方法", "Fund Deduction Sequence": "ファンド控除順序", "Fund Info": "ファンド情報", "Fund Trad Type": "ファンド取引タイプ", "fund_name": "ファンド名", "gender": "性别", "Gender": "性别", "Generate": "作成", "Generate Cash Value Saving Account": "現金価値貯蓄口座を作成する", "Generate new policy number for renewal": "新加入者番号を作成する", "Generate Result": "結果を生成する", "Generate Test Excel": "テスト用のExcelを生成する", "Generate Vehicle Databases Fields": "車両データベースフィールドを作成", "goods": "販売商品", "Goods / Package / Product not found during export process. Please check if the data exists and is correctly configured in the system.": "", "Goods A": "", "Goods allowed to be sold together": "", "Goods B": "", "Goods Code": "販売商品コード", "Goods Code/Goods Name": "販売商品コード/販売商品名", "GOODS CONFIGURATION": "", "Goods Name": "販売商品名名", "GOODS RELATED CONFIGURATION": "", "Goods V2": "", "Got it": "", "Grace Period": "保険料払込猶予期間", "Grace Period Rule": "猶予期間ルール", "Green Card Fee": "", "Green Card(Non-EU)": "", "Guarantee Period": "保証期間", "Guarantee Period Value Type": "保証期間の値タイプ", "Handling of Charge Deduction within or out of NLG": "NLG内外での料金控除の取り扱い", "Handling of Premium Unpaid": "未払い保険料の処理方法", "Handling of TIV Insufficient": "投資口座残高不足時の処理", "Has Cash Value": "現金価値有無", "Have SA": "保険金額があるかどうか。", "Health Claim Info": "健康告知情報", "HH:mm": "", "Holiday": "休日", "Hospital/Clinic": "病院/診療所", "Hospitalization Allowance Amount": "入院手当額", "Hospitalization Allowance Value Type": "入院手当タイプ", "Hospitalization start date be within x days/months/years after Incident date. E.g: if hospitalization start date should be within 1 year after Incident date, configure as 1, Year(s)).": "入院開始日は、事件日からx日/月/年以内になります。 たとえば、入院開始日が事故日から1年以内である場合は、1年として設定します。", "Hour": "時間", "ID": "ID", "if calculation sequence is needed，please make sure all discount have sequence definition.": "計算シーケンスが必要である場合、すべての割引計算式にシーケンスを設定してください。", "if calculation sequence is needed，please make sure all loading have sequence definition.": "計算シーケンスが必要である場合、すべての特別保険料計算式にシーケンスを設定してください。", "If cash value have surplus after ETI": "延期後に現金価値が余っている場合", "If cash value have surplus after paid up": "払済後に保険契約の現金価値が余っている場合", "If choose 'Refund', premium collection will be reserved in suspense and refunded later. If choose 'Fill in the ILP premium plan or installment premium', system will settle the installment premium or partially fill the ILP premium plan.": "'返金' を選択する場合、保険料収入は仮受金口座に送金します。'ILP保険料プランまたは分割払いの保険料を支払う' を選択する場合、システムは保険料課金または一部のILP商品の保険料を相殺します。", "If choose No, RTU will share same payment frequency with Planned Premium. If choose Yes, RTU is allowed to use different payment frequency with Planned Premium, which could be defined below.": "「いいえ」を選択した場合、定期増額は分割保険料と同じ払込頻度を共有します。 「はい」を選択すると、定期増額は分割保険料と異なる払込頻度を使用します、詳細は下に設定してください。", "if condition": "もし条件", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "if illustration sequence is needed，please make sure all illustration items have sequence definition.": "イラストの順序が必要な場合は、すべてのイラストアイテムが順序定義を持っていることを確認してください。", "If liability could not be found here, please check whether liability has configured claim type in liability management module": "ここで補償内容が見つからなかった場合は、補償内容管理モジュールで保険金請求タイプが設定されたか否かを確認してください", "If no parameter could be found, you could create middle factor by using the format Middle_XXX.": "パラメーターが見つからない場合は、書式 Middle_XXX を使用して中間因子を作成できます。", "If not defined on the product, the rules defined on the package will be used (allowing for unified definition directly when packaging the package)": "", "If someone planning to drive in non-EU countries that recognize the Green Card, he/she must request the card from auto insurance provider before traveling. This function covers the Green Card logic for purchasing auto insurance policies in non-EU countries, including fees, issuance, endorsements, refunds, printing, etc.": "", "If surgery classification needed, please configure 'Surgery Level' in advance!": "手術分類が必要な場合、手術レベルを先に設定してください。", "If the customer is responsible for 10%, the value is set to 10/.": "お客様側が10%を負担する場合、10と設定してください。", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "", "If the premium is insufficient to pay any outstanding billing or ILP premium plan, then": "保険料収入が任意の請求書に不足しているか、ILPの保険料を完納するのに不足している場合", "If this product has waiver liability, all liabilities must be mandatory.": "この商品に権利放棄責任がある場合、すべての責任は必須でなければなりません。", "If value of the factors are different across period.": "時期によって因子の値が異なる場合", "If value of the factors are same across period.": "各期間の因子値が同じ場合", "If you select yes，system support to add insurable interest under liability.": "「はい」を選択すると、システム?サポートにより、負債の被保険者利息が追加されます。", "Illustration Item": "シミュレーション項目", "Illustration Item Code": "イラスト項目コード", "Illustration Item List": "デモンストレーションシ項目リスト", "Illustration Item Value cannot starts with 'G_'": "イラスト項目の値は「G_」で始めることはできません。", "Illustration Item(PSI)": "イラスト項目（PSI）", "Illustration Level": "イラストレーションレベル", "Illustration Period": "イラストレーション期間", "Illustration Period Rule": "イラストレーション期間ルール", "Illustration Scenario": "デモンストレーションシナリオ", "Illustration Sequence": "イラストレーション順序", "ILP Bonus": "ILP配当金", "ILP Charge": "控除", "ILP Fund Calculation Basis": "ILPファンド計算ベース", "ILP Lapse": "ILP失効", "ILP Premium Holiday": "ILP 商品払込一時停止", "ILP Premium Limit": "ILP 保険料の制限", "ILP TIV Account Type": "ILP TIVアカウントタイプ", "ILPC + 3 digits": "ILPC+3桁数字", "Import": "インポート", "Import / Export": "インポート/エクスポート", "Import All": "", "Import Operation Fail": "インポート操作の失敗", "Import Successful!": "インポートに成功しました!", "In Japan's auto insurance system, Non-Fleet Grade and Accident Index are two key factors that affect policy rating and premium adjustments. This function is used to define the calculation formulas for Non-Fleet Grade and Accident Index.": "", "In Progress": "進行中", "In use:": "使用中", "Inactive": "無効", "Incident Date": "事故発生日", "Incident Reason": "事故原因", "Including Tax": "税込みでしょうか", "Increase Interest By(%)": "利息の増加率(%)", "Individual Test": "個別テスト", "Inforce Together": "一緒に強制", "Initial Premium Period": "初期保険料期間", "input is illegal": "入力不正", "Insert fields": "", "Installment Calculation Method": "分割払いの計算方法", "Installment Detail": "分割払情報", "Installment Info": "分割払い情報", "Installment Payment Plan for Claims": "", "Installment Payment Type": "分割支払タイプ", "Installment Premium Calculation Basis": "分割払い保険料の計算ベース", "Installment Premium Calculation Method": "分割払保険料計算方法", "Installment Standard Premium": "分割払い標準保険料", "Installment Standard Premium (Inc Tax)": "分割標準保険料（税込）", "Installment(s) Premium": "回分の分割払保険料を請求する", "Insurable Interest ID": "被保険利益ID", "Insurable Interest Info": "被保険利益情報", "Insurable Interest Name": "被保険利益名", "Insurable Interest/Liability Rate Setting": "被保険利益/補償のレート設定", "Insured Age Comparison": "被保険者年齢比較", "Insured Elements Relationship Matrix": "被保険者要素関係マトリックス", "Insured Info": "被保険者情報", "Insured Occupation Class": "被保険者職業の分類", "Insured Occupation Risk Category": "", "Insured's Entry Max Age": "被保険者の最大年齢", "Insured's Entry Min Age": "被保険者の最低年齢", "Insured‘s Entry Max Age": "被保険者の最大年齢", "Insured’s Age Range(NB)": "被保険者の年齢範囲(新規契約)", "Insured’s Age Range(Renewal)": "被保険者の年齢範囲（継続契約）", "Insured’s Entry Min Age": "被保険者の最小年齢", "Interest": "利息", "Internal: Compliance decision can only be provided by compliance user.": "", "Investment Agreement": "", "Investment Delay": "遅延投資", "Investment Delay Option": "遅延投資方式", "Is switch charge a fixed amount?": "スイッチ料は定額でよろしいでしょうか？", "is the default condition.": "...がデフォルトの条件である。", "Is there a need to make a lump sum payment before executing the installment plan?": "分割プランを実行する前に、一括で支払う必要はありますか？", "Issue with Green Card (Non-EU)": "", "It is also supported entering the exchange rate unit. You can open \"Component Instruction\" for more details.": "為替レート単位の入力にも対応しています。詳しくは「コンポーネント説明」をご覧ください。", "It will take a certain time for batch test. A platform message will be sent to you once the batch test is completed. And you could download test result through the message.": "バッチテストには一定の時間がかかります。バッチテストが完了すると、プラットフォームメッセージが送信されます。そして、そのメッセージを通してテスト結果をダウンロードすることができます。", "itemExtend1": "項目拡張1", "itemExtend2": "項目拡張2", "itemExtend3": "項目拡張3", "itemExtend4": "項目拡張4", "itemExtend5": "", "itemExtend6": "", "Japan Motor": "", "LANGUAGE": "言語", "Lapse Date": "失效日期", "Lapse/Termination": "失効/終了", "Last Modifier": "最終更新者", "LATEST OF": "", "Less": "少ない", "Liability": "補償内容", "Liability <{{liabilityId}}><{{liabilityName}}> has been created successfully.": "補償 <{{liabilityId}}><{{liabilityName}}> が正常に作成されました。", "Liability <{{liabilityId}}><{{liabilityName}}> has been edited successfully": "補償 <{{liabilityId}}><{{liabilityName}}> が正常に編集されました。", "Liability Agreement": "補償約定", "Liability Category": "補償項目カテゴリー", "Liability Code": "補償内容コード", "Liability Coverage Period": "補償内容の保険期間", "Liability Coverage Period defaults to the same as Product Coverage Period. If it differs from Product Coverage Period, you can define an independent coverage period for the liability here.": "補償内容の保険期間はデフォルトで保険商品の保険期間と同じです。保険商品の保険期間と異なる場合は、ここで補償内容の独立した保険期間を定義できます。", "Liability Description": "補償内容の説明", "Liability Handling": "補償内容の変更", "Liability Handling After ETI": "延長後の補償内容処理", "Liability Handling After Paid Up": "払済の補償範囲", "Liability ID": "補償内容ID", "Liability Info": "補償情報", "Liability information and POS items can not be configurated at the virtual main benefit. Do you want to delete them?": "バーチャル主契約に補償内容と異動項目は設定できません。削除してよろしいでしょうか？", "Liability is required": "補償内容は必須項目です", "Liability Level": "補償内容のレベル", "Liability Name": "補償内容名称", "Liability Name(Liability1)": "補償内容名称（補償内容1）", "Liability Name(Liability2)": "補償内容名称（補償内容2）", "Liability or Interest is required": "補償または利息が必要です。", "Liability Relationship Matrix": "補償内容関係マトリックス", "Liability Remark": "補償項目追加説明", "Liability SA": "補償内容の保険金額", "Liability Tag": "補償内容タグ", "Liability Termination": "補償内容の終了", "Liability Termination by Claim Setting": "保険金支払による全損失効設定", "Lien Indicator": "保険金削除可否", "Life Event": "ライフイベント", "Linked Formulas": "関連する計算式", "Linked Goods": "関連販売商品", "Linked Package": "", "Linked Packages": "関連パッケージ", "Linked Product / Package / Goods": "関連する商品/パッケージ/販売商品", "Linked Products": "関連商品", "Linked Ratetables": "", "Linked Rules": "", "Load More": "もっと読み込み", "Loading Method": "特別保険料の計算方法", "Loading Sequence": "特別保険料の計算順序", "Loading Type": "", "Loan Amount": "ローン金額", "Loan Term Period": "ローン期間", "Loan Term Type": "ローン期間のタイプ", "Low Risk Fund": "低リスクのファンド", "Lump Sum Amount": "一括払い: {{amount}}", "Main / Rider": "主契約/特約", "Main Benefit (Virtual)": "", "Main Condition Type": "主な条件の種類", "Main or Rider": "主契約か特約", "Main Product Code": "主契約のコード", "Main Product Name": "主契約の名称", "Main Relationship with Rider": "", "Make sure at least one of the diagnosis tag has been configured.": "少なくとも一つの診断タグを設定してください。", "Make sure at least one of the disabilities has been configured.": "少なくとも1つの障害が設定されていることを確認してください。", "Manual Adjustment": "", "Marketing Enumerated Values": "マーケティング列挙値", "Matrix Table": "マトリックステーブル", "Matrix Table Code": "", "Maturity Agreement": "満期設定", "Maturity Benefit": "満期金", "Maturity Benefit Leading Days": "N日前に満期金を作成する", "Maturity Reminder Date Compare to Policy Expiry Date": "満期通知時間（契約満期日との比較）", "Maturity Reminder Rule": "満期通知ルール", "Maturity Reminder Rule (Legacy)": "", "Maturity Reminder Rule cannot be repeated.": "満期通知ルールは重複することができません", "Max": "最大", "Max Claim Limit": "最大支払限度額", "Max Claim Percentage (%)": "最大支払率", "Max Claim Times": "最大保険金請求回数", "Max Down-Regulation Ratio": "最大引下げ比率", "Max Guaranteed Renewable": "最大担保再生可能", "Max SA Multiplier": "最大保険金額乗数", "Max SA Multiplier Type": "最大保険金額乗数タイプ", "Max Up-Regulation Ratio": "最大引上げ比率", "Max value cannot be less than min value": "", "Maximum Age for Whole Life": "加入年齢の上限", "Maximum Loanable Amount Formula": "最大貸付可能額の計算式", "Maximum Loanable Percentage": "最大貸付可能率", "Maximum Premium Holiday": "保険料収納一時停止最長期間", "Maximum Premium Holiday Period Type": "最大保费假期类型", "Medical Bill Configuration": "", "Medical Bill Item": "", "Medical Bill Type": "", "Medical Bill Type and Bill Item": "医療費用種類と明細", "Medical Billing Type": "医療請求のタイプ", "Medical Expense": "治療費用", "Medical Fee Rules": "健康診断料金ルール", "MENU": "メニュー", "Middle factor format is not correct. Please follow format: Middle_XXX.": "中間ファクターのフォーマットが正しくありません、\"Middle_\"で始まる必要があります。", "Min": "最小", "Min / Max Times": "最小/最大回数", "Min SA Multiplier": "最小保険金額", "Min SA Multiplier Type": "最小保険金額のタイプ", "Min Value of Policy": "減額後の最小残高", "Min Withdrawal Amount": "最低減額", "Minimum Investment Period": "最小投資期間", "Minimum Investment Period Applied to": "投資期間延長適用される商品", "Minimum Investment Period Type": "最小投資期間タイプ", "Minimum Investment Period Value": "最小投資期間値", "Minimum Net Premium (Standard Premium+Extra Premium-Premium Discount) for coverage period if Net premium calculated from formula is below minimum net premium.If regular payment, minimum premium cap needs to be multiplied by modal factor.": "補償期間の最小純保険料（標準保険料＋特別保険料－保険料割引）は、計算式で算出した純保険料が最低純保険料を下回る場合に適用されます。定期払いの場合は、最低保険料の上限に係数を乗じる必要があります。", "Minimum Premium Cap": "最小保険料", "Minute(s)": "分", "Missing parameter": "パラメータを入力してください", "Missing required parameter": "必須パラメータがありません", "Modal Factor": "係数", "Modifier": "", "Modifying the diagnosis tag info will auto clear the Diagnosis configuration. Please confirm whether to continue?": "診断タグを変更すると、診断設定が自動的にクリアされます。 続行してもよろしいですか？", "Modifying the Surgery level info will auto clear the surgery configuration. Please confirm whether to continue?": "手術レベルの変更より、手術設定は自動的にクリアされます、確認してください。", "Modifying the Surgery tag1 info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Modifying the Surgery tag2 info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Modifying the tag definition info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Month": "月", "Month Value": "月次値", "Month Value for Advance Filling": "事前支払い月数", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "月額ベース: システムは月払保険料を計算し、次に毎回保険料の月数に基づいて保険料を計算します。（年契約の場合、毎回保険料は同じ）", "Months": "月", "Multi Language": "", "Multi-language": "多言語対応", "My Creations": "", "NA": "", "Name": "名称", "Name Connection Method": "氏名の組み合わせ方法", "Name Format": "名称フォーマット", "Name Sequence Definition": "氏名の順番定義", "NCD Code": "NCDコード", "NCD Formula Code": "NCD計算式コード", "NCD Management": "NDC管理", "Need To Attach Advanced Criteria": "条件を添付する必要があります", "Net Premium Adjustment": "", "New Category": "", "New Claim Stack Structure": "", "New Product after ETI": "延期後の新たの商品", "New Product after Paid Up": "払済後の新しい商品", "New Version": "", "Next": "次へ", "NLG Condition": "不失效保证的条件", "NLG Period": "失効せず保証期間", "NLG Period Factor": "NLG期間パラメータ", "NLG Period Type": "不失效保证的期间类型", "NLG Period Type is required": "NLG期間種類が必要です", "NLG Period Value": "失効せず保証期間値", "NLG Period Value is required": "NLG期間タイプが必要です", "no": "いいえ", "No Claim Bonus": "無事故ボーナス", "No Claim Discount": "無事故割引", "No component is selected. Please modify.": "", "No Content": "", "No Data": "データなし", "No impact on original formula when editing formula copies": "計算式/レートテーブルコピーを編集すると、元販売商品に影響はない", "No Lapse Guarantee": "失効せず保証", "No match result.": "一致した結果がありません", "No Valid Data": "有効なデータがありません", "No.": "No.", "No. Claim Times": "保険金請求回数", "No. of Completed Policy Years for Bonus Vesting on Claim": "補償付き配当金の保険契約年度", "No. of Completed Policy Years for CB Becoming Payable": "現金配当の給付が開始される保険契約年度", "No. of Completed Policy Years for Surrender Bonus Allowed": "消滅配当金発生の契約年数", "No.of Installment": "分割払い回数", "Non-Fleet Grade and Accident Index of Japan Motor": "", "Non-Fleet Grade Formula": "", "Nonforfeiture Option Type": "不可没収オプションの種類", "Normal Policy Date Check Rule Compare to Master Policy": "普通契約と団体契約の間の日付チェックルール", "Normal Policy Expiry Date Rule": "通常ポリシーの満期日ルール", "Not Applicable": "", "Not Cascade": "連鎖しない", "Note: The relationship defined here is only applicable when the package is configured as single-main-benefit.If the package is marked as supporting multiple main benefits, this relationship will be ignored and have no effect.": "", "Notice": "", "Notice Date Compare with Due Date": "通知日と支払期日の比較", "Notice Rule": "通知ルール", "Notice Rule cannot be repeated": "通知ルールは重複できません", "Notification": "", "number": "番号", "Number": "番号", "Number Item": "項目番号", "Number Items": "項目番号", "Number of Files/URLs": "ファイル数/URL数", "Number1": "番号１", "Number2": "番号２", "Object": "対象", "Object Category": "商品カテゴリー", "Object Category & Object": "", "Object Category & Type": "", "Object Category Code": "", "Object Category Name": "", "Object Component": "", "Object Element-Auto-Vehicle": "", "Object Enumerated Values": "オブジェクト列挙値", "Object Info": "補償対象情報", "Object Sub Category": "補償対象サブカテゴリ", "Object.etc. Driver": "補償対象 例:運転手", "Obtain Template": "テンプレートを取得", "Occupation Risk Category": "仕事・業種別のリスクカテゴリー", "Occurrence Type（Accommodation)": "発生タイプ（施設）", "Occurrence Type（Transportation)": "発生タイプ（運送）", "Occurrence Type（Transportation）": "発生タイプ（交通）", "Offset Date": "オフセット日付", "Offset from X Days Compare to Expired Date": "満期日のX日前から", "Offset from X days prior to due date": "支払期日のX日前から", "Once allowed, the client don't need to pay full amount for total overdue premium bills during reinstatement. Only a minimum premium payment is required to pay all the charges and keep the policy effective. The POS formula needs to be defined under this condition.": "許可されると、契約者は復活時に延滞した保険料の全額を支払う必要がなくなります。契約の有効性を維持するための最低限の保険料を支払うのみです。 この状況で の計算式を設定する必要があります。", "Once opened, system allow policyholder to rebalance their fund account (TIV) based on specific ratio regularly.": "一旦開設されると、契約者はシステムで特定の比率に基づいて定期的にファンド口座（TIV）をリバランスすることができます", "Once opened, system allow user to trigger adanced DCA arrangement. Premium will put into a pre-defined low risk fund first, and then switch out and buy target fund on a regular basis.": "一旦開設されると、ユーザーはシステムで高度なDCAアレンジメントをトリガーすることができます。保険料は、まずあらかじめ定義された低リスクのファンドに投入され、そして定期的にスイッチアウトしたり、ターゲット・ファンドを購入したりすることができます。", "Only components with the same stack type can be in one template. Different stack types are detected in your submission. Please check the configuration.": "", "Only display formulas that are directly defined and associated under Package Management (e.g., Tax Setting, Premium Adjustment...)": "", "Only for Commission & Service Fee formula, it is supported to query linked Goods": "手数料とサービス料の計算式のみ、関連商品の照会ができます。", "Only for Commission & Service Fee formula, it is supported to query linked Goods.": "手数料とサービス料の計算式のみ、関連商品の照会ができます。", "Only for Levy and Stamp duty formula, it is supported to query linked package": "賦課金と印紙税の計算式のみ、関連商品組合せの照会ができます。", "Only one formula allowed for each fund, no duplicated allowed.": "1つファンドに対して、1つ計算式しか設定できません", "only one formula type either rate or premium could be configured for one product.": "同じ保険商品に対して、保険料計算式と保険料率計算式を同時に使うことができません。", "Only one modal factor allowed for each payment frequency, no duplicate allowed. Please check.": "支払頻度ごとに 1 つのモーダル係数のみ許可されます。重複しないようにご確認ください。", "Only one record is allowed for each calculation method. No duplicate allowed.": "各計算方法に対して一つだけの記録が許可されます。重複不可。", "Only perform rebalancing when the variance exceeds": "ファンドの価値が以下の予定値を超えた時にリバランスを行います。", "operation failure": "操作失敗", "Optional": "オプション", "Optional / Required": "オプション/必須", "Order": "オーダー", "Original Currency": "原通貨", "Out of No Lapse Guarantee": "失効せず保証期間後", "Overdue Auto Deduction": "不払解除の前に再度決済", "Overdue Handling": "不払処理ルール", "Overdue Status": "猶予期間経過後の状態", "Package": "商品組合せ", "Package Code": "商品組合せコード", "PACKAGE CONFIGURATION": "", "Package Name": "保険商品組合せ名", "PACKAGE RELATED CONFIGURATION": "", "Package V2": "", "packages": "保険商品組合せ", "Paid Up": "払済", "Paid Up SA Formula": "払済の保険金額計算式", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "パラメータが変更されました。アップロードしたマトリクステーブルはクリアされます。ご確認ください。", "Parameter or result code have been changed. The uploaded ratetable will be cleared. Please confirm.": "パラメータや結果コードが変更されました。アップロードされた料金表は削除されます。ご確認ください。", "Parameters relevant to multiple object can not be used directly in steps without list function.": "複数オブジェクト関連のパラメータをList関数以外のステップで使用しないでください。", "Parent Field": "", "Parent Field Name": "", "Partial": "", "Partial withdrawal allowed": "一部取消可能", "Participating Agreement": "配当契約", "Payment & Collection Method": "入出金方法", "Payment Defer Period": "支払遅延期間", "Payment Defer Period Type": "支払遅延期間タイプ", "Payment Frequency": "保険料支払頻度", "Payment Frequency & Modal Factor": "支払頻度と係数", "Payment Option": "支払方式", "Payment Period": "支払期間", "Payment Period Type": "支払期間タイプ", "Payment Period Value Type": "支払期間値タイプ", "Payment/Collection Method & Account": "入出金方法&口座", "Pealse select Formula Code": "", "Pending Case Enumerated Values": "ペンディングケース列挙値", "Percentage": "", "Period": "期間", "Period Limit Matrix": "期間制限マトリックス", "Period Type": "期間種類", "Period Value": "値", "Permium Handling Method Within Premium Holiday": "保険料収納一時停止期間中の保険料処理方法", "Pin": "固定する／ピン", "Place of Incurred(Allowance)": "発生場所（手当）", "Place of Incurred(Medical Bill)": "発生場所（医療費用明細）", "Plan Info": "プラン情報", "Planned Premium": "純保険料", "Planned Premium by Layer": "レイヤー別計画保険料", "Planned Premium Layer Info": "レイヤー別計画保険料の情報", "Please": "お願い", "Please add at least one formula step": "計算式を入力してください", "Please Add Premium Notice Rule": "保険料徴収通知ルールを追加してください", "Please add stack correlation": "スタックの紐づけ設定をしてください", "Please add Terminal Bonus formula.": "消滅配当金の計算式を追加してください。", "Please at least select one Option": "少なくとも1つのオプションを選択してください。", "Please check the required name set first": "最初に必要な名前セットをチェックしてください。", "Please check whether you need to modify the following Formula:": "下記の計算式を修正する必要があるかどうかをご確認ください", "Please choose at least one type of name connection method & name.": "少なくとも 1 種類の名前接続方法と字幅を選択してください。", "Please complete coverage period agreement": "保険期間約定を入力してください", "Please config Enumeration Value": "列挙値を設定してください", "Please Configure a Formula for Vehicle Market Value": "車両市場価格の計算式を設定してください", "Please configure at least one of the agreement.": "少なくとも一つの契約を設定してください", "Please configure at least one record.": "少なくとも1件のレコードを設定してください。", "Please configure before submit.": "設定を完了してから提出してください。", "Please configure calculation logic for risk sub-category.": "リスクサブカテゴリの計算ロジックを設定してください。", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration -> Premium Notice Reminder\".": "", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration\".": "", "Please configure this agreement, otherwise insured will not be default deactivated after fully claimed of the selected liability.": "この契約を設定してください。そうでない場合、選択された補償の全額請求後に被保険者がデフォルトで無効になりません。", "Please configure this agreement, otherwise liability will not be default terminated after claim.": "この約定を設定しなければ、完全保険金請求後にデフォルトで責任が終了となります。", "Please configure transaction type.": "取引タイプを設定してください。", "Please confirm changing the default option from": "デフォルトオプションの変更を確認してください。", "Please confirm setting the default option to": "デフォルトオプションを設定することを確認してください。", "Please confirm to wipe out all uploaded benefit details and all versions.": "", "Please continue to finish Premium holiday related configuration in the \"ILP Premium Holiday\" Section.": "「ILP 商品払込一時停止」セクションで、引き続き払込一時停止関連の設定を完了してください。", "Please copy your File URL first.": "まずファイルのURLをコピーしてください。", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "", "Please define the allowed investment strategy.": "設定可能な投資戦略を設定してください。", "Please do not enter a space in result code": "結果コードにスペースを入力しないでください", "Please ensure at least one record is entered in Factor Definition or Ratetable Replacement.": "", "Please fill in the mandatory items before submitting.": "必須項目を入力してから送信してください。", "Please first define the Object Category and Object under Configuration Center → Tenant Data Configuration → Basic Rule → Object Category & Object.": "", "Please input": "入力してください", "Please input at least one field.": "少なくとも1つの項目を入力してください。", "Please input at least three characters.": "3文字以上入力してください！", "Please input condition": "条件を入力してください", "Please input end number": "最後の番号数を入力してください", "Please input expression": "式を入力してください", "Please input in this format: 'HH:mm'. e.g. 12:00": "", "Please input liability handling.": "補償内容処理方法を入力してください。", "Please input number": "番号を入力してください", "Please input or paste the URL here": "ここにURLを入力または貼り付けてください。", "Please input positive value": "正の値を入力してください", "Please input result": "結果を入力してください", "Please input the range in [{{minimum}}, {{maximum}}]": "", "Please input variable": "変数を入力してください", "Please input variable and start number": "変数と初期値を入力してください", "Please input Vehicle DataBase Name": "車両データベース名を入力してください。", "Please make sure that both medical bill type and medical bill item have been configured.": "医療費タイプと医療費明細の両方が設定してください", "Please make sure you define how to calculate this middle factor when using in formula configuration.": "計算式にこの中間ファクターの計算方法を定義したことを確認してください。", "Please note: After premium holiday, cash rider will be terminated from next premium due date (of first unpaid bill), while unit deducting rider (if any) will keep effecitve.": "ご注意：保険料支払い一時停止の後、現金特約は次回の保険料払込応当日（最初の未払請求日）から終了しますが、単位控除特約（ある場合）は有効に存続します。", "Please note: Current detail information of Recurring Single Top Up will be overwritten by Premium Frequency & Installment Detail of Planned Premium.": "注意：現在の継続的な単一追加入金の詳細情報は、計画された保険料の頻度と分割払いの詳細情報によって上書きされます", "Please note: There is no refund after termination and policy is not allowed to be reinstated.If there are any active riders, they will also be terminated.": "ご注意：契約終了後の払戻や復活はできません。有効な特約がある場合、その特約も契約終了となります。", "please retain at least two branches": "少なくとも2つのブランチを保持してください", "Please save the changes to cancellation reason first.": "", "Please save the changes to cancellation type first.": "", "please select": "選択してください", "Please select": "選択してください", "Please select at least one advance criteria for ANFO setting.": "不可没収オプションをデフォルトにするには少なくとも一つの条件を添付する必要があります。", "Please select at least one related liability.": "関連する責任を少なくとも一つ選択してください。", "Please select at least two fields.": "少なくとも2つのフィールドを選択してください。", "Please select effective date": "", "Please select Formula Code": "計算式コードを選択してください", "Please select formula existed in steps": "ステップに存在する計算式を選択してください", "Please select language": "言語を選択してください。", "Please select one record at least!": "少なくとも1つのレコードを選択してください！", "Please select one reocrd": "レコードを選択してください", "Please select parameter": "", "Please select ratetable existed in steps": "ステップに存在するレートテーブルを選択してください", "Please select related Formula for POS": "POS関連の計算式を選択してください", "Please select the sections to export:": "", "Please select the sections to import:": "", "Please select to:": "", "Please set the deduction sequence if corresponding premium type is insufficient for charge deduction.": "該当する保険料タイプで支払控除が不十分な場合を防ぐため、控除順序を設定してください。", "Please submit change before test": "提出前に、テストしてください", "Please tick the additional condition that should be met before enjoy no lapse guarantee": "猶予期間を満たす追加条件を選択してください", "please upload excel or csv": "ExcelまたはCSVファイルをアップロードしてください", "Please upload the file in XLSX or XLX format!": "XLSXまたはXLXファイルをアップロードしてください！", "Please* configure at least one set of name rules.": "少なくとも 1 セットの名前設定ルールを設定してください。", "Policy Currency": "契約通貨", "Policy Effective Without Collection (NB)": "決済しないまま契約計上 (NB)", "Policy Effective Without Collection (Renewal)": "決済しないまま契約計上 (Renewal)", "Policy Enumerated Values": "契約選択肢", "Policy Holder Info": "契約者情報", "Policy Illustration": "申込の利益デモンストレーション", "Policy Info": "契約情報", "Policy Loan": "保険契約者貸付", "Policy Month": "契約経過月数", "Policy No.": "加入者番号", "Policy Type": "契約種類", "Policy will be lapsed after grace period ends.": "猶予期間終了後、契約が失効されます", "Policy will be terminated after grace period ends.": "猶予期間終了後、契約が消滅されます", "Policyholder Age Comparison": "保険加入者年齢比較", "Policyholder Elements Relationship Matrix": "契約者付加関係マトリックス", "Policyholder’s Age Range(NB)": "契約者の年齢範囲（新規契約）", "Policyholder’s Age Range(Renewal)": "契約者の年齢範囲（新規契約）", "PolicyStatusEnum.POLICY_EFFECT": "有効", "policySuspense（Mock）": "仮受金（<PERSON><PERSON>）", "Portfolio Rebalancing": "リバランス", "POS": "契約変更", "POS additional/refund net premium = (product net premium after POS - product net premium before) * unearned period / coverage period": "", "POS Item": "異動項目", "Post-sale Illustration": "ポストセールスの利益デモンストレーション", "Post-sale Illustration Trigger Point": "ポストセールスのイラストレーショントリガーポイント", "Pre-definition": "", "Pre-set Data Setting": "", "Premium Adjustment": "保険料調整", "Premium Adjustment Level": "保険料調整レベル", "Premium Adjustment Type": "保険料調整タイプ", "Premium Agreement": "保険料設定", "Premium Calculation Method": "保険料計算方法", "Premium Discount": "保険料割引", "Premium Discount Type": "保険料割引種類", "Premium Due Date Rule": "契約応当日ルール", "Premium End Date Calculation Rule": "払込満了日計算ルール", "Premium Frequency": "保険料支払頻度", "Premium Holiday": "保険料収納一時停止", "Premium Investment Strategy": "プレミアム投資戦略", "Premium Limit": "保険料設定", "Premium Limit Type": "保険料制限タイプ", "Premium Notice Date Compare with Due Date": "保険料徴収通知時間（始期応当日との比較）", "Premium Notice Rule": "保険料徴収通知ルール", "Premium Notice Rule cannot be repeated": "保険料通知ルールは重複できません", "Premium Notice Rule(Legacy)": "", "Premium Period": "保険料払込期間", "Premium Period & Installment": "保険料払込方法約定", "Premium Period Type": "保険料払込期間タイプ", "Premium Period Value Type": "設定方法", "Premium Period(title)": "保険料払込期間約定", "Premium Type": "保険料タイプ", "Premium Type Deduction Method": "保険料タイプ控除方法", "Premium Type Deduction Sequence": "控除される保険料のタイプのシーケンス", "Premium Unpaid Handling": "未付保费处理方式", "Preview Ratetable": "", "Previous": "", "Pro Rata Calculation Basis": "", "Product": "商品", "Product & Liability Relationship Matrix": "保険商品&補償項目付加関係", "Product Agreement": "保険商品約定", "Product Category": "保険商品カテゴリー", "Product Center": "商品センター", "Product Center - Product Management": "", "Product Class": "製品クラス", "Product Code": "保険商品コード", "Product Code already exists": "商品コードは既に存在しました", "PRODUCT CONFIGURATION": "", "Product Enumerated Values": "製品の列挙値", "Product Info": "商品情報", "Product Label": "商品ラベル", "Product Label Configuration": "商品ラベル設定", "Product Liability": "保険商品補償内容", "Product Line": "商品ライン", "Product Management": "商品管理", "Product Name": "保険商品名", "Product Overdue Status": "商品延滞ステータス", "PRODUCT RELATED CONFIGURATION": "", "Product SA": "商品保険金額", "Product SA Priority": "", "Product SA Setting": "商品保険金設定", "Product Status": "保険商品ステータス", "Product Template": "商品テンプレート", "Product V2": "", "Product Validation": "商品検証", "products": "商品", "Progression Table": "進行テーブル", "Property Bill Item": "財産請求項目", "Property Bill Object": "財産請求対象", "Property Bill Objects": "財産請求対象", "Property Claim Info": "財産保険金請求", "Publish": "有効化", "Publish Successfully": "公開成功", "Published": "", "Quick Menu": "クリックメニュー", "Range": "範囲", "Rate Formula Code": "レート計算式コード", "Rate Range": "レート範囲", "Rate Source": "レートソース", "Rate Table": "レートテーブル", "Ratetable": "料率テーブル", "Ratetable Category": "料率テーブル種類", "Ratetable Code": "レートテーブルコード", "RateTable Code": "レートテーブルコード", "Ratetable Code could not start with \"G_\".": "レートテーブルコードは\"G_\"で始まることはできません。", "Ratetable Code should not include \"_ProductCorrelationMatrix、_PeriodLimitMatrix、G_BenefitScheduleMatrix\".": "レートテーブルコードに\"_ProductCorrelationMatrix, _PeriodLimitMatrix, G_BenefitScheduleMatrix \"を含めることはできません。", "Ratetable Description": "", "Ratetable Introduction": "レートテーブル説明", "Ratetable Management": "レートテーブル管理", "Ratetable Name": "レートテーブル名", "Ratetable Replacement": "レートテーブル交換", "Ratetable Sub Category": "料率のサブ種類", "ratetables": "", "Re-Define Effective Date Rule": "有効日ルールの再定義", "Rebalance Date": "リバランス日時", "Rebalance Frequency": "リバランス頻度", "Recalculate SA or Premium": "保険金また保険料の再計算要否", "Recalculate SA or Premium Except for Change Reason": "変更理由を除いて保険金 または保険料を再計算する", "record": "件", "record(s) has been uploaded successfully.": "アップロード成功", "records": "件", "Recount Result": "再計算の結果", "Recurring Single Top Up Detail": "継続的な単一追加入金の詳細", "Reduce cash value to zero": "現金価値をゼロに減少させる", "Refund Confirmed Premium from": "確定保険料の返金元", "Refund Formula": "返戻計算式", "Regional Function Management": "", "Regular Top Up Frequency": "定期増額の払込頻度", "Regular Top Up Frequency configure at least one": "少なくとも１つの定期増額払込頻度を設定してください", "Regular Top Up payment needs to use separated frequency": "定期増額は違う払込頻度を設定する必要があります", "Regular withdrawal allowed": "定期減額を許可します", "Reinstatement Period": "復活期間", "Reinstatement Period Unit": "復活期間単位", "Related Stacks": "関連スタック", "Related Stacks within the Same Order": "同じ順番での関連スタック", "Related Termination": "関連終了", "Related to Bone Fracture/Dislocation Item and Bone Fracture/Dislocation Level": "骨折・脱臼項目および骨折・脱臼レベルに関連する", "Relation Policy Info": "関連契約情報", "Relation Policy Template": "リレーションポリシーテンプレート", "Relational Type": "続柄タイプ", "Relationship": "続柄", "Rename": "名前変更", "Renewable or Not": "自動継続可否", "Renewal": "自動継続", "Renewal Agreement": "自動継続約定", "Renewal Extraction Period": "更新データの抽出期間", "Renewal Grace Period": "自動継続猶予期間", "Renewal Policy Effective Date Rule": "自動継続契約の契約始期日ルール", "Renewal Policy Effective Without Collection": "徴収無の有効契約更新", "Renewal Proposal Submit Date": "", "Renewal Reminder Date Compare to Policy Expiry Date": "更新通知日と契約の有効期間と比較", "Renewal Reminder Rule": "更新通知ルール", "Renewal Reminder Rule cannot be repeated": "更新通知のルールは重複することができません", "Renewal Together": "一緒に更新", "Replacement Ratetable": "交換レートテーブル", "Replacement Ratetable is required": "交換レートテーブルが必要です", "Required": "必須", "Required / Optional": "オプション/必須", "Reserve Provision trigger point": "", "Reserve Provision Trigger Point": "責任準備金抽出トリガーポイント", "Reset": "リセット", "Result Code": "結果コード", "Result Details": "", "Retirement Age (Insured)": "退休年龄（被保人）", "Retirement Option": "退職計画", "Retirement Option Start Date": "退職計画の開始日", "Reversionary Bonus": "配当金", "Reversionary Bonus Allowed": "配当金有無", "Rider Code": "", "Rider Handling After ETI": "延期後特約の処理", "Rider Handling After Paid Up": "払済後特約の処理", "Rider Handling After Triggering Premium Holiday": "保険料支払い一時停止後特約の処理方法", "Rider Name": "特約名", "Rider Name(Rider1)": "特約名（特約１）", "Rider Name(Rider2)": "特約名（特約２）", "Rider Relationship Matrix": "特約付加関係マトリックス", "Rider Relationship with Main Product": "特約と主契約の関係", "Rider Type": "特約タイプ", "Risk Category": "リスクカテゴリ", "Risk SA for Risk Category": "リスクカテゴリの保険金額", "Risk SA for Risk Sub-category": "リスクサブカテゴリの保険金額", "Risk Sub-category": "リスクサブカテゴリ", "Room Level": "病室等級", "Round": "四捨五入", "Rounding Digits": "四捨五入桁数", "Rounding Type": "四捨五入タイプ", "Rule Category": "ルールカテゴリ", "Rule Code": "ルールコード", "Rule Name": "", "Rule/Rule Set": "", "Rule/Rule Set Code": "ルール／ルールセットコード", "Rule/Rule Set Name": "ルール/ルールセット名", "Rule1：Corresponding to effective date or period end date": "ルール1：契約日に対応する日、または期末日", "Rule2：Corresponding to effective date or next date of period end date": "ルール2：契約日に対応する日、または期末日の翌日", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "ルール3：対応した契約日または満期日は2月28日を期間終了日として確定します", "rules": "", "SA Agreement": "保険金額約定", "SA by Layer": "レイヤー別保険金額", "SA Calculation Method": "保険金額計算方法", "SA Decreasing Order": "保険金額の降順", "SA Limit": "保険金額制限", "SA Limit Type": "保険金額制限種類", "SA Multiplier": "保険金乗数", "SA Unit": "保険金額単位", "SA Unit Content": "各単位の保険金額", "SA Valuation": "", "SA Valuations": "", "Same Premium Adjustment strategy for All Goods": "すべての商品に同じ保険料調整戦略を適用します", "Same TB type Formula has duplicate record. Please check.": "同じタイプの消滅配当金の計算式は既にあります、確認してください。", "Save": "保存", "Save as Draft": "", "Save Illustration Sequence": "デモンストレーションシーケンス保存", "Save Sort": "ソート順保存", "Save successfully": "保存完了", "Save Successfully": "保存完了", "Saved successfully": "保存完了", "Scenario Code": "シナリオコード", "Scenario Code & Scenario Name": "シナリオコード＆シナリオ名", "Scenario code can not be duplicated": "シナリオコードは重複できません", "Scenario Name": "シナリオ名", "Scheduled Arrival Time": "到着予定時刻", "Scheduled Departure Time": "出発予定時刻", "Search": "検索", "Search by Policy": "契約内容で検索", "Search by Transportation": "交通で検索", "Search Result": "検索結果", "Second Date": "二番目の日付", "Select": "選択", "Select All": "", "Select Benefit Factors": "利益要素選択", "Select Cancellation Type": "", "Select Claim Stack(Template)": "クレームスタック（テンプレート）を選択", "Select Component": "", "Select Factor": "", "Select Formula": "サブ計算式", "Select Liability": "補償内容選択", "Select Parameter": "パラメータを選択", "Select Ratetable": "", "Select the enumeration value you want.": "列挙値を選択してください", "Select the Format you want": "希望のフォーマットを選択してください", "Selection": "", "Sequence": "シーケンス", "Serial No.": "シリアル番号", "Service Fee Clawback": "サービス料の回収", "Set": "セット", "Set Default Time": "", "Set Fixed Time": "", "Set Name": "セット名", "Set1": "氏名セット1", "Set2": "氏名セット2", "Set3": "氏名セット3", "Severity": "重大度", "Severity Definition": "深刻さの定義", "Sex": "", "Short Term Premium": "短期契約保険料", "Short Term Premium Calculation Method": "短期契約保険料の計算方法", "Single Top Up Type": "一時増額タイプ", "Source": "ソース", "Specify the calculation order": "", "Stack Code": "スタックコード", "Stack Component Name": "", "Stack Name": "スタック名称", "Stack Template Code": "", "Stack Template Name": "", "Stack Type": "スタックタイプ", "Stack Unit": "スタック単位", "Stack Unit Type": "", "Stack Value": "スタックの値", "Stack Value Type": "値タイプ", "Stacks Related to Special Agreements": "スタックと特約の紐づけ", "Standard Premium": "標準保険料", "Start date": "開始日", "Start Date": "開始日", "Starting Scenario": "初期シナリオ", "Status": "状態", "Step": "ステップ", "Step 1": "Step 1", "Step 1 Download Test Data Template": "ステップ１：テストデータテンプレートをダウンロード", "Step 1: Input Value for Factors in the Page": "Step 1: 本ページに因子の値を入力してください", "Step 2": "Step 2", "Step 2 Upload Test Data File": "ステップ２：テストデータファイルをアップロード", "Step 2: Input Value for Factors in the Excel": "Step 2: Excelで因子の値を入力してください", "Step 3 Download Test Result": "ステップ３：テスト結果をダウンロード", "Step 3: Testing Result": "Step 3: テスト結果", "Step result is duplicated with paramater, please modify.As follows": "計算式にパラメータは重複するから、修正してください。", "Strategy": "戦略", "Strategy Code": "ストラテジーコード", "Strategy Name": "ストラテジー名称", "Sub Formula": "子計算式", "Sub POS Effective Date": "サブ契約変更の発効日", "Submit": "送信", "Submit Product": "商品登録", "Submit successfully": "送信しました", "Submit Successfully": "送信しました", "Summary": "", "Support entering a regular expression to control the input rules for Account Number by each Bank. For example, if Account Number should only allow 10 to 12 digits(number), you can configure it as ^\\d{10,12}$ or ^[0-9]{10,12}$.": "", "support list calculation including +,-,*,/": "“+,-,*,/”を含むリスト計算をサポートします。", "Surgery": "手術", "Surgery Configuration Method": "手術設定方法", "Surgery Customized Data Details": "", "Surgery Level": "手術レベル", "Surgery Level cannot be empty": "手術レベルを設定してください", "Surgery Tag1 cannot be empty": "", "Surgery Tag2 cannot be empty": "", "Survival Benefit": "生存給付金", "Survival Benefit Leading Days": "N日前に生存給付金を作成する", "Switching the method of diagnosis configuration will auto clear the configured data.": "診断構成の方法を切り替えると、元のデータが自動的にクリアされます。", "Switching the method of surgery configuration will auto clear the configured surgery data.": "手術設定を変更する場合、既存な手術明細データを自動的に削除されます。", "System Data": "", "System Data Value": "", "System error": "システムエラー", "System is abnormal, please try again later.": "システムエラーが発生しました。しばらくお待ちください", "System pre-set data": "システムプリセットデータ", "System support to configure linkage between following fields": "次のフィールド間のカスケード関係を設定することができます", "System will automatically conduct batch test when test data is uploaded successfully.": "テストデータのアップロードが成功すると、システムは自動的にバッチテストを実施します。", "System will generate a test data template automatically based on parameters used in the formula.": "システムは、計算式で使用されるパラメータに基づき、テストデータテンプレートを自動的に生成します。", "tables": "", "Tag Definition": "タグ定義", "Tag definition cannot be empty": "", "Tag1": "タグ１", "Tag2": "タグ２", "Target Currency": "換算先通貨", "Target Date": "目標期日", "Tax Collection Rule": "税金の集金ルール", "Tax Rate": "税金率", "Tax Setting": "税金設定", "Tax Type": "税金タイプ", "Tax Value Type": "税金計算タイプ", "TB Payable for Claim from": "保険金による消滅配当金", "TB Payable for Surrender from": "解約による消滅配当金", "TB Type": "消滅配当種類", "Template change will influence object and liability setup.": "テンプレートが更新されると、補償対象と補償内容に影響を与えます。", "Template Code": "", "Template Code / Name": "", "Template Content": "", "Template Correlation Info": "テンプレート関連情報", "Template Description": "テンプレート詳細", "Template Name": "テンプレート名", "Template Status": "", "tenant": "テナント", "Tenant Data Configuration": "テナントデータ設定", "Terminal Bonus": "消滅配当", "Terminal Bonus Indicator": "終了配当インジケーター", "Terminate Liability After Claim Waiver": "完全保険金請求後の免除責任終了", "Terminate Policy After Terminate Liability": "責任終了後にポリシーを終了", "Terminate Product After Terminate Liability": "保険金請求後に契約を終了する", "Terminate Related Liability": "関連する賠償責任の終了", "Terminated Reason": "無効理由", "Test": "テスト", "Test Result": "テスト結果", "The “DCA frequency” table should at least have one record.": "ドルコスト平均法頻度表には少なくとも1つのレコードが含まれている必要があります。", "The automatic fund rebalancing will only occur when the portfolio variance from the pre-specified premium allocationt exceeds certain range.": "特別勘定の自動的なリバランスは、あらかじめ定めた保険料配分からのポートフォリオの乖離が一定の範囲を超えた場合にのみ行われます。", "The code could only contain letters, numbers, and underscores (_).": "", "The component code could only contain letters, numbers, and underscores (_).": "", "The content of the page has been modified, are you sure you want to leave": "ページの内容が変更されています。このままページを離れてもよろしいですか？", "The content of the page has been modified, are you sure you want to leave without saving?": "", "The coverage period agreement will be deleted": "保険期間約定は削除されます", "The current configuration cannot be modified after submission, confirm to continue?": "送信後に現在の設定を変更できません、続行してもよろしいですか?", "The fixed due date refers to the same day of each month. If certain month doesn't have the date, it will be the last day of that month.": "毎月の同じ日を固定期日として指定します。 ある月に指定日がない場合は、その月の最終日となります。", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "毎月の同じ日を固定期日として指定します。 ある月に指定日がない場合は、その月の最終日に期日を移動します", "The following benefit option(s) have been configured. The uploaded template will overwrite the data. Please confirm to proceed.": "以下の受益オプションが設定されています。アップロードされたテンプレートがデータを上書きします。続行する場合は確認してください。", "The following calculation factors are not found in structure.": "下記の計算要素は構造では見つかりませんでした", "The following products have used this formula. Editing formula and the following product will be affected. Please check before change:": "以下の製品はこの計算式を使用しています。計算式の編集は以下の製品に影響を与えますので、変更前に必ず確認してください。", "The formula code could only contain letters, numbers, and underscores (_).": "数式コードには、文字、数字、および下線（_）のみを含めることができます。", "The General category can only be referenced in formula definitions and is not limited to any specific formula category.": "", "The indicator of include or exclude is mandatory for diagnosis code configuration.": "包括/除外というインジケータは、診断の設定に必要です。", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "設定された月数は自然月です。例：11/16 00:00:00~12/15 23:59:59；12/16 00:00:00~1/15 23:59:59", "The name format will follow the sequence below, the sequence can be manully adjusted.": "氏名のフォーマットは下記の順番で並べます、順番を調整することができます。", "The object configured in {{liabilityId}} {{liabilityName}} does not exist in the product.": "", "The operator used for exponentiation. It raises a number to the power of another number. For example: 2*4 = 2*2*2*2 = 16": "", "The policy of GroupEB does not support the process of deactivating the insured": "", "The product code could only contain letters, numbers, and underscores (_).": "商品コードには、文字、数字、および下線（_）のみを含めることができます。", "The ratetable code could only contain letters, numbers, and underscores (_).": "レートテーブルコードには、アルファベット、数字、アンダースコア（_）しか使用できません。", "The rateTable is used and affected in several places below. Please confirm changes.": "レートテーブルは以下の数カ所で使用されており、影響を受けますので、変更する前にご確認してください。", "The result of formula is repeated naming(case insensitive)": "計算式の名称は既に存在しました", "The second value should be greater than or equal to the first value.": "2番目の値は1番目の値以上でなければなりません。", "The second value should greater than the first value": "2番目は1番目より大きい値を入力してください", "The selected formula has been updated. Please reselect.": "", "The selected rate table has been updated. Please reselect.": "", "The stack code could only contain letters, numbers, and underscores (_).": "スタックコードには、アルファベット、数字、アンダースコア（_）しか使用できません。", "The table contains an extensive amount of data and cannot be previewed here. Please use the download button to obtain and review the file locally": "", "The template code could only contain letters, numbers, and underscores (_).": "", "The template has been referenced and cannot be deleted.": "このテンプレートは参照されているため、削除できません。", "The template has been referenced and cannot be edited.": "このテンプレートは参照されているため、編集できません。", "The template has been used by following products. Please confirm if any deletion is required.": "テンプレートは以下の製品で使用されています。削除が必要な場合はご確認ください。\n{{productCodes}}", "The template has been used by following products. Please confirm if any modification is required.": "テンプレートは以下の商品で使用されています。修正が必要な場合はご確認ください。\n{{productCodes}}", "The Usage Premium Start Date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "使用量に基づく保険料の開始日は毎月のX日とします。特定の月に指定日がない場合は、その月の最終日に期日を移動します。", "The value of the last step must be 'final', and the value of other steps cannot be 'final', 'final' is case insensitive": "最後のステップの値は「final」である必要があり、他のステップの値は「final」にすることはできません。「final」では大文字と小文字は区別されません。", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "", "There no found.": "", "Third Party Agreement": "第三者契約", "This agreement is replaced by \"Claim Eligibility and Policy Matching\". Please use the new one if needed.": "この項目はすでに「保険金請求資格と契約マッチング」に置き換えられました。 必要に応じて新しい設定項目を使用してください。", "This component has already been used and cannot be modified or deleted.": "", "This conditional factor's value already exists, please modify it.": "", "This configuration can only be configured once, and cannot be modified after the configuration is completed.": "この項目は1回のみ設定できますので、設定を保存しますと二度と更新できません。", "This formula is referenced as a sub-formula in below. Please update the following parent formulas after saving this formula.": "", "This Illustration Scenario is already in use under a Benefit Illustration Item. Please disassociate it from the Benefit Illustration Item first.": "この デモンストレーションシナリオ は デモンストレーションシ項目 で使用されています。まず、それを デモンストレーションシ項目から切り離してください。", "This insurable interest has been used in the following package: [{{packages}}], please delete insurable interest in Package first.": "この被保険利益は商品組合[{{packages}}]の中に使用されております、先にこの商品組合から被保険利益を削除してください。", "This liability has been used in the following package: [{{packages}}], please delete liability in Package first.": "この補償内容はすでに [{{packages}}]という商品組合せで使用されていますので、まずこの補償内容を上記の商品組合せから削除してください。", "This product is read only.": "", "This ratetable is relied on": "", "This record has been deleted from premium period configuration. Please manually delete this record to ensure data consistency.": "このレコードはすでに保険料期間コンフィグレーションから削除されました。データの整合性を確保するため、手動でこのレコードを削除してください。", "This record was configured based on old version. Currently, viewing and editing are not supported.": "このレコードは古いバージョンに基づいて設定されていましたので、現時点では照会と編集はできません。", "This rider product has been used in the following package: [{{packages}}], please delete rider product in Package first.": "この特約商品はすでに [{{packages}}]という商品組合せで使用されていますので、まずこの特約商品を上記の商品組合せから削除してください。", "This section configures the system rules when installment premium is not paid on time.": "このセクションで更新後の保険料が期限内に支払われない場合のシステムの処理ルールを設定する。", "This section configures the system rules when the total investment value (policy value) is insufficent to pay charges.": "このセクションで契約口座残高（契約の現金価値）が契約関連費用に支払われない場合のシステムの処理ルールを設定する。", "This section is required for regular paid product. No need for single premium product.": "このセクションの設定は、分割払いの商品だけに有効です。 一括払の商品には設定する必要がありません。", "Time Zone": "タイムゾーン", "Times Type": "回数タイプ", "Tip1： Support calculation sign including": "ヒント1：以下の計算記号を使える", "Tip2： Support embedded calculation method including": "ヒント2：以下の計算方法を使える", "Tip3： Date Format only accept": "ヒント3：日付フォーマットのみ受け入れる", "Titile": "タイトル", "Tiv（Mock）": "Tiv（モック）", "to": "まで", "To define the premium allocation strategy.": "保険料配分方法を設定する", "To define whether claim reserve equals 0 is allowed for claim acceptance.": "支払備金が0にすることが可能か", "To reduce duplicate configurations, for POS Items applying to the entire policy, please go to [Package Management - Policy Change] section.": "", "Top Up Frequency": "定期増額頻度", "Trace": "トレース", "Transaction Type": "取引タイプ", "Transportation Data": "交通データ管理", "Transportation Date": "出発日付", "Transportation Facility": "交通施設", "Transportation No.": "交通データ番号", "Transportation Number": "交通データ番号", "Transportation Status": "交通状態", "Transportation Type": "交通輸送タイプ", "Treatment Condition": "治療状況", "Trigger Additional Maturity Benefit": "年金を追加", "Trigger Cash Refund": "現金で返戻する", "Trigger Type": "トリガー種類", "Try a different keyword.": "", "Type": "タイプ", "Type of Amount Per Period": "期間ごとの金額種類", "Type of Loss": "", "Uncollected Premium Deduction": "未払い保険料引き落とし", "Under this scenario, the policy Lapse process will be trigger when TIV is un-sufficient. Please make related configuration at \"Handling of TIV Insufficient\" section.": "現在の設定では、契約の投資口座の残高が不足すると、契約失効をトリガーします。 「投資口座残高不足処理」セクションで後の設定を完了してください。", "Under this scenario, the policy will keep effective when enter premium holiday. And once the allowed premium holiday period is used up, system will lapse policy when premium is unpaid.": "「現在の設定では、払込一時停止期間に入っても契約は有効状態のままです。払込一時停止期間が終了し、更新後の保険料がまだ支払われていない場合、システムは契約を消滅します。", "Underwriting Enumerated Values": "引受の列挙値", "Unit Type": "", "Unpaid Bill Cancellation Rule": "未払請求取消ルール", "Upload check failed, please download the error file to check!": "アップロードに失敗しました。エラーファイルをダウンロードしてチェックしてください。", "Upload Data": "", "Upload Failed": "アップロード失敗", "Upload File": "", "Upload File Infomation": "アップロードファイル確認", "Upload Files": "", "Upload Ratetable": "レートテーブルをアップロード", "Upload Success": "アップロードしました", "Upload successfully": "アップロード完了", "Upload Successfully": "アップロード完了", "Uploading, please refresh the page to check the results later.": "データアップロード中、画面リフレッシュにて結果が確認できます。", "URL": "URL", "Usage Based": "", "Usage Based Insurance Agreement": "利用ベース保険規約", "Usage Based Product": "利用ベース商品", "Usage Premium Start Date": "利用保険料開始日", "Use customized data": "ユーザーカスタマイズデータ", "Using Insurable Interest": "", "UW Risk SA Setting": "保険金額通算上限設定（顧客単位）", "Validation Details": "検証詳細", "value": "", "Value": "値", "Value Assignment": "", "Value Type": "設定方法", "Vehicle Claim Info": "車両の事故情報", "Vehicle Database Model": "車両データベースモード", "Vehicle Database Model needs to be configured as “Cascade” if there exists multiple vehicle databases.": "複数の車両データベースが存在する場合、車両データベースモードを「連鎖」と設定してください。", "Vehicle Database Name": "車両データベース名", "Vehicle Database Name has already existed.": "同じ車両データベース名存在します。", "Vehicle Databases": "車両データベース", "Vehicle Elements Relationship Matrix": "車両要素関係マトリック", "Vehicle Market Value": "車両市場価格", "Version": "バージョン", "Version Time": "", "Vesting Agreement": "ベスティング契約", "View": "照会", "View Claim Stack Template": "クレームスタックテンプレート照会", "View Component": "", "View Detail": "", "View Enumerated Fields Dependency": "", "View Formula": "公式照会", "View Liability": "補償内容照会", "View My Creations": "", "View Ratetable": "保険料率照会", "View Reference": "", "View Structure": "", "VIRTUAL MAIN": "", "Virtual Main Benefit": "バーチャル主契約", "Wait Until Grace Period Expires": "猶予期間終了まで", "Waiting Period": "免責期間", "Waiting Period of New Business": "新規契約の免責期間", "Waiting Period of Reinstatement": "復活待ち", "Warning Notification": "", "Week": "週", "When add a record for Liability A and Liability B as 1,000": "保険料1,000の補償内容Aと補償内容Bを追加する場合", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month)": "無料月が設定されている場合、契約発効日から開始しない可能性があります。 無料月はここで定義された契約月Xから計算されます。 (少なくとも第1契約月から）", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month) ": "無料月が設定されている場合、契約発効日から開始しない可能性があります。 無料月はここで定義された契約月Xから計算されます。 (少なくとも第1契約月から）", "When customer choose pre-defined investment strategy, the fund appointment rate for both premium direction and portfolio rebalance will follow the configuration on strategy.": "当顾客选择预先定义的投资策略，保费和投资组合再平衡的基金分配率都将遵循策略的配置。", "When issue a policy, save illustration data in the policy.": "契約を発行する際に、イラストレーションデータを契約に保存してください。", "When it is closed,  system will buy target fund chosen by customer directly after policy issuance. No delay of the investment.": "スイッチをオフにすると、加入成功直後に遅延投資ではなく、お客さまが選択された資金配分比率に応じて投資を行います。", "When premium comes in, system will fill in the installment premium due": "保険料受領後、下記方法で毎回保険料を配分する", "Whether After Campaign Discount": "キャンペーン割引後に計算するか", "Whether to use premium adjustment": "保険料調整を使用するかどうか", "With Extra Premium": "追加保険料", "With Guarantee Period": "保証期間付き", "With No Lapse Guarantee": "失効せず保証付き", "Within No Lapse Guarantee": "失効せず保証期間内", "Work Time": "", "Year": "年", "Year(s)": "年間", "yes": "はい", "Yes，Charge Amount is": "はい、スイッチ料は", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEGタイプのファイルのみを許可します。", "You can only upload xls, xlsx": "xls, xlsxのみアップロードできます。", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "アップロード可能なファイいるの拡張子はxls, xlsx, doc, docx, pdf, jpg, pngのみです。", "You can only upload XSL": "", "You don't have to configure it if you don't need it": "必要なければ設定する必要はありません", "You have unsubmitted information. Do you want to discard it?": "登録しない情報があり、削除してよろしいですか?", "You haven,t configured your bank information yet": ""}