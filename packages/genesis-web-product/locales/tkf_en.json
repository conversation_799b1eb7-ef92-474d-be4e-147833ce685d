{"- -": "--", "--": "--", "'Charges Deducted From Policy Value' includes the following types of charges: Policy Admin Fee, Account Management Fee，ETF Broker Fee, ETF Management Fee, Premium Holiday Charge, Cost of Insurance, Rider Premium, Partial Surrender Charge": "", "{{filename}} will be uploaded, the data is {{count}} records, please make sure to upload.": "", "%": "", "• Accumulate each liability SA -> Product SA is 2,000": "", "• Accumulate federated liability SA -> Product SA is 1,000": "", "Abbreviation": "", "Accelerating/Restoration": "Accelerating/Restoration", "Accident Index Formula": "", "Account Lockout Period": "", "Account Lockout Period (minimum value is 15 min)": "", "Accumulated Fund Dividend (Mock)": "", "Accumulated ILPBonus (Mock)": "", "Accumulated ILPCharges (Mock)": "", "Accumulated POS Withdrawal (Mock)": "", "Accumulated Premium (Mock)": "Accumulated Net Contribution (Mock)", "Accumulated Withdrawal (Mock)": "", "Actual Arrival Time": "", "Actual Departure Time": "", "Add": "Add", "Add a Condition": "", "Add a set of conditions": "Add a set of conditions", "Add Amount": "", "Add Component": "", "Add Condition": "", "Add Enumerated Fields Dependency": "", "Add Factor": "", "Add Formula": "Add Formula", "Add New": "Add New", "Add New as the Sub Level": "Add New as the Sub Level", "Add New Charge": "Add New Charge", "Add New Claim Stack Template": "", "Add New Condition": "", "Add New Formula": "Add New Formula", "Add New Liability": "Add New Liability", "Add New Product": "Add New Product", "Add New Ratetable": "Add New Ratetable", "Add New Risk Sub Category": "", "Add New Set": "", "Add New Version": "", "Add Ratetable": "", "Add Strategy": "Add Strategy", "Add successfully": "Add successfully", "Add Transportation Data": "", "Additional Clause": "Additional Clause", "Additional Clause Name": "Addtional Clause Name", "Additional Condition Factor": "", "Additional Condition Type": "", "Additional Grace Period": "Additional Grace Period", "Additional MB Formula": "", "Additional Participation Conditions": "", "Additional Trigger Conditions": "", "Addresses at the selected level and below can be selected in the drop-down box and support free editing.": "", "Adjust to 1st if Effective Day Not in Expiry Month": "", "Adjusted Market Value Floating Ratio": "Adjusted Market Value Floating Ratio", "Advanced Configuration": "Advanced Configuration", "After": "", "After (days)": "", "After ETI": "", "After Paid Up": "", "After(Days)": "After(Days)", "Age": "Age", "Age Calculation": "Age Calculation", "Age Calculation Basis": "Age Calculation Basis", "Age Validation": "Age Validation", "Age Validation Type": "", "ageCalcBasis": "", "Agent Info": "", "All Creations": "", "Allow Auto ETI": "", "Allow Auto ETI Tooltip": "", "Allow Auto Paid Up": "", "Allow Auto Paid Up Tooltip": "", "Allow Automatic Premium Loan": "", "Allow claim reserve equal 0": "Allow claim reserve equal 0", "Allow Claim Reserve Equal 0": "", "Allow collect outstanding premium after policy termination": "", "Allow CPF Payment": "", "Allow Flexible Premium Payment": "Allow Flexible Contribution Payment", "Allow Minimum Investment Period": "", "Allow Minimum Reinstatement?": "", "Allow Parital APL": "", "Allow Partial Filling the ILP Premium": "", "Allow Policy Loan": "Allow Certificate Loan", "Allow POS Effective Without Collection": "Allow POS Effective Without Collection", "Allow Premium Decrease": "", "Allow Premium Increase": "", "Allow Renewal": "", "Allow SA Decrease": "Allow SA Decrease", "Allow SA Increase": "Allow SA Increase", "Allow to apply waive the premium liability": "", "allow to change premium": "", "allow to change SA": "", "Allow to Waive Interest": "", "allow to withdraw CV": "", "Allow Uncollected Premium Deduction from POS Refund": "Allow Uncollected Premium Deduction from POS Refund", "Allow Vesting": "", "Allowance Agreement": "Allowance Agreement", "Allowance Amount": "Allowance Amount", "Allowance Amount Type": "", "Allowance Configuration": "", "Allowance Type": "Allowance Type", "Allowance Unit Type": "Allowance Unit Type", "Allowance Unit Value": "Allowance Unit Value", "Allowance Value Type": "Allowance Value Type", "Allowed Investment Strategy": "", "Allowed Range for Admission Time Compare to Incident Date (Hospitalization)": "Allowed Range for Admission Time Compare to Incident Date (Hospitalization)", "Allowed Range for Admission Time to Incident Date (Medical Bill)": "Allowed Range for Admission Time to Incident Date (Medical Bill)", "Allowed Range for Death Date Compare to Incident Date": "", "Allowed Range for End Date Compare to Incident Date(Allowance)": "", "Allowed Range for Outpatient Date to Incident Date (Medical Bill)": "Allowed Range for Outpatient Date to Incident Date (Medical Bill)", "Allowed Range for Start Date Compare to Incident Date(Allowance)": "", "Allowed Vesting Age": "", "Amount Per Period": "", "Amount Per Time": "", "Amount Range": "", "AND": "", "Annual Interest Rate": "", "Annuity": "Annuity", "Annuity Leading Days": "Annuity Leading Days", "Any modification of the current work shifts will impact the work schedules used, please check": "", "Applicable Business Type": "Applicable Business Type", "Applicable Goods": "", "Applicable only to deductible and co-pay types of stacks": "", "Applicable Target": "", "Applied Goods": "", "Applied Unit": "", "Approve": "Approve", "Are you sure delete this record?": "", "Are you sure to clear all records?": "", "Are you sure to clear this record?": "", "Are you sure to clear this section?": "Are you sure to clear this section?", "Are you sure to delete this record?": "Are you sure to delete this record?", "Are you sure to delete this template?": "", "Are you sure to delete this version?": "", "Are you sure to enable this feature? Once enabled, it cannot be turned off. Please confirm to proceed.": "", "Are you sure to export all configurations?": "", "Are you sure to import all configurations?": "", "Arrival Airport": "", "Arrival City/Place": "", "Attach to Policy": "", "Auto Deduction Date": "Auto Deduction Date", "Auto Deduction Date Compare to Due Date": "Auto Deduction Date Compare to Due Date", "Auto Deduction Days Before Due Date": "", "Auto Repay APL/PL": "Auto Repay APL/PL", "Auto Termination Rule": "", "Auto Trigger Premium Holiday": "Auto Trigger Premium Holiday", "Automatic Adjustment": "", "Available DCA Frequency": "", "Available Option": "", "Back": "Back", "Back to Search": "Back to Search", "Bank": "Bank", "Bank Branch Address": "", "Bank City": "Bank City", "Basic": "", "Basic Component": "Basic Component", "Basic Information": "Basic Information", "Batch Test": "Batch Test", "Before (days)": "", "Before(Days)": "Before(Days)", "Benefit Calculation": "Benefit Calculation", "Benefit Illustration": "Benefit Illustration", "Benefit Illustration Configuration": "", "Benefit Illustration Test": "", "Benefit Option": "", "Benefit Option  Name": "", "Benefit Option Code": "", "Benefit Option Details": "", "Benefit Option Name": "", "Benefit Options cannot be repeated.": "", "Benefit Payment": "Benefit Payment", "Benefit Schedule & Rate": "Benefit Schedule & Rate", "Bill Conditions": "", "Bill Item": "<PERSON>", "Bill Item Sort": "", "Bill Object": "<PERSON>", "Birthday": "Birthday", "Bonus Allocation Date": "Bonus Allocation Date", "Bonus Code": "", "Bonus Date": "Bonus Date", "Bonus Formula": "Bonus Formula", "Bonus Frequency": "", "Bonus Handling After ETI": "", "Bonus Handling After Paid Up": "", "Bonus Name": "", "Bonus Option": "Bonus Option", "Bonus Period": "", "Bonus Period Factor": "", "Bonus Period(In Years)": "Bonus Period(In Years)", "Bonus Rate Declaration": "Bonus Rate Declaration", "Bonus Rate Declaration List": "Bonus Rate Declaration List", "Bonus Rate Type": "Bonus Rate Type", "Bonus Start from": "Bonus Start from", "Bonus Type": "", "Bonus/Malus Table": "", "bonusRateType": "", "Both: Compliance decision can be provided by user or external triggered, system will take the previous one between the two values.": "", "browse": "browse", "Browse": "", "Bundle Rules": "", "Business": "", "Business Component": "Business Component", "Business Date": "", "Business Element Group": "", "Business Interruption Loss Configuration": "", "Business Scenario": "", "Business Time": "", "Business Transaction": "", "Business Type": "", "Buy/Sell Unit Price": "", "By": "", "Caculation": "", "Calculate from Standard Premium": "", "Calculate Product SA by Liability SA": "", "Calculate Success": "Calculate Success", "Calculation": "Calculation", "Calculation Accuracy": "", "Calculation Accuracy List": "", "Calculation Basis": "Calculation Basis", "Calculation Basis Of Max Insured’s Entry Age": "Calculation Basis Of Max Insured’s Entry Age", "Calculation Basis Of Min Insured’s Entry Age": "Calculation Basis Of Min Insured’s Entry Age", "Calculation Details": "", "Calculation Direction": "Calculation Direction", "Calculation Frequency": "Calculation Frequency", "Calculation Level": "Calculation Level", "Calculation Management": "", "Calculation Method": "Calculation Method", "Calculation Order": "Calculation Order", "Calculation Priority Order": "", "Calculation Type": "", "Calendar Date (per year)": "", "Can,t find stack": "", "Cancel": "Cancel", "Cancel pin": "", "Cancellation Reason": "", "Cancellation Sub Reason": "", "Cancellation Type": "Cancellation Type", "Cancellation Type & Cancellation Reason": "", "Cancellation Type Reason": "", "Cascade": "Cascade", "Cash Bonus": "Cash Bonus", "Cash Bonus Allowed": "Cash Bonus Allowed", "Cash Refund Formula": "", "Cash Value Calculation Level": "", "cashValueTypes": "", "Change installment premium calculation method will clear the existing configuration, please confirm.": "Change installment contribution calculation method will clear the existing configuration, please confirm.", "Change the switch will clear the current configured data, are you sure to do it?": "", "Changes have not been saved.": "", "Changes made after submission will not be revoked": "", "Changing vehicle database model or fields will clear the existing configuration, please confirm.": "Changing vehicle database model or fields will clear the existing configuration, please confirm.", "Channel Enumerated Values": "Channel Enumerated Values", "Channel Info": "", "Charge": "Charge", "Charge Amount After": " ", "Charge Code": "Charge Code", "Charge code is duplicated, please check.": "Charge code is duplicated, please check.", "Charge Code should start with ILPC and followed by 3 digits": "Charge Code should start with ILPC and followed by 3 digits", "Charge For Investment Product": "Charge For Investment Product", "Charge Formula": "", "Charge Free Month": "", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value.": "", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value. ": "", "Charge Free Period": "", "Charge Free Start From": "", "Charge Handling": "Charge Handling", "Charge Period Factor": "", "Charge Type": "Charge Type", "Checking whether there are any products using this template...": "", "Child Field": "", "Child Field Name": "", "Cited Times": "Cited Times", "Claim Agreement": "", "Claim Bill Item Setting": "Claim <PERSON>", "Claim Calculation": "Claim Calculation", "Claim Clawback": "", "Claim Compensation Amount": "", "Claim Eligibility and Policy Matching": "Claim Eligibility and Certificate Matching", "Claim Incident Type": "Claim Incident Type", "Claim Limit": "<PERSON><PERSON><PERSON>", "Claim Reserve Setting": "Claim Reserve Setting", "Claim Scenario": "<PERSON><PERSON><PERSON>", "Claim Section": "", "Claim Stack": "", "Claim Stack Correlation": "<PERSON><PERSON><PERSON> Correlation", "Claim Stack Correlation Info": "", "Claim Stack Definition": "", "Claim Stack Info": "", "Claim Stack Management": "<PERSON><PERSON><PERSON>ack Management", "Claim Stack Query": "", "Claim Stack Template": "", "Claim Stack Template Management": "", "Claim Status": "", "Claim Type": "Claim Type", "Claim type of the selected liability is not supported, please Confirm.": "Claim type of the selected liability is not supported, please Confirm.", "Claimable Loss Party": "Claimable Loss Party", "Claimable Loss Party Type": "", "claimHistoryTimes（Mock）": "", "Claims Ratio": "<PERSON><PERSON><PERSON>", "claimStatus": "", "Clear": "Clear", "Clear All": "", "Clear Factor": "", "Clear successfully": "", "clear the all datas will delete this version, are you sure to delete all?": "", "Clear the diagnosis configuration": "Clear the diagnosis configuration", "Clear the surgery configuration": "Clear the surgery configuration", "Clear the surgery tag1 configuration": "", "Clear the surgery tag2 configuration": "", "Click Edit on the right to configure multiple-language for the name": "", "Click here to upload": "", "Click or drag the file here to upload": "Click or drag the file here to upload", "Click to the right of the title to edit the name format value": "Click to the right of the title to edit the name format value", "Close": "", "code": "", "Code": "Code", "Collect": "Collect", "Collect Extra Installment Premium?": "Collect Extra Installment Premium?", "Collect Overdue Premium Interest": "", "Collect the entire premium when a claim is made": "", "Collection Payment Info": "", "Combination Relationship": "Combination Relationship", "Combined liability cash value calculation could not contain optional liability.": "", "Commission Clawback": "Commission Clawback", "Common Enumerated Values": "Common Enumerated Values", "Comparison Type": "", "Compensation Bill Item": "Compensation Bill Item", "Compensation Claim Info": "Compensation Claim Info", "Compliance Enumerated Values": "", "Component": "Component", "Component Code": "", "Component Description": "", "Component Instruction": "Component Instruction", "Component Name": "", "Component Query": "", "Condition": "", "Conditional UW Agreement": "Conditional UW Agreement", "Conditions": "Conditions", "Configuration History": "", "Configure": "", "Configure Benefit Factors": "", "Configure Benefit Option Detail": "", "Configure Payment & Collection Method": "", "Configure product categories for the tenant.": "", "Configure the association between fields": "Configure the association between fields", "Confirm": "Confirm", "Confirm Export": "", "Confirm Import": "", "Confirm to submit the product?": "Confirm to submit the product?", "Confirmation Period": "", "Conversion Date": "", "Copy Formula": "Copy Formula", "Copy Link": "", "Copy Reminder": "", "Copy Successfully": "", "Copy to New Product": "Copy to New Product", "Copy to New Version": "Copy to New Version", "Coverage Data Change Type": "Coverage Data Change Type", "Coverage Date Change Type": "", "Coverage Period": "Coverage Period", "Coverage Period Fields to Revise": "", "Coverage Period Range": "Coverage Period Range", "Coverage Period Type": "Coverage Period Type", "Coverage Period Value Type": "Coverage Period Value Type", "Coverage Type": "Coverage Type", "Create": "Create", "Create successfully": "", "Create_time": "Create Time", "Creator": "Creator", "Critical illness name": "", "Critical illness stage": "", "Customer Enumerated Values": "Customer Enumerated Values", "Customized Data": "", "CV Formula of Basic SA": "CV Formula of Basic SA", "CV Formula of Campaign Free SA": "CV Formula of Campaign Free SA", "CV Formula of Reversionary Bonus": "CV Formula of Reversionary Bonus", "CV is sufficient for one period APL": "", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "Daily Basis: The system calculates the daily contribution and then calculates each installment contribution based on the number of days in that installment period. (For a full-year certificate, the contributions of each installment could be different)", "Data filling": "", "Data has existed. please check!": "Data has existed. please check!", "Data Type": "Data Type", "Day of Fixed Due Date": "", "Day of Usage Premium Start Date": "", "Day(s)": "Day(s)", "Day(s) After Due Date": "Day(s) After Due Date", "Day(s) Before Due Date": "Day(s) Before Due Date", "Days": "", "Days Type": "Days Type", "DCA Amount": "", "DCA Frequency": "", "DCA frequency cannot be lower than premium frequency.": "", "DCA Frequency Type": "", "Deactivate Insured": "Deactivate Insured", "Deactivate Insured After Fully Claimed": "Deactivate Insured After Fully Claimed", "Deduction cut-off date upon policy terminates": "", "Deduction Date": "", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "Deduction Date Compare to renewal Certificate effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.", "Deduction Source": "Deduction Source", "Default ANFO Setting Up": "", "Default as Reinvestment if dividend amount per fund is less than": "", "Default as tenant": "", "Default logic for refunded premium is confirmed instalment premium after incident date. If not, refund calculation formula needs to be configured here.": "", "Default Option": "", "Default Time": "", "Default Time Editable": "", "Defer Period": "Defer Period", "Deferred Interest": "", "Define": "Define", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "", "Define Benefit Options": "", "Define Factor & Ratetable by Scenario": "", "Define Fund Calculation Basis for Each Transaction": "", "Define Illustration Sequence": "", "Define the day before policy expired date and days after expired date to extract renewal policy": "Define the day before certificate expired date and days after expired date to extract renewal certificate", "Define the times of product sa and planned premium.": "", "Define whether it is the normal policy insure date or effective date should be within relevant master policy effective period when normal policy issuance.": "Define whether it is the normal certificate insure date or effective date should be within relevant master certificate effective period when normal certificate issuance.", "Defined Application Elements": "Defined Application Elements", "Delay Type": "Delay Type", "delete": "Delete", "Delete Failed": "", "Delete Successfully": "", "Delete successfully.": "", "Delete will affect the associated work scheduling. Are you sure to delete it?": "", "Deleted successfully": "Deleted successfully", "Deleting this record will affect the associated rider relationships. Please delete the related rider relationship records first.": "", "Departure Airport": "", "Departure City/Place": "", "Departure Date": "", "Description": "Description", "Deselect this goods will remove it from related bundle rules in step 2. Please confirm.": "", "Diagnosis": "Diagnosis", "Diagnosis Code": "Diagnosis Code", "Diagnosis Configuration Method": "Diagnosis Configuration Method", "Diagnosis Description": "Diagnosis Description", "Diagnosis Set": "", "Digit": "", "Disability Category": "Disability Category", "Disability Classification": "", "Disability Grade": "Disability Grade", "Disability Set": "", "Discount Sequence": "Discount Sequence", "Discount Type": "", "Disease Agreement": "Disease Agreement", "Disease Classification": "Disease Classification", "Distribution Method": "", "DMS": "", "Dollar Cost Averaging Arrangement": "", "Down Sell Indicator": "", "Download Result": "", "Download Successfully": "", "Download Template": "Download Template", "Draft": "", "Drag to adjust the calculation order": "", "Due Date": "Due Date", "Due Date Rules": "", "Dunning Rule": "", "Duplicate data": "", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "Duplicate formula configuration for same formula category and sub category. Please edit original one.", "Duplicate formula configuration for same formula sub category and tax type. Please edit original one.": "Duplicate formula configuration for same formula sub category and tax type. Please edit original one.", "Duplicated data exists, please check the Original Currency and Target Currency.": "", "Duplicated Interest!": "", "E.g.: If there exists multiple liabilities for one insured. User could define whether to deactivate this insured when one of the liability has been fully claimed.": "E.g.: If there exists multiple liabilities for one insured. User could define whether to deactivate this insured when one of the liability has been fully claimed.", "E.g.: If there exists multiple liabilities under one product. User could define whether to terminate this liability/Product when one of the liability has been fully claimed.": "E.g.: If there exists multiple liabilities under one product. User could define whether to terminate this liability/Product when one of the liability has been fully claimed.", "Each premium frequency can only configure 1 DCA frequency record.": "", "EARLIEST OF": "", "Edit": "Edit", "Edit Charge": "Edit Charge", "Edit Claim Stack Template": "", "Edit Component": "", "Edit Enumerated Fields Dependency": "", "Edit Factor": "", "Edit Formula": "Edit Formula", "Edit Liability": "Edit Liability", "Edit Ratetable": "Edit Ratetable", "Edit Reminder": "", "Edit Tag Definition": "", "Editing": "Editing", "EDITING": "", "Editing any formula or ratetable within the new or original product will impact its calculations.": "", "Editing any formula or ratetable within the new or original version will impact its calculations.": "", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "", "EFFECTIVE": "", "Effective Date Month End to Expiry Date Month End": "", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "Effective Date Rule": "Effective Date Rule", "Effective Period": "Effective Period", "Effective Time": "", "Element": "Element", "Embedded Ratetable": "", "Enable Green Card Issuance Logic for Non-EU Countries": "", "Enable Non-Fleet Grade and Accident Index Configuration": "", "End date": "", "Enumerated Fields Dependency": "", "Error": "", "Error Description": "", "error format, eg: 1/1.0/1.00": "error format, eg: 1/1.0/1.00", "Estimated Lapse Date Formula": "Estimated Lapse Date Formula", "Every": "", "Example": "", "Example: V01": "", "Exception Output": "Exception Output", "Excess Premium Handling": "", "Exchange Rate": "", "Exchange Rate (Buying)": "", "Exchange Rate (Middle)": "", "Exchange Rate (Selling)": "", "Exchange Rate Query": "", "Exclusion Indicator": "Exclusion Indicator", "Existing Stack in the Product": "", "Exists duplicate value": "Exists duplicate value", "Exit Full Screen": "", "Expand": "Expand", "Expiry Date Adjustment": "", "Expiry Date Calculation Method": "Expiry Date Calculation Method", "Expiry Date Rule": "Expiry Date Rule", "Expiry Time Agreement Type": "Expiry Time Agreement Type", "Export": "", "Export All": "", "Export failed, please try again!": "", "Export Operation Fail": "", "Export Range": "", "Export successfully!": "", "Export to CSV": "", "Extended Term": "", "Extended Term Formula": "", "External: Compliance decision can only be provided by external triggered.": "", "Extra Grace Period": "Extra Grace Period", "Extra Loading": "Extra Loading", "Extra Loading Calculation Method": "Extra Loading Calculation Method", "Extra Loading Indicator": "Extra Loading Indicator", "Extra Loading Type": "Extra Loading Type", "Extra Premium Formula Code": "Extra Contribution Formula Code", "Extract Bill Date": "Extract Bill Date", "Extract Bill Days Before Due Date": "", "Extract day(s) must be greater than offset day(s)": "", "Extract premium from X days prior to due date": "Extract contribution from X days prior to due date", "Extraction Method": "", "Factor": "", "Factor Code": "", "Factor Definition": "", "Factors": "", "Fee Limit": "<PERSON><PERSON><PERSON>", "Fee Type": "Ujrah Type", "Field Value": "", "File": "", "File Declaration": "", "File Management": "", "File Name": "", "File Type": "", "FILE TYPE": "", "file upload failed": "", "file uploaded successfully": "", "File Version": "", "Filter": "", "Financial Anniversary Date": "Financial Anniversary Date", "First": "", "First Date": "First Date", "first.": "", "Fixed Age": "", "Fixed Amount": "", "Fixed Amount or Fixed Period": "", "Fixed due date rule：Fixed due date or period end date": "Fixed due date rule：Fixed due date or period end date", "Fixed Due Date Value": "", "Fixed Exchange Rate": "", "Fixed Time": "", "Fixed Value": "", "Fixed Value is required": "", "Flexible Premium Allocation": "Flexible Contribution Allocation", "Flexible Premium and SA Agreement": "Flexible Contribution and SA Agreement", "Flexible Premium Relationship": "Flexible Contribution Relationship", "Flexible Premium Type 1": "Flexible Contribution Type 1", "Flexible Premium Type 2": "Flexible Contribution Type 2", "Floating Exchange Rate": "", "Follow the same as Planned Premium": "", "For Fixed Day of Month, please input number from 1 and 28": "", "For formula(s) or ratetable(s) created inside one specific product, the editing function is not supported.": "", "For formulas that use the list function, Batch Test is not supported yet.": "For formulas that use the list function, Batch Test is not supported yet.", "For liabilities without combination relationship configured, it will be settled as \"Attachable\" by default. If you configure liability combination relationship as \"Compulsory\", it means liability 2 depends on liability 1; And if you configure as \"Non-attachable\", it means liability 1 and liability 2 can not be sold in combination.": "For liabilities without combination relationship configured, it will be settled as \"Attachable\" by default. If you configure liability combination relationship as \"Compulsory\", it means liability 2 depends on liability 1; And if you configure as \"Non-attachable\", it means liability 1 and liability 2 can not be sold in combination.", "For New-Biz": "", "For policy annual illustration": "", "For POS": "", "For POS triggered illustration": "", "For step {{index}}, nested calculation components within the ifElse component are not allowed. Please create a separate step before ifElse for calculations.": "", "For Usage based insurance, accrued premium is calculated according to actual usage during certain period. Usage Premium Start date represents the first day of usage period. And the end date will be calculated based on Usage Premium Start Date and payment frequency.": "For Usage based takaful, accrued contribution is calculated according to actual usage during certain period. Usage Contribution Start date represents the first day of usage period. And the end date will be calculated based on Usage Contribution Start Date and payment frequency.", "Format": "", "formula": "Formula", "Formula": "", "Formula Category": "Formula Category", "Formula Code": "Formula Code", "Formula Code could not start with \"G_\".": "", "Formula Code could not start with numbers.": "Formula Code could not start with numbers.", "Formula Description": "Formula Description", "Formula Detail": "", "Formula in Specific Scenarios": "", "Formula Level": "Formula Level", "Formula Management": "Formula Management", "Formula Name": "Formula Name", "Formula Sub Category": "Formula Sub Category", "formulas": "", "Free Switch Times": "", "Free Withdrawal Time(s)": "", "Freelook Period": "", "Freelook Period Base Date": "", "Freelook Period Expiration Rule": "Freelook Period Expiration Rule", "Freelook Refund Account Rule": "", "Freelook Reverse Rules": "", "Frequency": "Frequency", "Full Screen": "", "Fund": "", "Fund Appointment Rate for Rebalance": "", "Fund Calculation Basis Details": "", "Fund Calculation Basis of Product": "", "Fund Code": "", "Fund Deduction Method": "Fund Deduction Method", "Fund Deduction Sequence": "", "Fund Info": "", "Fund Trad Type": "", "fund_name": "Fund Name", "gender": "Gender", "Gender": "Gender", "Generate": "Generate", "Generate Cash Value Saving Account": "Generate Cash Value Saving Account", "Generate new policy number for renewal": "Generate new certificate number for renewal", "Generate Result": "", "Generate Test Excel": "", "Generate Vehicle Databases Fields": "Generate Vehicle Databases Fields", "goods": "", "Goods / Package / Product not found during export process. Please check if the data exists and is correctly configured in the system.": "", "Goods A": "", "Goods allowed to be sold together": "", "Goods B": "", "Goods Code": "Goods Code", "Goods Code/Goods Name": "", "GOODS CONFIGURATION": "", "Goods Name": "Goods Name", "GOODS RELATED CONFIGURATION": "", "Goods V2": "", "Got it": "", "Grace Period": "<PERSON> Period", "Grace Period Rule": "Grace Period Rule", "Green Card Fee": "", "Green Card(Non-EU)": "", "Guarantee Period": "", "Guarantee Period Value Type": "", "Handling of Charge Deduction within or out of NLG": "", "Handling of Premium Unpaid": "Handling of Premium Unpaid", "Handling of TIV Insufficient": "Handling of TIV Insufficient", "Has Cash Value": "Has Cash Value", "Have SA": "", "Health Claim Info": "Health Claim Info", "HH:mm": "", "Holiday": "", "Hospital/Clinic": "Hospital/Clinic", "Hospitalization Allowance Amount": "Hospitalization Allowance Amount", "Hospitalization Allowance Value Type": "Hospitalization Allowance Value Type", "Hospitalization start date be within x days/months/years after Incident date. E.g: if hospitalization start date should be within 1 year after Incident date, configure as 1, Year(s)).": "Hospitalization start date be within x days/months/years after Incident date. E.g: if hospitalization start date should be within 1 year after Incident date, configure as 1, Year(s)).", "Hour": "", "ID": "ID", "if calculation sequence is needed，please make sure all discount have sequence definition.": "if calculation sequence is needed，please make sure all discount have sequence definition.", "if calculation sequence is needed，please make sure all loading have sequence definition.": "if calculation sequence is needed，please make sure all loading have sequence definition.", "If cash value have surplus after ETI": "", "If cash value have surplus after paid up": "", "If choose 'Refund', premium collection will be reserved in suspense and refunded later. If choose 'Fill in the ILP premium plan or installment premium', system will settle the installment premium or partially fill the ILP premium plan.": "", "If choose No, RTU will share same payment frequency with Planned Premium. If choose Yes, RTU is allowed to use different payment frequency with Planned Premium, which could be defined below.": "", "if condition": "", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "if illustration sequence is needed，please make sure all illustration items have sequence definition.": "", "If liability could not be found here, please check whether liability has configured claim type in liability management module": "If liability could not be found here, please check whether liability has configured claim type in liability management module", "If no parameter could be found, you could create middle factor by using the format Middle_XXX.": "", "If not defined on the product, the rules defined on the package will be used (allowing for unified definition directly when packaging the package)": "", "If someone planning to drive in non-EU countries that recognize the Green Card, he/she must request the card from auto insurance provider before traveling. This function covers the Green Card logic for purchasing auto insurance policies in non-EU countries, including fees, issuance, endorsements, refunds, printing, etc.": "", "If surgery classification needed, please configure 'Surgery Level' in advance!": "If surgery classification needed, please configure 'Surgery Level' in advance!", "If the customer is responsible for 10%, the value is set to 10/.": "If the customer is responsible for 10%, the value is set to 10/.", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "", "If the premium is insufficient to pay any outstanding billing or ILP premium plan, then": "", "If this product has waiver liability, all liabilities must be mandatory.": "", "If value of the factors are different across period.": "", "If value of the factors are same across period.": "", "If you select yes，system support to add insurable interest under liability.": "If you select yes，system support to add insurable interest under liability.", "Illustration Item": "Illustration Item", "Illustration Item Code": "", "Illustration Item List": "", "Illustration Item Value cannot starts with 'G_'": "Illustration Item Value cannot starts with 'G_'", "Illustration Item(PSI)": "", "Illustration Level": "", "Illustration Period": "Illustration Period", "Illustration Period Rule": "Illustration Period Rule", "Illustration Scenario": "", "Illustration Sequence": "", "ILP Bonus": "", "ILP Charge": "", "ILP Fund Calculation Basis": "", "ILP Lapse": "ILP Lapse", "ILP Premium Holiday": "ILP Premium Holiday", "ILP Premium Limit": "", "ILP TIV Account Type": "", "ILPC + 3 digits": "ILPC + 3 digits", "Import": "", "Import / Export": "", "Import All": "", "Import Operation Fail": "", "Import Successful!": "", "In Japan's auto insurance system, Non-Fleet Grade and Accident Index are two key factors that affect policy rating and premium adjustments. This function is used to define the calculation formulas for Non-Fleet Grade and Accident Index.": "", "In Progress": "", "In use:": "", "Inactive": "Inactive", "Incident Date": "", "Incident Reason": "Incident Reason", "Including Tax": "Including Tax", "Increase Interest By(%)": "", "Individual Test": "Individual Test", "Inforce Together": "", "Initial Premium Period": "Initial Contribution Period", "input is illegal": "input is illegal", "Insert fields": "", "Installment Calculation Method": "", "Installment Detail": "Installment Detail", "Installment Info": "", "Installment Payment Plan for Claims": "", "Installment Payment Type": "", "Installment Premium Calculation Basis": "Installment Contribution Calculation Basis", "Installment Premium Calculation Method": "Installment Contribution Calculation Method", "Installment Standard Premium": "", "Installment Standard Premium (Inc Tax)": "", "Installment(s) Premium": "Installment(s) Contribution", "Insurable Interest ID": "Insurable Interest ID", "Insurable Interest Info": "", "Insurable Interest Name": "Insurable Interest Name", "Insurable Interest/Liability Rate Setting": "Insurable Interest/Liability Rate Setting", "Insured Age Comparison": "Insured Age Comparison", "Insured Elements Relationship Matrix": "", "Insured Info": "", "Insured Occupation Class": "Insured Occupation Class", "Insured Occupation Risk Category": "", "Insured's Entry Max Age": "Insured's Entry Max Age", "Insured's Entry Min Age": "Insured's Entry Min Age", "Insured‘s Entry Max Age": "Insured‘s Entry Max Age", "Insured’s Age Range(NB)": "", "Insured’s Age Range(Renewal)": "", "Insured’s Entry Min Age": "Insured’s Entry Min Age", "Interest": "", "Internal: Compliance decision can only be provided by compliance user.": "", "Investment Agreement": "", "Investment Delay": "", "Investment Delay Option": "", "Is switch charge a fixed amount?": "", "is the default condition.": "", "Is there a need to make a lump sum payment before executing the installment plan?": "", "Issue with Green Card (Non-EU)": "", "It is also supported entering the exchange rate unit. You can open \"Component Instruction\" for more details.": "", "It will take a certain time for batch test. A platform message will be sent to you once the batch test is completed. And you could download test result through the message.": "It will take a certain time for batch test. A platform message will be sent to you once the batch test is completed. And you could download test result through the message.", "itemExtend1": "itemExtend1", "itemExtend2": "itemExtend2", "itemExtend3": "itemExtend3", "itemExtend4": "itemExtend4", "itemExtend5": "", "itemExtend6": "", "Japan Motor": "", "LANGUAGE": "", "Lapse Date": "Lapse Date", "Lapse/Termination": "", "Last Modifier": "Last Modifier", "LATEST OF": "", "Less": "", "Liability": "Liability", "Liability <{{liabilityId}}><{{liabilityName}}> has been created successfully.": "", "Liability <{{liabilityId}}><{{liabilityName}}> has been edited successfully": "", "Liability Agreement": "Liability Agreement", "Liability Category": "Liability Category", "Liability Code": "Liability Code", "Liability Coverage Period": "Liability Coverage Period", "Liability Coverage Period defaults to the same as Product Coverage Period. If it differs from Product Coverage Period, you can define an independent coverage period for the liability here.": "", "Liability Description": "Liability Description", "Liability Handling": "", "Liability Handling After ETI": "", "Liability Handling After Paid Up": "", "Liability ID": "Liability ID", "Liability Info": "", "Liability information and POS items can not be configurated at the virtual main benefit. Do you want to delete them?": "Liability information and POS items can not be configurated at the virtual main benefit. Do you want to delete them?", "Liability is required": "Liability is required", "Liability Level": "Liability Level", "Liability Name": "Liability Name", "Liability Name(Liability1)": "Liability Name(Liability1)", "Liability Name(Liability2)": "Liability Name(Liability2)", "Liability or Interest is required": "", "Liability Relationship Matrix": "Liability Relationship Matrix", "Liability Remark": "Liability Remark", "Liability SA": "", "Liability Tag": "Liability Tag", "Liability Termination": "Liability Termination", "Liability Termination by Claim Setting": "Liability Termination by <PERSON><PERSON><PERSON>", "Lien Indicator": "", "Life Event": "", "Linked Formulas": "", "Linked Goods": "Linked Goods", "Linked Package": "", "Linked Packages": "Linked Packages", "Linked Product / Package / Goods": "Linked Product / Package / Goods", "Linked Products": "Linked Products", "Linked Ratetables": "", "Linked Rules": "", "Load More": "", "Loading Method": "Loading Method", "Loading Sequence": "Loading Sequence", "Loading Type": "", "Loan Amount": "", "Loan Term Period": "", "Loan Term Type": "", "Low Risk Fund": "", "Lump Sum Amount": "", "Main / Rider": "Main / Rider", "Main Benefit (Virtual)": "", "Main Condition Type": "Main Condition Type", "Main or Rider": "Main or Rider", "Main Product Code": "", "Main Product Name": "", "Main Relationship with Rider": "", "Make sure at least one of the diagnosis tag has been configured.": "Make sure at least one of the diagnosis tag has been configured.", "Make sure at least one of the disabilities has been configured.": "", "Manual Adjustment": "", "Marketing Enumerated Values": "Marketing Enumerated Values", "Matrix Table": "", "Matrix Table Code": "", "Maturity Agreement": "Maturity Agreement", "Maturity Benefit": "Maturity Benefit", "Maturity Benefit Leading Days": "Maturity Benefit Leading Days", "Maturity Reminder Date Compare to Policy Expiry Date": "Maturity Reminder Date Compare to Certificate Expiry Date", "Maturity Reminder Rule": "Maturity Reminder Rule", "Maturity Reminder Rule (Legacy)": "", "Maturity Reminder Rule cannot be repeated.": "Maturity Reminder Rule cannot be repeated.", "Max": "Max", "Max Claim Limit": "<PERSON>", "Max Claim Percentage (%)": "Max Claim Percentage (%)", "Max Claim Times": "", "Max Down-Regulation Ratio": "Max Down-Regulation Ratio", "Max Guaranteed Renewable": "Max Gua<PERSON>eed Renewable", "Max SA Multiplier": "", "Max SA Multiplier Type": "", "Max Up-Regulation Ratio": "Max Up-Regulation Ratio", "Max value cannot be less than min value": "", "Maximum Age for Whole Life": "", "Maximum Loanable Amount Formula": "", "Maximum Loanable Percentage": "", "Maximum Premium Holiday": "Maximum Premium Holiday", "Maximum Premium Holiday Period Type": "Maximum Premium Holiday Period Type", "Medical Bill Configuration": "", "Medical Bill Item": "", "Medical Bill Type": "", "Medical Bill Type and Bill Item": "Medical Bill Type and Bill <PERSON>", "Medical Billing Type": "", "Medical Expense": "Medical Expense", "Medical Fee Rules": "", "MENU": "", "Middle factor format is not correct. Please follow format: Middle_XXX.": "", "Min": "Min", "Min / Max Times": "Min / Max Times", "Min SA Multiplier": "", "Min SA Multiplier Type": "", "Min Value of Policy": "", "Min Withdrawal Amount": "", "Minimum Investment Period": "", "Minimum Investment Period Applied to": "", "Minimum Investment Period Type": "", "Minimum Investment Period Value": "", "Minimum Net Premium (Standard Premium+Extra Premium-Premium Discount) for coverage period if Net premium calculated from formula is below minimum net premium.If regular payment, minimum premium cap needs to be multiplied by modal factor.": "Minimum Net Contribution (Standard Contribution+Extra Contribution-Contribution Discount) for coverage period if Net contribution calculated from formula is below minimum net contribution.If regular payment, minimum contribution cap needs to be multiplied by modal factor.", "Minimum Premium Cap": "Minimum Contribution Cap", "Minute(s)": "", "Missing parameter": "Missing parameter", "Missing required parameter": "", "Modal Factor": "Modal Factor", "Modifier": "", "Modifying the diagnosis tag info will auto clear the Diagnosis configuration. Please confirm whether to continue?": "Modifying the diagnosis tag info will auto clear the Diagnosis configuration. Please confirm whether to continue?", "Modifying the Surgery level info will auto clear the surgery configuration. Please confirm whether to continue?": "Modifying the Surgery level info will auto clear the surgery configuration. Please confirm whether to continue?", "Modifying the Surgery tag1 info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Modifying the Surgery tag2 info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Modifying the tag definition info will auto clear the surgery configuration. Please confirm whether to continue?": "", "Month": "Month", "Month Value": "", "Month Value for Advance Filling": "", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "Monthly Basis: The system calculates the monthly contribution and then calculates each installment contribution based on the number of months in that installment period. (For a full-year certificate, the contributions of each installment are the same)", "Months": "", "Multi Language": "", "Multi-language": "Multi-language", "My Creations": "", "NA": "", "Name": "Name", "Name Connection Method": "Name Connection Method", "Name Format": "Name Format", "Name Sequence Definition": "Name Sequence Definition", "NCD Code": "", "NCD Formula Code": "NCD Formula Code", "NCD Management": "", "Need To Attach Advanced Criteria": "", "Net Premium Adjustment": "", "New Category": "", "New Claim Stack Structure": "", "New Product after ETI": "", "New Product after Paid Up": "", "New Version": "", "Next": "Next", "NLG Condition": "NLG Condition", "NLG Period": "NLG Period", "NLG Period Factor": "", "NLG Period Type": "NLG Period Type", "NLG Period Type is required": "", "NLG Period Value": "NLG Period Value", "NLG Period Value is required": "", "no": "No", "No Claim Bonus": "No Claim Bonus", "No Claim Discount": "No Claim Discount", "No component is selected. Please modify.": "", "No Content": "", "No Data": "No Data", "No impact on original formula when editing formula copies": "", "No Lapse Guarantee": "No Lapse Guarantee", "No match result.": "", "No Valid Data": "", "No.": "No.", "No. Claim Times": "", "No. of Completed Policy Years for Bonus Vesting on Claim": "No. of Completed Certificate Years for Bonus Vesting on Claim", "No. of Completed Policy Years for CB Becoming Payable": "No. of Completed Certificate Years for CB Becoming Payable", "No. of Completed Policy Years for Surrender Bonus Allowed": "No. of Completed Certificate Years for Surrender Bonus Allowed", "No.of Installment": "", "Non-Fleet Grade and Accident Index of Japan Motor": "", "Non-Fleet Grade Formula": "", "Nonforfeiture Option Type": "", "Normal Policy Date Check Rule Compare to Master Policy": "Normal Certificate Date Check Rule Compare to Master Certificate", "Normal Policy Expiry Date Rule": "Normal Certificate Expiry Date Rule", "Not Applicable": "", "Not Cascade": "Not Cascade", "Note: The relationship defined here is only applicable when the package is configured as single-main-benefit.If the package is marked as supporting multiple main benefits, this relationship will be ignored and have no effect.": "", "Notice": "", "Notice Date Compare with Due Date": "", "Notice Rule": "", "Notice Rule cannot be repeated": "", "Notification": "", "number": "No.", "Number": "", "Number Item": "", "Number Items": "", "Number of Files/URLs": "", "Number1": "", "Number2": "", "Object": "Object", "Object Category": "Object Category", "Object Category & Object": "", "Object Category & Type": "", "Object Category Code": "", "Object Category Name": "", "Object Component": "", "Object Element-Auto-Vehicle": "", "Object Enumerated Values": "Object Enumerated Values", "Object Info": "", "Object Sub Category": "Object Sub Category", "Object.etc. Driver": "", "Obtain Template": "<PERSON><PERSON><PERSON>", "Occupation Risk Category": "Occupation Risk Category", "Occurrence Type（Accommodation)": "Occurrence Type（Accommodation)", "Occurrence Type（Transportation)": "Occurrence Type（Transportation)", "Occurrence Type（Transportation）": "", "Offset Date": "Offset Date", "Offset from X Days Compare to Expired Date": "Offset from X Days Compare to Expired Date", "Offset from X days prior to due date": "Offset from X days prior to due date", "Once allowed, the client don't need to pay full amount for total overdue premium bills during reinstatement. Only a minimum premium payment is required to pay all the charges and keep the policy effective. The POS formula needs to be defined under this condition.": "", "Once opened, system allow policyholder to rebalance their fund account (TIV) based on specific ratio regularly.": "", "Once opened, system allow user to trigger adanced DCA arrangement. Premium will put into a pre-defined low risk fund first, and then switch out and buy target fund on a regular basis.": "", "Only components with the same stack type can be in one template. Different stack types are detected in your submission. Please check the configuration.": "", "Only display formulas that are directly defined and associated under Package Management (e.g., Tax Setting, Premium Adjustment...)": "", "Only for Commission & Service Fee formula, it is supported to query linked Goods": "", "Only for Commission & Service Fee formula, it is supported to query linked Goods.": "", "Only for Levy and Stamp duty formula, it is supported to query linked package": "", "Only one formula allowed for each fund, no duplicated allowed.": "Only one formula allowed for each fund, no duplicated allowed.", "only one formula type either rate or premium could be configured for one product.": "only one formula type either rate or contribution could be configured for one product.", "Only one modal factor allowed for each payment frequency, no duplicate allowed. Please check.": "Only one modal factor allowed for each payment frequency, no duplicate allowed. Please check.", "Only one record is allowed for each calculation method. No duplicate allowed.": "", "Only perform rebalancing when the variance exceeds": "", "operation failure": "", "Optional": "Optional", "Optional / Required": "Optional / Required", "Order": "Order", "Original Currency": "", "Out of No Lapse Guarantee": "Out of No Lapse Guarantee", "Overdue Auto Deduction": "Overdue Auto Deduction", "Overdue Handling": "", "Overdue Status": "Overdue Status", "Package": "", "Package Code": "Package Code", "PACKAGE CONFIGURATION": "", "Package Name": "Package Name", "PACKAGE RELATED CONFIGURATION": "", "Package V2": "", "packages": "", "Paid Up": "Paid Up", "Paid Up SA Formula": "", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "", "Parameter or result code have been changed. The uploaded ratetable will be cleared. Please confirm.": "", "Parameters relevant to multiple object can not be used directly in steps without list function.": "Parameters relevant to multiple object can not be used directly in steps without list function.", "Parent Field": "", "Parent Field Name": "", "Partial": "", "Partial withdrawal allowed": "Partial withdrawal allowed", "Participating Agreement": "", "Payment & Collection Method": "Payment & Collection Method", "Payment Defer Period": "", "Payment Defer Period Type": "", "Payment Frequency": "Payment Frequency", "Payment Frequency & Modal Factor": "Payment Frequency & Modal Factor", "Payment Option": "Payment Option", "Payment Period": "", "Payment Period Type": "", "Payment Period Value Type": "", "Payment/Collection Method & Account": "Payment/Collection Method & Account", "Pealse select Formula Code": "", "Pending Case Enumerated Values": "", "Percentage": "", "Period": "", "Period Limit Matrix": "Period Limit Matrix", "Period Type": "Period Type", "Period Value": "", "Permium Handling Method Within Premium Holiday": "Permium Handling Method Within Premium Holiday", "Pin": "", "Place of Incurred(Allowance)": "Place of Incurred(Allowance)", "Place of Incurred(Medical Bill)": "Place of Incurred(Medical Bill)", "Plan Info": "", "Planned Premium": "", "Planned Premium by Layer": "", "Planned Premium Layer Info": "", "Please": "", "Please add at least one formula step": "Please add at least one formula step", "Please Add Premium Notice Rule": "Please Add Contribution Notice Rule", "Please add stack correlation": "", "Please add Terminal Bonus formula.": "Please add Terminal Bonus formula.", "Please at least select one Option": "", "Please check the required name set first": "Please check the required name set first", "Please check whether you need to modify the following Formula:": "Please check whether you need to modify the following Formula:", "Please choose at least one type of name connection method & name.": "Please choose at least one type of name connection method & name.", "Please complete coverage period agreement": "Please complete coverage period agreement", "Please config Enumeration Value": "", "Please Configure a Formula for Vehicle Market Value": "Please Configure a Formula for Vehicle Market Value", "Please configure at least one of the agreement.": "", "Please configure at least one record.": "Please configure at least one record.", "Please configure before submit.": "Please configure before submit.", "Please configure calculation logic for risk sub-category.": "", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration -> Premium Notice Reminder\".": "", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration\".": "", "Please configure this agreement, otherwise insured will not be default deactivated after fully claimed of the selected liability.": "Please configure this agreement, otherwise insured will not be default deactivated after fully claimed of the selected liability.", "Please configure this agreement, otherwise liability will not be default terminated after claim.": "Please configure this agreement, otherwise liability will not be default terminated after claim.", "Please configure transaction type.": "Please configure transaction type.", "Please confirm changing the default option from": "", "Please confirm setting the default option to": "", "Please confirm to wipe out all uploaded benefit details and all versions.": "", "Please continue to finish Premium holiday related configuration in the \"ILP Premium Holiday\" Section.": "Please continue to finish Premium holiday related configuration in the \"ILP Premium Holiday\" Section.", "Please copy your File URL first.": "", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "", "Please define the allowed investment strategy.": "", "Please do not enter a space in result code": "Please do not enter a space in result code", "Please ensure at least one record is entered in Factor Definition or Ratetable Replacement.": "", "Please fill in the mandatory items before submitting.": "", "Please first define the Object Category and Object under Configuration Center → Tenant Data Configuration → Basic Rule → Object Category & Object.": "", "Please input": "Please input", "Please input at least one field.": "Please input at least one field.", "Please input at least three characters.": "Please input at least three characters.", "Please input condition": "Please input condition", "Please input end number": "Please input end number", "Please input expression": "Please input expression", "Please input in this format: 'HH:mm'. e.g. 12:00": "", "Please input liability handling.": "", "Please input number": "Please input number", "Please input or paste the URL here": "", "Please input positive value": "Please input positive value", "Please input result": "Please input result", "Please input the range in [{{minimum}}, {{maximum}}]": "", "Please input variable": "Please input variable", "Please input variable and start number": "Please input variable and start number", "Please input Vehicle DataBase Name": "Please input Vehicle DataBase Name", "Please make sure that both medical bill type and medical bill item have been configured.": "Please make sure that both medical bill type and medical bill item have been configured.", "Please make sure you define how to calculate this middle factor when using in formula configuration.": "", "Please note: After premium holiday, cash rider will be terminated from next premium due date (of first unpaid bill), while unit deducting rider (if any) will keep effecitve.": "", "Please note: Current detail information of Recurring Single Top Up will be overwritten by Premium Frequency & Installment Detail of Planned Premium.": "", "Please note: There is no refund after termination and policy is not allowed to be reinstated.If there are any active riders, they will also be terminated.": "Please note: There is no refund after termination and policy is not allowed to be reinstated.If there are any active riders, they will also be terminated.", "please retain at least two branches": "please retain at least two branches", "Please save the changes to cancellation reason first.": "", "Please save the changes to cancellation type first.": "", "please select": "", "Please select": "Please select", "Please select at least one advance criteria for ANFO setting.": "", "Please select at least one related liability.": "", "Please select at least two fields.": "Please select at least two fields.", "Please select effective date": "", "Please select Formula Code": "", "Please select formula existed in steps": "Please select formula existed in steps", "Please select language": "Please select language", "Please select one record at least!": "Please select one record at least!", "Please select one reocrd": "", "Please select parameter": "", "Please select ratetable existed in steps": "Please select ratetable existed in steps", "Please select related Formula for POS": "Please select related Formula for POS", "Please select the sections to export:": "", "Please select the sections to import:": "", "Please select to:": "", "Please set the deduction sequence if corresponding premium type is insufficient for charge deduction.": "", "Please submit change before test": "Please submit change before test", "Please tick the additional condition that should be met before enjoy no lapse guarantee": "Please tick the additional condition that should be met before enjoy no lapse guarantee", "please upload excel or csv": "please upload excel or csv", "Please upload the file in XLSX or XLX format!": "Please upload the file in XLSX or XLX format!", "Please* configure at least one set of name rules.": "", "Policy Currency": "", "Policy Effective Without Collection (NB)": "Certificate Effective Without Pay (NB)", "Policy Effective Without Collection (Renewal)": "Certificate Effective Without Pay (Renewal)", "Policy Enumerated Values": "Certificate Enumerated Values", "Policy Holder Info": "", "Policy Illustration": "", "Policy Info": "", "Policy Loan": "Certificate Loan", "Policy Month": "Policy Month", "Policy No.": "", "Policy Type": "Certificate Type", "Policy will be lapsed after grace period ends.": "Policy will be lapsed after grace period ends.", "Policy will be terminated after grace period ends.": "Policy will be terminated after grace period ends.", "Policyholder Age Comparison": "Participant Age Comparison", "Policyholder Elements Relationship Matrix": "", "Policyholder’s Age Range(NB)": "", "Policyholder’s Age Range(Renewal)": "", "PolicyStatusEnum.POLICY_EFFECT": "Effective", "policySuspense（Mock）": "", "Portfolio Rebalancing": "", "POS": "", "POS additional/refund net premium = (product net premium after POS - product net premium before) * unearned period / coverage period": "", "POS Item": "POS Item", "Post-sale Illustration": "", "Post-sale Illustration Trigger Point": "", "Pre-definition": "", "Pre-set Data Setting": "", "Premium Adjustment": "", "Premium Adjustment Level": "", "Premium Adjustment Type": "", "Premium Agreement": "Contribution Limit", "Premium Calculation Method": "", "Premium Discount": "Contribution Discount", "Premium Discount Type": "Contribution Discount Type", "Premium Due Date Rule": "Contribution Due Date Rules", "Premium End Date Calculation Rule": "Contribution End Date Calculation Rule", "Premium Frequency": "Contribution Frequency", "Premium Holiday": "Premium Holiday", "Premium Investment Strategy": "Contribution Investment Strategy", "Premium Limit": "", "Premium Limit Type": "", "Premium Notice Date Compare with Due Date": "Contribution Notice Date Compare with Due Date", "Premium Notice Rule": "Contribution Notice Rule", "Premium Notice Rule cannot be repeated": "", "Premium Notice Rule(Legacy)": "", "Premium Period": "Contribution Period", "Premium Period & Installment": "Contribution Frequency & Installment", "Premium Period Type": "Contribution Period Type", "Premium Period Value Type": "Contribution Period Value Type", "Premium Period(title)": "Contribution Period", "Premium Type": "", "Premium Type Deduction Method": "", "Premium Type Deduction Sequence": "", "Premium Unpaid Handling": "Premium Unpaid Handling", "Preview Ratetable": "", "Previous": "", "Pro Rata Calculation Basis": "", "Product": "Product", "Product & Liability Relationship Matrix": "Product & Liability Relationship Matrix", "Product Agreement": "Product Agreement", "Product Category": "Product Category", "Product Center": "", "Product Center - Product Management": "", "Product Class": "Product Class", "Product Code": "Product Code", "Product Code already exists": "Product Code already exists", "PRODUCT CONFIGURATION": "", "Product Enumerated Values": "Product Enumerated Values", "Product Info": "", "Product Label": "", "Product Label Configuration": "", "Product Liability": "Product Liability", "Product Line": "", "Product Management": "", "Product Name": "Product Name", "Product Overdue Status": "Product Overdue Status", "PRODUCT RELATED CONFIGURATION": "", "Product SA": "", "Product SA Priority": "", "Product SA Setting": "", "Product Status": "Product Status", "Product Template": "Product Template", "Product V2": "", "Product Validation": "", "products": "", "Progression Table": "", "Property Bill Item": "", "Property Bill Object": "", "Property Bill Objects": "", "Property Claim Info": "Property Claim Info", "Publish": "", "Publish Successfully": "", "Published": "", "Quick Menu": "", "Range": "Range", "Rate Formula Code": "Rate Formula Code", "Rate Range": "", "Rate Source": "", "Rate Table": "", "Ratetable": "Ratetable", "Ratetable Category": "Ratetable Category", "Ratetable Code": "Ratetable Code", "RateTable Code": "", "Ratetable Code could not start with \"G_\".": "Ratetable Code could not start with \"G_\".", "Ratetable Code should not include \"_ProductCorrelationMatrix、_PeriodLimitMatrix、G_BenefitScheduleMatrix\".": "", "Ratetable Description": "", "Ratetable Introduction": "Ratetable Introduction", "Ratetable Management": "Ratetable Management", "Ratetable Name": "Ratetable Name", "Ratetable Replacement": "", "Ratetable Sub Category": "Ratetable Sub Category", "ratetables": "", "Re-Define Effective Date Rule": "", "Rebalance Date": "", "Rebalance Frequency": "", "Recalculate SA or Premium": "Recalculate SA or Contribution", "Recalculate SA or Premium Except for Change Reason": "Recalculate SA or Contribution Except for Change Reason", "record": "Records", "record(s) has been uploaded successfully.": "", "records": "Records", "Recount Result": "", "Recurring Single Top Up Detail": "", "Reduce cash value to zero": "", "Refund Confirmed Premium from": "", "Refund Formula": "", "Regional Function Management": "", "Regular Top Up Frequency": "", "Regular Top Up Frequency configure at least one": "", "Regular Top Up payment needs to use separated frequency": "", "Regular withdrawal allowed": "Regular withdrawal allowed", "Reinstatement Period": "Reinstatement Period", "Reinstatement Period Unit": "Reinstatement Period Unit", "Related Stacks": "", "Related Stacks within the Same Order": "", "Related Termination": "", "Related to Bone Fracture/Dislocation Item and Bone Fracture/Dislocation Level": "", "Relation Policy Info": "", "Relation Policy Template": "", "Relational Type": "", "Relationship": "", "Rename": "", "Renewable or Not": "Renewable or Not", "Renewal": "", "Renewal Agreement": "Renewal Agreement", "Renewal Extraction Period": "Renewal Extraction Period", "Renewal Grace Period": "<PERSON><PERSON>", "Renewal Policy Effective Date Rule": "Renewal Certificate Effective Date Rule", "Renewal Policy Effective Without Collection": "", "Renewal Proposal Submit Date": "", "Renewal Reminder Date Compare to Policy Expiry Date": "Renewal Reminder Date Compare to Certificate Expiry Date", "Renewal Reminder Rule": "", "Renewal Reminder Rule cannot be repeated": "Renewal Reminder Rule cannot be repeated", "Renewal Together": "", "Replacement Ratetable": "", "Replacement Ratetable is required": "", "Required": "Required", "Required / Optional": "Required / Optional", "Reserve Provision trigger point": "", "Reserve Provision Trigger Point": "Reserve provision trigger point", "Reset": "Reset", "Result Code": "Result Code", "Result Details": "", "Retirement Age (Insured)": "", "Retirement Option": "", "Retirement Option Start Date": "", "Reversionary Bonus": "Reversionary Bonus", "Reversionary Bonus Allowed": "Reversionary Bonus Allowed", "Rider Code": "", "Rider Handling After ETI": "", "Rider Handling After Paid Up": "", "Rider Handling After Triggering Premium Holiday": "", "Rider Name": "", "Rider Name(Rider1)": "", "Rider Name(Rider2)": "", "Rider Relationship Matrix": "", "Rider Relationship with Main Product": "", "Rider Type": "", "Risk Category": "Risk Category", "Risk SA for Risk Category": "", "Risk SA for Risk Sub-category": "", "Risk Sub-category": "Risk Sub-category", "Room Level": "Room Level", "Round": "", "Rounding Digits": "", "Rounding Type": "", "Rule Category": "", "Rule Code": "Rule Code", "Rule Name": "", "Rule/Rule Set": "", "Rule/Rule Set Code": "", "Rule/Rule Set Name": "", "Rule1：Corresponding to effective date or period end date": "Rule1：Corresponding to effective date or period end date", "Rule2：Corresponding to effective date or next date of period end date": "Rule2：Corresponding to effective date or next date of period end date", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date", "rules": "", "SA Agreement": "SA Agreement", "SA by Layer": "SA by Layer", "SA Calculation Method": "", "SA Decreasing Order": "SA Decreasing Order", "SA Limit": "SA Limit", "SA Limit Type": "", "SA Multiplier": "", "SA Unit": "SA Unit", "SA Unit Content": "", "SA Valuation": "", "SA Valuations": "", "Same Premium Adjustment strategy for All Goods": "", "Same TB type Formula has duplicate record. Please check.": "Same TB type Formula has duplicate record. Please check.", "Save": "Save", "Save as Draft": "", "Save Illustration Sequence": "", "Save Sort": "Save Sort", "Save successfully": "Save successfully", "Save Successfully": "", "Saved successfully": "Saved successfully", "Scenario Code": "", "Scenario Code & Scenario Name": "", "Scenario code can not be duplicated": "", "Scenario Name": "", "Scheduled Arrival Time": "", "Scheduled Departure Time": "", "Search": "Search", "Search by Policy": "", "Search by Transportation": "", "Search Result": "", "Second Date": "Second Date", "Select": "Select", "Select All": "", "Select Benefit Factors": "", "Select Cancellation Type": "", "Select Claim Stack(Template)": "", "Select Component": "", "Select Factor": "", "Select Formula": "Select Formula", "Select Liability": "", "Select Parameter": "Select Parameter", "Select Ratetable": "", "Select the enumeration value you want.": "", "Select the Format you want": "Select the Format you want", "Selection": "", "Sequence": "", "Serial No.": "", "Service Fee Clawback": "", "Set": "", "Set Default Time": "", "Set Fixed Time": "", "Set Name": "", "Set1": "Set1", "Set2": "Set2", "Set3": "Set3", "Severity": "Severity", "Severity Definition": "", "Sex": "", "Short Term Premium": "", "Short Term Premium Calculation Method": "", "Single Top Up Type": "", "Source": "", "Specify the calculation order": "", "Stack Code": "Stack Code", "Stack Component Name": "", "Stack Name": "Stack Name", "Stack Template Code": "", "Stack Template Name": "", "Stack Type": "Stack Type", "Stack Unit": "Stack Unit", "Stack Unit Type": "", "Stack Value": "Stack Value", "Stack Value Type": "Stack Value Type", "Stacks Related to Special Agreements": "", "Standard Premium": "", "Start date": "", "Start Date": "", "Starting Scenario": "", "Status": "Status", "Step": "Step", "Step 1": "Step 1", "Step 1 Download Test Data Template": "Step 1 Download Test Data Template", "Step 1: Input Value for Factors in the Page": "", "Step 2": "Step 2", "Step 2 Upload Test Data File": "Step 2 Upload Test Data File", "Step 2: Input Value for Factors in the Excel": "", "Step 3 Download Test Result": "Step 3 Download Test Result", "Step 3: Testing Result": "", "Step result is duplicated with paramater, please modify.As follows": "Step result is duplicated with paramater, please modify.As follows", "Strategy": "", "Strategy Code": "Strategy Code", "Strategy Name": "Strategy Name", "Sub Formula": "", "Sub POS Effective Date": "", "Submit": "Submit", "Submit Product": "Submit Product", "Submit successfully": "", "Submit Successfully": "Submit Successfully", "Summary": "", "Support entering a regular expression to control the input rules for Account Number by each Bank. For example, if Account Number should only allow 10 to 12 digits(number), you can configure it as ^\\d{10,12}$ or ^[0-9]{10,12}$.": "", "support list calculation including +,-,*,/": "", "Surgery": "Surgery", "Surgery Configuration Method": "Surgery Configuration Method", "Surgery Customized Data Details": "", "Surgery Level": "Surgery Level", "Surgery Level cannot be empty": "Surgery Level cannot be empty", "Surgery Tag1 cannot be empty": "", "Surgery Tag2 cannot be empty": "", "Survival Benefit": "Survival Benefit", "Survival Benefit Leading Days": "Survival Benefit Leading Days", "Switching the method of diagnosis configuration will auto clear the configured data.": "Switching the method of diagnosis configuration will auto clear the configured data.", "Switching the method of surgery configuration will auto clear the configured surgery data.": "Switching the method of surgery configuration will auto clear the configured surgery data.", "System Data": "", "System Data Value": "", "System error": "", "System is abnormal, please try again later.": "System is abnormal, please try again later.", "System pre-set data": "System pre-set data", "System support to configure linkage between following fields": "System support to configure linkage between following fields", "System will automatically conduct batch test when test data is uploaded successfully.": "System will automatically conduct batch test when test data is uploaded successfully.", "System will generate a test data template automatically based on parameters used in the formula.": "System will generate a test data template automatically based on parameters used in the formula.", "tables": "", "Tag Definition": "", "Tag definition cannot be empty": "", "Tag1": "Tag1", "Tag2": "Tag2", "Target Currency": "", "Target Date": "Target Date", "Tax Collection Rule": "Tax Collection Rule", "Tax Rate": "Tax Rate", "Tax Setting": "Tax Setting", "Tax Type": "Tax Type", "Tax Value Type": "Tax Value Type", "TB Payable for Claim from": "TB Payable for Claim from", "TB Payable for Surrender from": "TB Payable for Surrender from", "TB Type": "TB Type", "Template change will influence object and liability setup.": "Template change will influence object and liability setup.", "Template Code": "", "Template Code / Name": "", "Template Content": "", "Template Correlation Info": "", "Template Description": "", "Template Name": "", "Template Status": "", "tenant": "Tenant", "Tenant Data Configuration": "Tenant Data Configuration", "Terminal Bonus": "Terminal Bonus", "Terminal Bonus Indicator": "Terminal Bonus Indicator", "Terminate Liability After Claim Waiver": "Terminate Liability After <PERSON><PERSON><PERSON>", "Terminate Policy After Terminate Liability": "", "Terminate Product After Terminate Liability": "Terminate Product After Terminate Liability", "Terminate Related Liability": "", "Terminated Reason": "", "Test": "Test", "Test Result": "", "The “DCA frequency” table should at least have one record.": "", "The automatic fund rebalancing will only occur when the portfolio variance from the pre-specified premium allocationt exceeds certain range.": "", "The code could only contain letters, numbers, and underscores (_).": "", "The component code could only contain letters, numbers, and underscores (_).": "", "The content of the page has been modified, are you sure you want to leave": "", "The content of the page has been modified, are you sure you want to leave without saving?": "", "The coverage period agreement will be deleted": "The coverage period agreement will be deleted", "The current configuration cannot be modified after submission, confirm to continue?": "The current configuration cannot be modified after submission, confirm to continue?", "The fixed due date refers to the same day of each month. If certain month doesn't have the date, it will be the last day of that month.": "", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "", "The following benefit option(s) have been configured. The uploaded template will overwrite the data. Please confirm to proceed.": "", "The following calculation factors are not found in structure.": "The following calculation factors are not found in structure.", "The following products have used this formula. Editing formula and the following product will be affected. Please check before change:": "", "The formula code could only contain letters, numbers, and underscores (_).": "The formula code could only contain letters, numbers, and underscores (_).", "The General category can only be referenced in formula definitions and is not limited to any specific formula category.": "", "The indicator of include or exclude is mandatory for diagnosis code configuration.": "The indicator of include or exclude is mandatory for diagnosis code configuration.", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59", "The name format will follow the sequence below, the sequence can be manully adjusted.": "The name format will follow the sequence below, the sequence can be manully adjusted.", "The object configured in {{liabilityId}} {{liabilityName}} does not exist in the product.": "", "The operator used for exponentiation. It raises a number to the power of another number. For example: 2*4 = 2*2*2*2 = 16": "", "The policy of GroupEB does not support the process of deactivating the insured": "", "The product code could only contain letters, numbers, and underscores (_).": "The product code could only contain letters, numbers, and underscores (_).", "The ratetable code could only contain letters, numbers, and underscores (_).": "The ratetable code could only contain letters, numbers, and underscores (_).", "The rateTable is used and affected in several places below. Please confirm changes.": "", "The result of formula is repeated naming(case insensitive)": "The result of formula is repeated naming(case insensitive)", "The second value should be greater than or equal to the first value.": "The second value should be greater than or equal to the first value.", "The second value should greater than the first value": "The second value should greater than the first value", "The selected formula has been updated. Please reselect.": "", "The selected rate table has been updated. Please reselect.": "", "The stack code could only contain letters, numbers, and underscores (_).": "", "The table contains an extensive amount of data and cannot be previewed here. Please use the download button to obtain and review the file locally": "", "The template code could only contain letters, numbers, and underscores (_).": "", "The template has been referenced and cannot be deleted.": "", "The template has been referenced and cannot be edited.": "", "The template has been used by following products. Please confirm if any deletion is required.": "", "The template has been used by following products. Please confirm if any modification is required.": "", "The Usage Premium Start Date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "", "The value of the last step must be 'final', and the value of other steps cannot be 'final', 'final' is case insensitive": "The value of the last step must be 'final', and the value of other steps cannot be 'final', 'final' is case insensitive", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "", "There no found.": "", "Third Party Agreement": "", "This agreement is replaced by \"Claim Eligibility and Policy Matching\". Please use the new one if needed.": "This agreement will be replaced by a new one--Claim Eligibility and Certificate Matching. Please use the new agreement if needed.", "This component has already been used and cannot be modified or deleted.": "", "This conditional factor's value already exists, please modify it.": "", "This configuration can only be configured once, and cannot be modified after the configuration is completed.": "", "This formula is referenced as a sub-formula in below. Please update the following parent formulas after saving this formula.": "", "This Illustration Scenario is already in use under a Benefit Illustration Item. Please disassociate it from the Benefit Illustration Item first.": "", "This insurable interest has been used in the following package: [{{packages}}], please delete insurable interest in Package first.": "This insurable interest has been used in the following package: [{{packages}}], please delete insurable interest in Package first.", "This liability has been used in the following package: [{{packages}}], please delete liability in Package first.": "This liability has been used in the following package: [{{packages}}], please delete liability in Package first.", "This product is read only.": "", "This ratetable is relied on": "", "This record has been deleted from premium period configuration. Please manually delete this record to ensure data consistency.": "This record has been deleted from premium period configuration. Please manually delete this record to ensure data consistency.", "This record was configured based on old version. Currently, viewing and editing are not supported.": "This record was configured based on old version. Currently, viewing and editing are not supported.", "This rider product has been used in the following package: [{{packages}}], please delete rider product in Package first.": "This rider product has been used in the following package: [{{packages}}], please delete rider product in Package first.", "This section configures the system rules when installment premium is not paid on time.": "This section configures the system rules when installment premium is not paid on time.", "This section configures the system rules when the total investment value (policy value) is insufficent to pay charges.": "This section configures the system rules when the total investment value (policy value) is insufficent to pay charges.", "This section is required for regular paid product. No need for single premium product.": "This section is required for regular paid product. No need for single premium product.", "Time Zone": "", "Times Type": "Times Type", "Tip1： Support calculation sign including": "Tip1： Support calculation sign including", "Tip2： Support embedded calculation method including": "Tip2： Support embedded calculation method including", "Tip3： Date Format only accept": "Tip3： Date Format only accept", "Titile": "<PERSON>itile", "Tiv（Mock）": "", "to": "", "To define the premium allocation strategy.": "To define the contribution allocation strategy.", "To define whether claim reserve equals 0 is allowed for claim acceptance.": "To define whether claim reserve equals 0 is allowed for claim acceptance.", "To reduce duplicate configurations, for POS Items applying to the entire policy, please go to [Package Management - Policy Change] section.": "", "Top Up Frequency": "", "Trace": "Trace", "Transaction Type": "Transaction Type", "Transportation Data": "", "Transportation Date": "", "Transportation Facility": "", "Transportation No.": "", "Transportation Number": "", "Transportation Status": "", "Transportation Type": "", "Treatment Condition": "", "Trigger Additional Maturity Benefit": "", "Trigger Cash Refund": "", "Trigger Type": "Trigger Type", "Try a different keyword.": "", "Type": "", "Type of Amount Per Period": "", "Type of Loss": "", "Uncollected Premium Deduction": "Uncollected Contribution Deduction", "Under this scenario, the policy Lapse process will be trigger when TIV is un-sufficient. Please make related configuration at \"Handling of TIV Insufficient\" section.": "Under this scenario, the policy Lapse process will be trigger when TIV is un-sufficient. Please make related configuration at \"Handling of TIV Insufficient\" section.", "Under this scenario, the policy will keep effective when enter premium holiday. And once the allowed premium holiday period is used up, system will lapse policy when premium is unpaid.": "Under this scenario, the policy will keep effective when enter premium holiday. And once the allowed premium holiday period is used up, system will lapse policy when premium is unpaid.", "Underwriting Enumerated Values": "", "Unit Type": "", "Unpaid Bill Cancellation Rule": "", "Upload check failed, please download the error file to check!": "Upload check failed, please download the error file to check!", "Upload Data": "", "Upload Failed": "Upload Failed", "Upload File": "", "Upload File Infomation": "", "Upload Files": "", "Upload Ratetable": "Upload Ratetable", "Upload Success": "Upload Success", "Upload successfully": "Upload successfully", "Upload Successfully": "", "Uploading, please refresh the page to check the results later.": "", "URL": "", "Usage Based": "", "Usage Based Insurance Agreement": "Usage Based Takaful Agreement", "Usage Based Product": "Usage Based Product", "Usage Premium Start Date": "Usage Contribution Start Date", "Use customized data": "Use customized data", "Using Insurable Interest": "Using Insurable Interest", "UW Risk SA Setting": "UW Risk SA Setting", "Validation Details": "Validation Details", "value": "", "Value": "Value", "Value Assignment": "", "Value Type": "Value Type", "Vehicle Claim Info": "Vehicle Claim Info", "Vehicle Database Model": "Vehicle Database Model", "Vehicle Database Model needs to be configured as “Cascade” if there exists multiple vehicle databases.": "Vehicle Database Model needs to be configured as “Cascade” if there exists multiple vehicle databases.", "Vehicle Database Name": "Vehicle Database Name", "Vehicle Database Name has already existed.": "Vehicle Database Name has already existed.", "Vehicle Databases": "Vehicle Databases", "Vehicle Elements Relationship Matrix": "", "Vehicle Market Value": "Vehicle Market Value", "Version": "Version", "Version Time": "", "Vesting Agreement": "", "View": "View", "View Claim Stack Template": "", "View Component": "", "View Detail": "", "View Enumerated Fields Dependency": "", "View Formula": "View Formula", "View Liability": "View Liability", "View My Creations": "", "View Ratetable": "View Ratetable", "View Reference": "", "View Structure": "", "VIRTUAL MAIN": "", "Virtual Main Benefit": "Virtual Main Benefit", "Wait Until Grace Period Expires": "Wait Until Grace Period Expires", "Waiting Period": "Waiting Period", "Waiting Period of New Business": "", "Waiting Period of Reinstatement": "", "Warning Notification": "", "Week": "", "When add a record for Liability A and Liability B as 1,000": "", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month)": "", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month) ": "", "When customer choose pre-defined investment strategy, the fund appointment rate for both premium direction and portfolio rebalance will follow the configuration on strategy.": "", "When issue a policy, save illustration data in the policy.": "", "When it is closed,  system will buy target fund chosen by customer directly after policy issuance. No delay of the investment.": "", "When premium comes in, system will fill in the installment premium due": "When contribution comes in, system will fill in the installment contribution due", "Whether After Campaign Discount": "Whether After Campaign Discount", "Whether to use premium adjustment": "", "With Extra Premium": "", "With Guarantee Period": "", "With No Lapse Guarantee": "With No Lapse Guarantee", "Within No Lapse Guarantee": "Within No Lapse Guarantee", "Work Time": "", "Year": "Year", "Year(s)": "Year(s)", "yes": "Yes", "Yes，Charge Amount is": "", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "", "You can only upload xls, xlsx": "You can only upload xls, xlsx", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.", "You can only upload XSL": "", "You don't have to configure it if you don't need it": "", "You have unsubmitted information. Do you want to discard it?": "You have unsubmitted information. Do you want to discard it?", "You haven,t configured your bank information yet": ""}