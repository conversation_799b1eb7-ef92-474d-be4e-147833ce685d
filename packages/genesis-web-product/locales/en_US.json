{"- -": "--", "--": "--", "'Charges Deducted From Policy Value' includes the following types of charges: Policy Admin Fee, Account Management Fee，ETF Broker Fee, ETF Management Fee, Premium Holiday Charge, Cost of Insurance, Rider Premium, Partial Surrender Charge": "'Charges Deducted From Policy Value' includes the following types of charges: Policy Admin Fee, Account Management Fee，ETF Broker Fee, ETF Management Fee, Premium Holiday Charge, Cost of Insurance, Rider Premium, Partial Surrender Charge", "{{filename}} will be uploaded, the data is {{count}} records, please make sure to upload.": "{{filename}} will be uploaded, the data is {{count}} records, please make sure to upload.", "%": "%", "• Accumulate each liability SA -> Product SA is 2,000": "• Accumulate each liability SA -> {{product,product}} SA is 2,000", "• Accumulate federated liability SA -> Product SA is 1,000": "• Accumulate federated liability SA -> {{product,product}} SA is 1,000", "Abbreviation": "Abbreviation", "Accelerating/Restoration": "Accelerating/Restoration", "Accident Index Formula": "Accident Index Formula", "Account Lockout Period": "Account Lockout Period", "Account Lockout Period (minimum value is 15 min)": "Account Lockout Period (minimum value is 15 min)", "Accumulated Fund Dividend (Mock)": "Accumulated Fund Dividend (Mock)", "Accumulated ILPBonus (Mock)": "Accumulated ILPBonus (Mock)", "Accumulated ILPCharges (Mock)": "Accumulated ILPCharges (Mock)", "Accumulated POS Withdrawal (Mock)": "Accumulated P<PERSON>drawal (Mock)", "Accumulated Premium (Mock)": "Accumulated Premium (Mock)", "Accumulated Withdrawal (Mock)": "Accumulated <PERSON><PERSON><PERSON> (Mock)", "Actual Arrival Time": "Actual Arrival Time", "Actual Departure Time": "Actual Departure Time", "Add": "Add", "Add a Condition": "Add a Condition", "Add a set of conditions": "Add a set of conditions", "Add Amount": "Add Amount", "Add Component": "Add Component", "Add Condition": "Add Condition", "Add Enumerated Fields Dependency": "Add Enumerated Fields Dependency", "Add Factor": "Add Factor", "Add Formula": "Add Formula", "Add New": "Add New", "Add New as the Sub Level": "Add New as the Sub Level", "Add New Charge": "Add New Charge", "Add New Claim Stack Template": "Add New Claim Stack Template", "Add New Condition": "Add New Condition", "Add New Formula": "Add New Formula", "Add New Liability": "Add New Liability", "Add New Product": "Add New {{product,product}}", "Add New Ratetable": "Add New Ratetable", "Add New Risk Sub Category": "Add New Risk Sub Category", "Add New Set": "Add New Set", "Add New Version": "Add New Version", "Add Ratetable": "Add Ratetable", "Add Strategy": "Add Strategy", "Add successfully": "Add successfully", "Add Transportation Data": "Add Transportation Data", "Additional Clause": "Additional Clause", "Additional Clause Name": "Additional Clause Name", "Additional Condition Factor": "Additional Condition Factor", "Additional Condition Type": "Additional Condition Type", "Additional Grace Period": "Additional Grace Period", "Additional MB Formula": "Additional MB Formula", "Additional Participation Conditions": "Additional Participation Conditions", "Additional Trigger Conditions": "Additional Trigger Conditions", "Addresses at the selected level and below can be selected in the drop-down box and support free editing.": "Addresses at the selected level and below can be selected in the drop-down box and support free editing.", "Adjust to 1st if Effective Day Not in Expiry Month": "Adjust to 1st if Effective Day Not in Expiry Month", "Adjusted Market Value Floating Ratio": "Adjusted Market Value Floating Ratio", "Advanced Configuration": "Advanced Configuration", "After": "After", "After (days)": "After (days)", "After ETI": "After ETI", "After Paid Up": "After Paid Up", "After(Days)": "After(Days)", "Age": "Age", "Age Calculation": "Age Calculation", "Age Calculation Basis": "Age Calculation Basis", "Age Validation": "Age Validation", "Age Validation Type": "Age Validation Type", "ageCalcBasis": "ageCalcBasis", "Agent Info": "Agent Info", "All Creations": "All Creations", "Allow Auto ETI": "Allow Auto ETI", "Allow Auto ETI Tooltip": "When you allow ETI, then system will trigger the ETI process after grace period end. If the {{product,product,lowerCase}} also allow other auto nonforfeiture options, then please set the default setting at “Default ANFO section”.", "Allow Auto Paid Up": "Allow Auto Paid Up", "Allow Auto Paid Up Tooltip": "When you allow auto paid up, then system will trigger the reduced paid up process after grace period end. If the {{product,product,lowerCase}} also allow other autorcleOutlined nonforfeiture options, then please set the default setting at “Default ANFO section”.", "Allow Automatic Premium Loan": "Allow Automatic Premium Loan", "Allow claim reserve equal 0": "Allow claim reserve equal 0", "Allow Claim Reserve Equal 0": "Allow Claim Reserve Equal 0", "Allow collect outstanding premium after policy termination": "Allow collect outstanding premium after policy termination", "Allow CPF Payment": "Allow CPF Payment", "Allow Flexible Premium Payment": "Allow Flexible Premium Payment", "Allow Minimum Investment Period": "Allow Minimum Investment Period", "Allow Minimum Reinstatement?": "Allow Minimum Reinstatement?", "Allow Parital APL": "Allow Parital APL", "Allow Partial Filling the ILP Premium": "Allow Partial Filling the ILP Premium", "Allow Policy Loan": "Allow Policy Loan", "Allow POS Effective Without Collection": "Allow POS Effective Without Collection", "Allow Premium Decrease": "Allow Premium Decrease", "Allow Premium Increase": "Allow Premium Increase", "Allow Renewal": "Allow <PERSON>", "Allow SA Decrease": "Allow SA Decrease", "Allow SA Increase": "Allow SA Increase", "Allow to apply waive the premium liability": "Allow to apply waive the premium liability", "allow to change premium": "allow to change premium", "allow to change SA": "allow to change SA", "Allow to Waive Interest": "Allow to Waive Interest", "allow to withdraw CV": "allow to withdraw CV", "Allow Uncollected Premium Deduction from POS Refund": "Allow Uncollected Premium Deduction from POS Refund", "Allow Vesting": "Allow Vesting", "Allowance Agreement": "Allowance Agreement", "Allowance Amount": "Allowance Amount", "Allowance Amount Type": "Allowance Amount Type", "Allowance Configuration": "Allowance Configuration", "Allowance Type": "Allowance Type", "Allowance Unit Type": "Allowance Unit Type", "Allowance Unit Value": "Allowance Unit Value", "Allowance Value Type": "Allowance Value Type", "Allowed Investment Strategy": "Allowed Investment Strategy", "Allowed Range for Admission Time Compare to Incident Date (Hospitalization)": "Allowed Range for Admission Time Compare to Incident Date (Hospitalization)", "Allowed Range for Admission Time to Incident Date (Medical Bill)": "Allowed Range for Admission Time to Incident Date (Medical Bill)", "Allowed Range for Death Date Compare to Incident Date": "Allowed Range for Death Date Compare to Incident Date", "Allowed Range for End Date Compare to Incident Date(Allowance)": "Allowed Range for End Date Compare to Incident Date(Allowance)", "Allowed Range for Outpatient Date to Incident Date (Medical Bill)": "Allowed Range for Outpatient Date to Incident Date (Medical Bill)", "Allowed Range for Start Date Compare to Incident Date(Allowance)": "Allowed Range for Start Date Compare to Incident Date(Allowance)", "Allowed Vesting Age": "Allowed Vesting Age", "Amount Per Period": "Amount Per Period", "Amount Per Time": "Amount Per Time", "Amount Range": "Amount Range", "AND": "AND", "Annual Interest Rate": "Annual Interest Rate", "Annuity": "Annuity", "Annuity Leading Days": "Annuity Leading Days", "Any modification of the current work shifts will impact the work schedules used, please check": "Any modification of the current work shifts will impact the work schedules used, please check", "Applicable Business Type": "Applicable Business Type", "Applicable Goods": "Applicable Goods", "Applicable only to deductible and co-pay types of stacks": "Applicable only to deductible and co-pay types of stacks", "Applicable Target": "Applicable Target", "Applied Goods": "Applied Goods", "Applied Unit": "Applied Unit", "Approve": "Approve", "Are you sure delete this record?": "Are you sure delete this record?", "Are you sure to clear all records?": "Are you sure to clear all records?", "Are you sure to clear this record?": "Are you sure to clear this record?", "Are you sure to clear this section?": "Are you sure to clear this section?", "Are you sure to delete this record?": "Are you sure to delete this record?", "Are you sure to delete this template?": "Are you sure to delete this template?", "Are you sure to delete this version?": "Are you sure to delete this version?", "Are you sure to enable this feature? Once enabled, it cannot be turned off. Please confirm to proceed.": "Are you sure to enable this feature? Once enabled, it cannot be turned off. Please confirm to proceed.", "Are you sure to export all configurations?": "Are you sure to export all configurations?", "Are you sure to import all configurations?": "Are you sure to import all configurations?", "Arrival Airport": "Arrival Airport", "Arrival City/Place": "Arrival City/Place", "Attach to Policy": "Attach to Policy", "Auto Deduction Date": "Auto Deduction Date", "Auto Deduction Date Compare to Due Date": "Auto Deduction Date Compare to Due Date", "Auto Deduction Days Before Due Date": "Auto Deduction Days Before Due Date", "Auto Repay APL/PL": "Auto Repay APL/PL", "Auto Termination Rule": "Auto Termination Rule", "Auto Trigger Premium Holiday": "Auto Trigger Premium Holiday", "Automatic Adjustment": "Automatic Adjustment", "Available DCA Frequency": "Available DCA Frequency", "Available Option": "Available Option", "Back": "Back", "Back to Search": "Back to Search", "Bank": "Bank", "Bank Branch Address": "Bank Branch Address", "Bank City": "Bank City", "Basic": "Basic", "Basic Component": "Basic Component", "Basic Information": "Basic Information", "Batch Test": "Batch Test", "Before (days)": "Before (days)", "Before(Days)": "Before(Days)", "Benefit Calculation": "Benefit Calculation", "Benefit Illustration": "Benefit Illustration", "Benefit Illustration Configuration": "Benefit Illustration Configuration", "Benefit Illustration Test": "Benefit Illustration Test", "Benefit Option": "Benefit Option", "Benefit Option  Name": "Benefit Option  Name", "Benefit Option Code": "Benefit Option Code", "Benefit Option Details": "Benefit Option Details", "Benefit Option Name": "Benefit Option Name", "Benefit Options cannot be repeated.": "Benefit Options cannot be repeated.", "Benefit Payment": "Benefit Payment", "Benefit Schedule & Rate": "Benefit Schedule & Rate", "Bill Conditions": "Bill Conditions", "Bill Item": "<PERSON>", "Bill Item Sort": "<PERSON>", "Bill Object": "<PERSON>", "Birthday": "Birthday", "Bonus Allocation Date": "Bonus Allocation Date", "Bonus Code": "Bonus Code", "Bonus Date": "Bonus Date", "Bonus Formula": "Bonus Formula", "Bonus Frequency": "Bonus Frequency", "Bonus Handling After ETI": "Bonus Handling After ETI", "Bonus Handling After Paid Up": "Bonus Handling After Paid Up", "Bonus Name": "Bonus Name", "Bonus Option": "Bonus Option", "Bonus Period": "Bonus Period", "Bonus Period Factor": "Bonus Period Factor", "Bonus Period(In Years)": "Bonus Period(In Years)", "Bonus Rate Declaration": "Bonus Rate Declaration", "Bonus Rate Declaration List": "Bonus Rate Declaration List", "Bonus Rate Type": "Bonus Rate Type", "Bonus Start from": "Bonus Start from", "Bonus Type": "Bonus Type", "Bonus/Malus Table": "Bonus/Malus Table", "bonusRateType": "bonusRateType", "Both: Compliance decision can be provided by user or external triggered, system will take the previous one between the two values.": "Both: Compliance decision can be provided by user or external triggered, system will take the previous one between the two values.", "browse": "browse", "Browse": "Browse", "Bundle Rules": "Bundle Rules", "Business": "Business", "Business Component": "Business Component", "Business Date": "Business Date", "Business Element Group": "Business Element Group", "Business Interruption Loss Configuration": "Business Interruption Loss Configuration", "Business Scenario": "Business Scenario", "Business Time": "Business Time", "Business Transaction": "Business Transaction", "Business Type": "Business Type", "Buy/Sell Unit Price": "Buy/Sell Unit Price", "By": "By", "Caculation": "Caculation", "Calculate from Standard Premium": "Calculate from Standard Premium", "Calculate Product SA by Liability SA": "Calculate {{product,product}} SA by Liability SA", "Calculate Success": "Calculate Success", "Calculation": "Calculation", "Calculation Accuracy": "Calculation Accuracy", "Calculation Accuracy List": "Calculation Accuracy List", "Calculation Basis": "Calculation Basis", "Calculation Basis Of Max Insured’s Entry Age": "Calculation Basis Of Max Insured’s Entry Age", "Calculation Basis Of Min Insured’s Entry Age": "Calculation Basis Of Min Insured’s Entry Age", "Calculation Details": "Calculation Details", "Calculation Direction": "Calculation Direction", "Calculation Frequency": "Calculation Frequency", "Calculation Level": "Calculation Level", "Calculation Management": "Calculation Management", "Calculation Method": "Calculation Method", "Calculation Order": "Calculation Order", "Calculation Priority Order": "Calculation Priority Order", "Calculation Type": "Calculation Type", "Calendar Date (per year)": "Calendar Date (per year)", "Can,t find stack": "Can,t find stack", "Cancel": "Cancel", "Cancel pin": "Cancel pin", "Cancellation Reason": "Cancellation Reason", "Cancellation Sub Reason": "Cancellation Sub Reason", "Cancellation Type": "Cancellation Type", "Cancellation Type & Cancellation Reason": "Cancellation Type & Cancellation Reason", "Cancellation Type Reason": "Cancellation Type Reason", "Cascade": "Cascade", "Cash Bonus": "Cash Bonus", "Cash Bonus Allowed": "Cash Bonus Allowed", "Cash Refund Formula": "Cash Refund Formula", "Cash Value Calculation Level": "Cash Value Calculation Level", "cashValueTypes": "cashValueTypes", "Change installment premium calculation method will clear the existing configuration, please confirm.": "Change installment premium calculation method will clear the existing configuration, please confirm.", "Change the switch will clear the current configured data, are you sure to do it?": "Change the switch will clear the current configured data, are you sure to do it?", "Changes have not been saved.": "Changes have not been saved.", "Changes made after submission will not be revoked": "Changes made after submission will not be revoked", "Changing vehicle database model or fields will clear the existing configuration, please confirm.": "Changing vehicle database model or fields will clear the existing configuration, please confirm.", "Channel Enumerated Values": "Channel Enumerated Values", "Channel Info": "Channel Info", "Charge": "Charge", "Charge Amount After": " ", "Charge Code": "Charge Code", "Charge code is duplicated, please check.": "Charge code is duplicated, please check.", "Charge Code should start with ILPC and followed by 3 digits": "Charge Code should start with ILPC and followed by 3 digits", "Charge For Investment Product": "Charge For Investment {{product,product}}", "Charge Formula": "Charge Formula", "Charge Free Month": "Charge Free Month", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value.": "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value.", "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value. ": "Charge free month is existing, charge free should be at least available from 1st policy month. Please fill in “Charge free start from” value. ", "Charge Free Period": "Charge Free Period", "Charge Free Start From": "Charge Free Start From", "Charge Handling": "Charge Handling", "Charge Period Factor": "Charge Period Factor", "Charge Type": "Charge Type", "Checking whether there are any products using this template...": "Checking whether there are any {{product,product,lowerCase}}s using this template...", "Child Field": "Child Field", "Child Field Name": "Child Field Name", "Cited Times": "Cited Times", "Claim Agreement": "Claim Agreement", "Claim Bill Item Setting": "Claim <PERSON>", "Claim Calculation": "Claim Calculation", "Claim Clawback": "<PERSON><PERSON><PERSON>", "Claim Compensation Amount": "Claim Compensation Amount", "Claim Eligibility and Policy Matching": "Claim Eligibility and Policy Matching", "Claim Incident Type": "Claim Incident Type", "Claim Limit": "<PERSON><PERSON><PERSON>", "Claim Reserve Setting": "Claim Reserve Setting", "Claim Scenario": "<PERSON><PERSON><PERSON>", "Claim Section": "Claim Section", "Claim Stack": "<PERSON><PERSON><PERSON>", "Claim Stack Correlation": "<PERSON><PERSON><PERSON> Correlation", "Claim Stack Correlation Info": "<PERSON><PERSON><PERSON> Correlation Info", "Claim Stack Definition": "<PERSON><PERSON><PERSON>ack Definition", "Claim Stack Info": "<PERSON><PERSON><PERSON>", "Claim Stack Management": "<PERSON><PERSON><PERSON>ack Management", "Claim Stack Query": "<PERSON><PERSON><PERSON>", "Claim Stack Template": "<PERSON><PERSON><PERSON>", "Claim Stack Template Management": "<PERSON><PERSON><PERSON>ack Template Management", "Claim Status": "Claim Status", "Claim Type": "Claim Type", "Claim type of the selected liability is not supported, please Confirm.": "Claim type of the selected liability is not supported, please Confirm.", "Claimable Loss Party": "Claimable Loss Party", "Claimable Loss Party Type": "Claimable Loss Party Type", "claimHistoryTimes（Mock）": "claimHistoryTimes（<PERSON><PERSON>）", "Claims Ratio": "<PERSON><PERSON><PERSON>", "claimStatus": "claimStatus", "Clear": "Clear", "Clear All": "Clear All", "Clear Factor": "Clear Factor", "Clear successfully": "Clear successfully", "clear the all datas will delete this version, are you sure to delete all?": "clear the all datas will delete this version, are you sure to delete all?", "Clear the diagnosis configuration": "Clear the diagnosis configuration", "Clear the surgery configuration": "Clear the surgery configuration", "Clear the surgery tag1 configuration": "Clear the surgery tag1 configuration", "Clear the surgery tag2 configuration": "Clear the surgery tag2 configuration", "Click Edit on the right to configure multiple-language for the name": "Click Edit on the right to configure multiple-language for the name", "Click here to upload": "Click here to upload", "Click or drag the file here to upload": "Click or drag the file here to upload", "Click to the right of the title to edit the name format value": "Click to the right of the title to edit the name format value", "Close": "Close", "code": "code", "Code": "Code", "Collect": "Collect", "Collect Extra Installment Premium?": "Collect Extra Installment Premium?", "Collect Overdue Premium Interest": "Collect Overdue Premium Interest", "Collect the entire premium when a claim is made": "Collect the entire premium when a claim is made", "Collection Payment Info": "Collection Payment Info", "Combination Relationship": "Combination Relationship", "Combined liability cash value calculation could not contain optional liability.": "Combined liability cash value calculation could not contain optional liability.", "Commission Clawback": "Commission Clawback", "Common Enumerated Values": "Common Enumerated Values", "Comparison Type": "Comparison Type", "Compensation Bill Item": "Compensation Bill Item", "Compensation Claim Info": "Compensation Claim Info", "Compliance Enumerated Values": "Compliance Enumerated Values", "Component": "Component", "Component Code": "Component Code", "Component Description": "Component Description", "Component Instruction": "Component Instruction", "Component Name": "Component Name", "Component Query": "Component Query", "Condition": "Condition", "Conditional UW Agreement": "Conditional UW Agreement", "Conditions": "Conditions", "Configuration History": "Configuration History", "Configure": "Configure", "Configure Benefit Factors": "Configure Benefit Factors", "Configure Benefit Option Detail": "Configure Benefit Option Detail", "Configure Payment & Collection Method": "Configure Payment & Collection Method", "Configure product categories for the tenant.": "Configure {{product,product,lowerCase}} categories for the tenant.", "Configure the association between fields": "Configure the association between fields", "Confirm": "Confirm", "Confirm Export": "Confirm Export", "Confirm Import": "Confirm Import", "Confirm to submit the product?": "Confirm to submit the {{product,product,lowerCase}}?", "Confirmation Period": "Confirmation Period", "Conversion Date": "Conversion Date", "Copy Formula": "Copy Formula", "Copy Link": "Copy Link", "Copy Reminder": "<PERSON><PERSON>", "Copy Successfully": "<PERSON><PERSON> Successfully", "Copy to New Product": "Copy to New {{product,product}}", "Copy to New Version": "Copy to New Version", "Coverage Data Change Type": "Coverage Data Change Type", "Coverage Date Change Type": "Coverage Date Change Type", "Coverage Period": "Coverage Period", "Coverage Period Fields to Revise": "Coverage Period Fields to Revise", "Coverage Period Range": "Coverage Period Range", "Coverage Period Type": "Coverage Period Type", "Coverage Period Value Type": "Coverage Period Value Type", "Coverage Type": "Coverage Type", "Create": "Create", "Create successfully": "Create successfully", "Create_time": "Create Time", "Creator": "Creator", "Critical illness name": "Critical illness name", "Critical illness stage": "Critical illness stage", "Customer Enumerated Values": "Customer Enumerated Values", "Customized Data": "Customized Data", "CV Formula of Basic SA": "CV Formula of Basic SA", "CV Formula of Campaign Free SA": "CV Formula of Campaign Free SA", "CV Formula of Reversionary Bonus": "CV Formula of Reversionary Bonus", "CV is sufficient for one period APL": "CV is sufficient for one period APL", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)", "Data filling": "Data filling", "Data has existed. please check!": "Data has existed. please check!", "Data Type": "Data Type", "Day of Fixed Due Date": "Day of Fixed Due Date", "Day of Usage Premium Start Date": "Day of Usage Premium Start Date", "Day(s)": "Day(s)", "Day(s) After Due Date": "Day(s) After Due Date", "Day(s) Before Due Date": "Day(s) Before Due Date", "Days": "Days", "Days Type": "Days Type", "DCA Amount": "DCA Amount", "DCA Frequency": "DCA Frequency", "DCA frequency cannot be lower than premium frequency.": "DCA frequency cannot be lower than premium frequency.", "DCA Frequency Type": "DCA Frequency Type", "Deactivate Insured": "Deactivate Insured", "Deactivate Insured After Fully Claimed": "Deactivate Insured After Fully Claimed", "Deduction cut-off date upon policy terminates": "Deduction cut-off date upon policy terminates", "Deduction Date": "Deduction Date", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.", "Deduction Source": "Deduction Source", "Default ANFO Setting Up": "Default ANFO Setting Up", "Default as Reinvestment if dividend amount per fund is less than": "Default as Reinvestment if dividend amount per fund is less than", "Default as tenant": "De<PERSON><PERSON> as tenant", "Default logic for refunded premium is confirmed instalment premium after incident date. If not, refund calculation formula needs to be configured here.": "Default logic for refunded premium is confirmed instalment premium after incident date. If not, refund calculation formula needs to be configured here.", "Default Option": "Default Option", "Default Time": "Default Time", "Default Time Editable": "Default Time Editable", "Defer Period": "Defer Period", "Deferred Interest": "Deferred Interest", "Define": "Define", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.", "Define Benefit Options": "Define Benefit Options", "Define Factor & Ratetable by Scenario": "Define Factor & Ratetable by Scenario", "Define Fund Calculation Basis for Each Transaction": "Define Fund Calculation Basis for Each Transaction", "Define Illustration Sequence": "Define Illustration Sequence", "Define the day before policy expired date and days after expired date to extract renewal policy": "Define the day before policy expired date and days after expired date to extract renewal policy", "Define the times of product sa and planned premium.": "Define the times of {{product,product,lowerCase}} sa and planned premium.", "Define whether it is the normal policy insure date or effective date should be within relevant master policy effective period when normal policy issuance.": "Define whether it is the normal policy insure date or effective date should be within relevant master policy effective period when normal policy issuance.", "Defined Application Elements": "Defined Application Elements", "Delay Type": "Delay Type", "delete": "Delete", "Delete Failed": "Delete Failed", "Delete Successfully": "Delete Successfully", "Delete successfully.": "Delete successfully.", "Delete will affect the associated work scheduling. Are you sure to delete it?": "Delete will affect the associated work scheduling. Are you sure to delete it?", "Deleted successfully": "Deleted successfully", "Deleting this record will affect the associated rider relationships. Please delete the related rider relationship records first.": "Deleting this record will affect the associated rider relationships. Please delete the related rider relationship records first.", "Departure Airport": "Departure Airport", "Departure City/Place": "Departure City/Place", "Departure Date": "Departure Date", "Description": "Description", "Deselect this goods will remove it from related bundle rules in step 2. Please confirm.": "Deselect this goods will remove it from related bundle rules in step 2. Please confirm.", "Diagnosis": "Diagnosis", "Diagnosis Code": "Diagnosis Code", "Diagnosis Configuration Method": "Diagnosis Configuration Method", "Diagnosis Description": "Diagnosis Description", "Diagnosis Set": "Diagnosis Set", "Digit": "Digit", "Disability Category": "Disability Category", "Disability Classification": "Disability Classification", "Disability Grade": "Disability Grade", "Disability Set": "Disability Set", "Discount Sequence": "Discount Sequence", "Discount Type": "Discount Type", "Disease Agreement": "Disease Agreement", "Disease Classification": "Disease Classification", "Distribution Method": "Distribution Method", "DMS": "DMS", "Dollar Cost Averaging Arrangement": "Dollar Cost Averaging Arrangement", "Down Sell Indicator": "<PERSON> Sell Indicator", "Download Result": "Download Result", "Download Successfully": "Download Successfully", "Download Template": "Download Template", "Draft": "Draft", "Drag to adjust the calculation order": "Drag to adjust the calculation order", "Due Date": "Due Date", "Due Date Rules": "Due Date Rules", "Dunning Rule": "Dunning Rule", "Duplicate data": "Duplicate data", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "Duplicate formula configuration for same formula category and sub category. Please edit original one.", "Duplicate formula configuration for same formula sub category and tax type. Please edit original one.": "Duplicate formula configuration for same formula sub category and tax type. Please edit original one.", "Duplicated data exists, please check the Original Currency and Target Currency.": "Duplicated data exists, please check the Original Currency and Target Currency.", "Duplicated Interest!": "Duplicated Interest!", "E.g.: If there exists multiple liabilities for one insured. User could define whether to deactivate this insured when one of the liability has been fully claimed.": "E.g.: If there exists multiple liabilities for one insured. User could define whether to deactivate this insured when one of the liability has been fully claimed.", "E.g.: If there exists multiple liabilities under one product. User could define whether to terminate this liability/Product when one of the liability has been fully claimed.": "E.g.: If there exists multiple liabilities under one {{product,product,lowerCase}}. User could define whether to terminate this liability/{{product,product}} when one of the liability has been fully claimed.", "Each premium frequency can only configure 1 DCA frequency record.": "Each premium frequency can only configure 1 DCA frequency record.", "EARLIEST OF": "EARLIEST OF", "Edit": "Edit", "Edit Charge": "Edit Charge", "Edit Claim Stack Template": "Edit <PERSON><PERSON><PERSON> Te<PERSON>late", "Edit Component": "Edit Component", "Edit Enumerated Fields Dependency": "Edit Enumerated Fields Dependency", "Edit Factor": "Edit Factor", "Edit Formula": "Edit Formula", "Edit Liability": "Edit Liability", "Edit Ratetable": "Edit Ratetable", "Edit Reminder": "<PERSON>minder", "Edit Tag Definition": "Edit Tag Definition", "Editing": "Editing", "EDITING": "EDITING", "Editing any formula or ratetable within the new or original product will impact its calculations.": "Editing any formula or ratetable within the new or original {{product,product,lowerCase}} will impact its calculations.", "Editing any formula or ratetable within the new or original version will impact its calculations.": "Editing any formula or ratetable within the new or original version will impact its calculations.", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "Editing benefit factors will clear the existing uploaded benefit details，please confirm.", "EFFECTIVE": "EFFECTIVE", "Effective Date Month End to Expiry Date Month End": "Effective Date Month End to Expiry Date Month End", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)", "Effective Date Rule": "Effective Date Rule", "Effective Period": "Effective Period", "Effective Time": "Effective Time", "Element": "Element", "Embedded Ratetable": "Embedded Ratetable", "Enable Green Card Issuance Logic for Non-EU Countries": "Enable Green Card Issuance Logic for Non-EU Countries", "Enable Non-Fleet Grade and Accident Index Configuration": "Enable Non-Fleet Grade and Accident Index Configuration", "End date": "End date", "Enumerated Fields Dependency": "Enumerated Fields Dependency", "Error": "Error", "Error Description": "Error Des<PERSON>", "error format, eg: 1/1.0/1.00": "error format, eg: 1/1.0/1.00", "Estimated Lapse Date Formula": "Estimated Lapse Date Formula", "Every": "Every", "Example": "Example", "Example: V01": "Example: V01", "Exception Output": "Exception Output", "Excess Premium Handling": "Excess Premium Handling", "Exchange Rate": "Exchange Rate", "Exchange Rate (Buying)": "Exchange Rate (Buying)", "Exchange Rate (Middle)": "Exchange Rate (Middle)", "Exchange Rate (Selling)": "Exchange Rate (Selling)", "Exchange Rate Query": "Exchange Rate Query", "Exclusion Indicator": "Exclusion Indicator", "Existing Stack in the Product": "Existing Stack in the {{product,product}}", "Exists duplicate value": "Exists duplicate value", "Exit Full Screen": "Exit Full Screen", "Expand": "Expand", "Expiry Date Adjustment": "Expiry Date Adjustment", "Expiry Date Calculation Method": "Expiry Date Calculation Method", "Expiry Date Rule": "Expiry Date Rule", "Expiry Time Agreement Type": "Expiry Time Agreement Type", "Export": "Export", "Export All": "Export All", "Export failed, please try again!": "Export failed, please try again!", "Export Operation Fail": "Export Operation Fail", "Export Range": "Export Range", "Export successfully!": "Export successfully!", "Export to CSV": "Export to CSV", "Extended Term": "Extended Term", "Extended Term Formula": "Extended Term Formula", "External: Compliance decision can only be provided by external triggered.": "External: Compliance decision can only be provided by external triggered.", "Extra Grace Period": "Extra Grace Period", "Extra Loading": "Extra Loading", "Extra Loading Calculation Method": "Extra Loading Calculation Method", "Extra Loading Indicator": "Extra Loading Indicator", "Extra Loading Type": "Extra Loading Type", "Extra Premium Formula Code": "Extra Premium Formula Code", "Extract Bill Date": "Extract Bill Date", "Extract Bill Days Before Due Date": "Extract Bill Days Before Due Date", "Extract day(s) must be greater than offset day(s)": "Extract day(s) must be greater than offset day(s)", "Extract premium from X days prior to due date": "Extract premium from X days prior to due date", "Extraction Method": "Extraction Method", "Factor": "Factor", "Factor Code": "Factor Code", "Factor Definition": "Factor Definition", "Factors": "Factors", "Fee Limit": "<PERSON><PERSON>it", "Fee Type": "Fee Type", "Field Value": "Field Value", "File": "File", "File Declaration": "File Declaration", "File Management": "File Management", "File Name": "File Name", "File Type": "File Type", "FILE TYPE": "FILE TYPE", "file upload failed": "file upload failed", "file uploaded successfully": "file uploaded successfully", "File Version": "File Version", "Filter": "Filter", "Financial Anniversary Date": "Financial Anniversary Date", "First": "First", "First Date": "First Date", "first.": "first.", "Fixed Age": "Fixed Age", "Fixed Amount": "Fixed Amount", "Fixed Amount or Fixed Period": "Fixed Amount or Fixed Period", "Fixed due date rule：Fixed due date or period end date": "Fixed due date rule：Fixed due date or period end date", "Fixed Due Date Value": "Fixed Due Date Value", "Fixed Exchange Rate": "Fixed Exchange Rate", "Fixed Time": "Fixed Time", "Fixed Value": "Fixed Value", "Fixed Value is required": "Fixed Value is required", "Flexible Premium Allocation": "Flexible Premium Allocation", "Flexible Premium and SA Agreement": "Flexible Premium and SA Agreement", "Flexible Premium Relationship": "Flexible Premium Relationship", "Flexible Premium Type 1": "Flexible Premium Type 1", "Flexible Premium Type 2": "Flexible Premium Type 2", "Floating Exchange Rate": "Floating Exchange Rate", "Follow the same as Planned Premium": "Follow the same as Planned Premium", "For Fixed Day of Month, please input number from 1 and 28": "For Fixed Day of Month, please input number from 1 and 28", "For formula(s) or ratetable(s) created inside one specific product, the editing function is not supported.": "For formula(s) or ratetable(s) created inside one specific {{product,product,lowerCase}}, the editing function is not supported.", "For formulas that use the list function, Batch Test is not supported yet.": "For formulas that use the list function, Batch Test is not supported yet.", "For liabilities without combination relationship configured, it will be settled as \"Attachable\" by default. If you configure liability combination relationship as \"Compulsory\", it means liability 2 depends on liability 1; And if you configure as \"Non-attachable\", it means liability 1 and liability 2 can not be sold in combination.": "For liabilities without combination relationship configured, it will be settled as \"Attachable\" by default. If you configure liability combination relationship as \"Compulsory\", it means liability 2 depends on liability 1; And if you configure as \"Non-attachable\", it means liability 1 and liability 2 can not be sold in combination.", "For New-Biz": "For New-Biz", "For policy annual illustration": "For policy annual illustration", "For POS": "For POS", "For POS triggered illustration": "For POS triggered illustration", "For step {{index}}, nested calculation components within the ifElse component are not allowed. Please create a separate step before ifElse for calculations.": "For step {{index}}, nested calculation components within the ifElse component are not allowed. Please create a separate step before ifElse for calculations.", "For Usage based insurance, accrued premium is calculated according to actual usage during certain period. Usage Premium Start date represents the first day of usage period. And the end date will be calculated based on Usage Premium Start Date and payment frequency.": "For Usage based insurance, accrued premium is calculated according to actual usage during certain period. Usage Premium Start date represents the first day of usage period. And the end date will be calculated based on Usage Premium Start Date and payment frequency.", "Format": "Format", "formula": "Formula", "Formula": "Formula", "Formula Category": "Formula Category", "Formula Code": "Formula Code", "Formula Code could not start with \"G_\".": "Formula Code could not start with \"G_\".", "Formula Code could not start with numbers.": "Formula Code could not start with numbers.", "Formula Description": "Formula Description", "Formula Detail": "Formula Detail", "Formula in Specific Scenarios": "Formula in Specific Scenarios", "Formula Level": "Formula Level", "Formula Management": "Formula Management", "Formula Name": "Formula Name", "Formula Sub Category": "Formula Sub Category", "formulas": "formulas", "Free Switch Times": "Free Switch Times", "Free Withdrawal Time(s)": "<PERSON> Withdrawal Time(s)", "Freelook Period": "Freelook Period", "Freelook Period Base Date": "Freelook Period Base Date", "Freelook Period Expiration Rule": "Freelook Period Expiration Rule", "Freelook Refund Account Rule": "Freelook Refund Account Rule", "Freelook Reverse Rules": "Freelook Reverse Rules", "Frequency": "Frequency", "Full Screen": "Full Screen", "Fund": "Fund", "Fund Appointment Rate for Rebalance": "Fund Appointment Rate for Rebalance", "Fund Calculation Basis Details": "Fund Calculation Basis Details", "Fund Calculation Basis of Product": "Fund Calculation Basis of {{product,product}}", "Fund Code": "Fund Code", "Fund Deduction Method": "Fund Deduction Method", "Fund Deduction Sequence": "Fund Deduction Sequence", "Fund Info": "Fund Info", "Fund Trad Type": "Fund Trad Type", "fund_name": "Fund Name", "gender": "Gender", "Gender": "Gender", "Generate": "Generate", "Generate Cash Value Saving Account": "Generate Cash Value Saving Account", "Generate new policy number for renewal": "Generate new policy number for renewal", "Generate Result": "Generate Result", "Generate Test Excel": "Generate Test Excel", "Generate Vehicle Databases Fields": "Generate Vehicle Databases Fields", "goods": "goods", "Goods / Package / Product not found during export process. Please check if the data exists and is correctly configured in the system.": "Goods / Package / Product not found during export process. Please check if the data exists and is correctly configured in the system.", "Goods A": "Goods A", "Goods allowed to be sold together": "Goods allowed to be sold together", "Goods B": "Goods B", "Goods Code": "Goods Code", "Goods Code/Goods Name": "Goods Code/Goods Name", "GOODS CONFIGURATION": "GOODS CONFIGURATION", "Goods Name": "Goods Name", "GOODS RELATED CONFIGURATION": "GOODS RELATED CONFIGURATION", "Goods V2": "Goods V2", "Got it": "Got it", "Grace Period": "<PERSON> Period", "Grace Period Rule": "Grace Period Rule", "Green Card Fee": "Green Card Fee", "Green Card(Non-EU)": "Green Card(Non-EU)", "Guarantee Period": "Guarantee Period", "Guarantee Period Value Type": "Guarantee Period Value Type", "Handling of Charge Deduction within or out of NLG": "Handling of Charge Deduction within or out of NLG", "Handling of Premium Unpaid": "Handling of Premium Unpaid", "Handling of TIV Insufficient": "Handling of TIV Insufficient", "Has Cash Value": "Has Cash Value", "Have SA": "Have SA", "Health Claim Info": "Health Claim Info", "HH:mm": "HH:mm", "Holiday": "Holiday", "Hospital/Clinic": "Hospital/Clinic", "Hospitalization Allowance Amount": "Hospitalization Allowance Amount", "Hospitalization Allowance Value Type": "Hospitalization Allowance Value Type", "Hospitalization start date be within x days/months/years after Incident date. E.g: if hospitalization start date should be within 1 year after Incident date, configure as 1, Year(s)).": "Hospitalization start date be within x days/months/years after Incident date. E.g: if hospitalization start date should be within 1 year after Incident date, configure as 1, Year(s)).", "Hour": "Hour", "ID": "ID", "if calculation sequence is needed，please make sure all discount have sequence definition.": "if calculation sequence is needed，please make sure all discount have sequence definition.", "if calculation sequence is needed，please make sure all loading have sequence definition.": "if calculation sequence is needed，please make sure all loading have sequence definition.", "If cash value have surplus after ETI": "If cash value have surplus after ETI", "If cash value have surplus after paid up": "If cash value have surplus after paid up", "If choose 'Refund', premium collection will be reserved in suspense and refunded later. If choose 'Fill in the ILP premium plan or installment premium', system will settle the installment premium or partially fill the ILP premium plan.": "If choose 'Refund', premium collection will be reserved in suspense and refunded later. If choose 'Fill in the ILP premium plan or installment premium', system will settle the installment premium or partially fill the ILP premium plan.", "If choose No, RTU will share same payment frequency with Planned Premium. If choose Yes, RTU is allowed to use different payment frequency with Planned Premium, which could be defined below.": "If choose No, RTU will share same payment frequency with Planned Premium. If choose Yes, RTU is allowed to use different payment frequency with Planned Premium, which could be defined below.", "if condition": "if condition", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)", "if illustration sequence is needed，please make sure all illustration items have sequence definition.": "if illustration sequence is needed，please make sure all illustration items have sequence definition.", "If liability could not be found here, please check whether liability has configured claim type in liability management module": "If liability could not be found here, please check whether liability has configured claim type in liability management module", "If no parameter could be found, you could create middle factor by using the format Middle_XXX.": "If no parameter could be found, you could create middle factor by using the format Middle_XXX.", "If not defined on the product, the rules defined on the package will be used (allowing for unified definition directly when packaging the package)": "If not defined on the {{product,product,lowerCase}}, the rules defined on the {{package,package,lowerCase}} will be used (allowing for unified definition directly when packaging the {{package,package,lowerCase}})", "If someone planning to drive in non-EU countries that recognize the Green Card, he/she must request the card from auto insurance provider before traveling. This function covers the Green Card logic for purchasing auto insurance policies in non-EU countries, including fees, issuance, endorsements, refunds, printing, etc.": "If someone planning to drive in non-EU countries that recognize the Green Card, he/she must request the card from auto insurance provider before traveling. This function covers the Green Card logic for purchasing auto insurance policies in non-EU countries, including fees, issuance, endorsements, refunds, printing, etc.", "If surgery classification needed, please configure 'Surgery Level' in advance!": "If surgery classification needed, please configure 'Surgery Level' in advance!", "If the customer is responsible for 10%, the value is set to 10/.": "If the customer is responsible for 10%, the value is set to 10/.", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)", "If the premium is insufficient to pay any outstanding billing or ILP premium plan, then": "If the premium is insufficient to pay any outstanding billing or ILP premium plan, then", "If this product has waiver liability, all liabilities must be mandatory.": "If this {{product,product,lowerCase}} has waiver liability, all liabilities must be mandatory.", "If value of the factors are different across period.": "If value of the factors are different across period.", "If value of the factors are same across period.": "If value of the factors are same across period.", "If you select yes，system support to add insurable interest under liability.": "If you select yes，system support to add insurable interest under liability.", "Illustration Item": "Illustration Item", "Illustration Item Code": "Illustration Item Code", "Illustration Item List": "Illustration Item List", "Illustration Item Value cannot starts with 'G_'": "Illustration Item Value cannot starts with 'G_'", "Illustration Item(PSI)": "Illustration Item(PSI)", "Illustration Level": "Illustration Level", "Illustration Period": "Illustration Period", "Illustration Period Rule": "Illustration Period Rule", "Illustration Scenario": "Illustration Scenario", "Illustration Sequence": "Illustration Sequence", "ILP Bonus": "ILP Bonus", "ILP Charge": "ILP Charge", "ILP Fund Calculation Basis": "ILP Fund Calculation Basis", "ILP Lapse": "ILP Lapse", "ILP Premium Holiday": "ILP Premium Holiday", "ILP Premium Limit": "ILP Premium Limit", "ILP TIV Account Type": "ILP TIV Account Type", "ILPC + 3 digits": "ILPC + 3 digits", "Import": "Import", "Import / Export": "Import / Export", "Import All": "Import All", "Import Operation Fail": "Import Operation Fail", "Import Successful!": "Import Successful!", "In Japan's auto insurance system, Non-Fleet Grade and Accident Index are two key factors that affect policy rating and premium adjustments. This function is used to define the calculation formulas for Non-Fleet Grade and Accident Index.": "In Japan's auto insurance system, Non-Fleet Grade and Accident Index are two key factors that affect policy rating and premium adjustments. This function is used to define the calculation formulas for Non-Fleet Grade and Accident Index.", "In Progress": "In Progress", "In use:": "In use:", "Inactive": "Inactive", "Incident Date": "Incident Date", "Incident Reason": "Incident Reason", "Including Tax": "Including Tax", "Increase Interest By(%)": "Increase Interest By(%)", "Individual Test": "Individual Test", "Inforce Together": "Inforce Together", "Initial Premium Period": "Initial Premium Period", "input is illegal": "input is illegal", "Insert fields": "Insert fields", "Installment Calculation Method": "Installment Calculation Method", "Installment Detail": "Installment Detail", "Installment Info": "Installment Info", "Installment Payment Plan for Claims": "Installment Payment Plan for Claims", "Installment Payment Type": "Installment Payment Type", "Installment Premium Calculation Basis": "Installment Premium Calculation Basis", "Installment Premium Calculation Method": "Installment Premium Calculation Method", "Installment Standard Premium": "Installment Standard Premium", "Installment Standard Premium (Inc Tax)": "Installment Standard Premium (Inc Tax)", "Installment(s) Premium": "Installment(s) Premium", "Insurable Interest ID": "Insurable Interest ID", "Insurable Interest Info": "Insurable Interest Info", "Insurable Interest Name": "Insurable Interest Name", "Insurable Interest/Liability Rate Setting": "Insurable Interest/Liability Rate Setting", "Insured Age Comparison": "Insured Age Comparison", "Insured Elements Relationship Matrix": "Insured Elements Relationship Matrix", "Insured Info": "Insured Info", "Insured Occupation Class": "Insured Occupation Class", "Insured Occupation Risk Category": "Insured Occupation Risk Category", "Insured's Entry Max Age": "Insured's Entry Max Age", "Insured's Entry Min Age": "Insured's Entry Min Age", "Insured‘s Entry Max Age": "Insured‘s Entry Max Age", "Insured’s Age Range(NB)": "Insured’s Age Range(NB)", "Insured’s Age Range(Renewal)": "Insured’s Age Range(Renewal)", "Insured’s Entry Min Age": "Insured’s Entry Min Age", "Interest": "Interest", "Internal: Compliance decision can only be provided by compliance user.": "Internal: Compliance decision can only be provided by compliance user.", "Investment Agreement": "Investment Agreement", "Investment Delay": "Investment Delay", "Investment Delay Option": "Investment Delay Option", "Is switch charge a fixed amount?": "Is switch charge a fixed amount?", "is the default condition.": "is the default condition.", "Is there a need to make a lump sum payment before executing the installment plan?": "Is there a need to make a lump sum payment before executing the installment plan?", "Issue with Green Card (Non-EU)": "Issue with Green Card (Non-EU)", "It is also supported entering the exchange rate unit. You can open \"Component Instruction\" for more details.": "It is also supported entering the exchange rate unit. You can open \"Component Instruction\" for more details.", "It will take a certain time for batch test. A platform message will be sent to you once the batch test is completed. And you could download test result through the message.": "It will take a certain time for batch test. A platform message will be sent to you once the batch test is completed. And you could download test result through the message.", "itemExtend1": "itemExtend1", "itemExtend2": "itemExtend2", "itemExtend3": "itemExtend3", "itemExtend4": "itemExtend4", "itemExtend5": "itemExtend5", "itemExtend6": "itemExtend6", "Japan Motor": "Japan Motor", "LANGUAGE": "LANGUAGE", "Lapse Date": "Lapse Date", "Lapse/Termination": "Lapse/Termination", "Last Modifier": "Last Modifier", "LATEST OF": "LATEST OF", "Less": "Less", "Liability": "Liability", "Liability <{{liabilityId}}><{{liabilityName}}> has been created successfully.": "Liability <{{liabilityId}}><{{liabilityName}}> has been created successfully.", "Liability <{{liabilityId}}><{{liabilityName}}> has been edited successfully": "Liability <{{liabilityId}}><{{liabilityName}}> has been edited successfully", "Liability Agreement": "Liability Agreement", "Liability Category": "Liability Category", "Liability Code": "Liability Code", "Liability Coverage Period": "Liability Coverage Period", "Liability Coverage Period defaults to the same as Product Coverage Period. If it differs from Product Coverage Period, you can define an independent coverage period for the liability here.": "Liability Coverage Period defaults to the same as {{product,product}} Coverage Period. If it differs from {{product,product}} Coverage Period, you can define an independent coverage period for the liability here.", "Liability Description": "Liability Description", "Liability Handling": "Liability Handling", "Liability Handling After ETI": "Liability Handling After ETI", "Liability Handling After Paid Up": "Liability Handling After Paid Up", "Liability ID": "Liability ID", "Liability Info": "Liability Info", "Liability information and POS items can not be configurated at the virtual main benefit. Do you want to delete them?": "Liability information and POS items can not be configurated at the virtual main benefit. Do you want to delete them?", "Liability is required": "Liability is required", "Liability Level": "Liability Level", "Liability Name": "Liability Name", "Liability Name(Liability1)": "Liability Name(Liability1)", "Liability Name(Liability2)": "Liability Name(Liability2)", "Liability or Interest is required": "Liability or Interest is required", "Liability Relationship Matrix": "Liability Relationship Matrix", "Liability Remark": "Liability Remark", "Liability SA": "Liability SA", "Liability Tag": "Liability Tag", "Liability Termination": "Liability Termination", "Liability Termination by Claim Setting": "Liability Termination by <PERSON><PERSON><PERSON>", "Lien Indicator": "Lien Indicator", "Life Event": "Life Event", "Linked Formulas": "Linked Formulas", "Linked Goods": "Linked Goods", "Linked Package": "Linked {{package,package}}", "Linked Packages": "Linked {{package,package}}s", "Linked Product / Package / Goods": "Linked {{product,product}} / {{package,package}} / Goods", "Linked Products": "Linked {{product,product}}s", "Linked Ratetables": "Linked Ratetables", "Linked Rules": "Linked Rules", "Load More": "Load More", "Loading Method": "Loading Method", "Loading Sequence": "Loading Sequence", "Loading Type": "Loading Type", "Loan Amount": "<PERSON><PERSON>", "Loan Term Period": "Loan Term Period", "Loan Term Type": "Loan Term Type", "Low Risk Fund": "Low Risk Fund", "Lump Sum Amount": "<PERSON><PERSON> Amount", "Main / Rider": "Main / Rider", "Main Benefit (Virtual)": "Main Benefit (Virtual)", "Main Condition Type": "Main Condition Type", "Main or Rider": "Main or Rider", "Main Product Code": "Main {{product,product}} Code", "Main Product Name": "Main {{product,product}} Name", "Main Relationship with Rider": "Main Relationship with Rider", "Make sure at least one of the diagnosis tag has been configured.": "Make sure at least one of the diagnosis tag has been configured.", "Make sure at least one of the disabilities has been configured.": "Make sure at least one of the disabilities has been configured.", "Manual Adjustment": "Manual Adjustment", "Marketing Enumerated Values": "Marketing Enumerated Values", "Matrix Table": "Matrix Table", "Matrix Table Code": "Matrix Table Code", "Maturity Agreement": "Maturity Agreement", "Maturity Benefit": "Maturity Benefit", "Maturity Benefit Leading Days": "Maturity Benefit Leading Days", "Maturity Reminder Date Compare to Policy Expiry Date": "Maturity Reminder Date Compare to Policy Expiry Date", "Maturity Reminder Rule": "Maturity Reminder Rule", "Maturity Reminder Rule (Legacy)": "Maturity Reminder Rule (Legacy)", "Maturity Reminder Rule cannot be repeated.": "Maturity Reminder Rule cannot be repeated.", "Max": "Max", "Max Claim Limit": "<PERSON>", "Max Claim Percentage (%)": "Max Claim Percentage (%)", "Max Claim Times": "<PERSON>", "Max Down-Regulation Ratio": "Max Down-Regulation Ratio", "Max Guaranteed Renewable": "Max Gua<PERSON>eed Renewable", "Max SA Multiplier": "Max SA Multiplier", "Max SA Multiplier Type": "Max SA Multiplier Type", "Max Up-Regulation Ratio": "Max Up-Regulation Ratio", "Max value cannot be less than min value": "Max value cannot be less than min value", "Maximum Age for Whole Life": "Maximum Age for Whole Life", "Maximum Loanable Amount Formula": "Maximum Loanable Amount Formula", "Maximum Loanable Percentage": "Maximum Loanable Percentage", "Maximum Premium Holiday": "Maximum Premium Holiday", "Maximum Premium Holiday Period Type": "Maximum Premium Holiday Period Type", "Medical Bill Configuration": "Medical Bill Configuration", "Medical Bill Item": "Medical Bill <PERSON>em", "Medical Bill Type": "Medical Bill Type", "Medical Bill Type and Bill Item": "Medical Bill Type and Bill <PERSON>", "Medical Billing Type": "Medical Billing Type", "Medical Expense": "Medical Expense", "Medical Fee Rules": "Medical Fee Rules", "MENU": "MENU", "Middle factor format is not correct. Please follow format: Middle_XXX.": "Middle factor format is not correct. Please follow format: Middle_XXX.", "Min": "Min", "Min / Max Times": "Min / Max Times", "Min SA Multiplier": "Min SA Multiplier", "Min SA Multiplier Type": "Min SA Multiplier Type", "Min Value of Policy": "Min Value of Policy", "Min Withdrawal Amount": "<PERSON> Amount", "Minimum Investment Period": "Minimum Investment Period", "Minimum Investment Period Applied to": "Minimum Investment Period Applied to", "Minimum Investment Period Type": "Minimum Investment Period Type", "Minimum Investment Period Value": "Minimum Investment Period Value", "Minimum Net Premium (Standard Premium+Extra Premium-Premium Discount) for coverage period if Net premium calculated from formula is below minimum net premium.If regular payment, minimum premium cap needs to be multiplied by modal factor.": "Minimum Net Premium (Standard Premium+Extra Premium-Premium Discount) for coverage period if Net premium calculated from formula is below minimum net premium.If regular payment, minimum premium cap needs to be multiplied by modal factor.", "Minimum Premium Cap": "Minimum Premium Cap", "Minute(s)": "Minute(s)", "Missing parameter": "Missing parameter", "Missing required parameter": "Missing required parameter", "Modal Factor": "Modal Factor", "Modifier": "Modifier", "Modifying the diagnosis tag info will auto clear the Diagnosis configuration. Please confirm whether to continue?": "Modifying the diagnosis tag info will auto clear the Diagnosis configuration. Please confirm whether to continue?", "Modifying the Surgery level info will auto clear the surgery configuration. Please confirm whether to continue?": "Modifying the Surgery level info will auto clear the surgery configuration. Please confirm whether to continue?", "Modifying the Surgery tag1 info will auto clear the surgery configuration. Please confirm whether to continue?": "Modifying the Surgery tag1 info will auto clear the surgery configuration. Please confirm whether to continue?", "Modifying the Surgery tag2 info will auto clear the surgery configuration. Please confirm whether to continue?": "Modifying the Surgery tag2 info will auto clear the surgery configuration. Please confirm whether to continue?", "Modifying the tag definition info will auto clear the surgery configuration. Please confirm whether to continue?": "Modifying the tag definition info will auto clear the surgery configuration. Please confirm whether to continue?", "Month": "Month", "Month Value": "Month Value", "Month Value for Advance Filling": "Month Value for Advance Filling", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)", "Months": "Months", "Multi Language": "Multi Language", "Multi-language": "Multi-language", "My Creations": "My Creations", "NA": "NA", "Name": "Name", "Name Connection Method": "Name Connection Method", "Name Format": "Name Format", "Name Sequence Definition": "Name Sequence Definition", "NCD Code": "NCD Code", "NCD Formula Code": "NCD Formula Code", "NCD Management": "NCD Management", "Need To Attach Advanced Criteria": "Need To Attach Advanced Criteria", "Net Premium Adjustment": "Net Premium Adjustment", "New Category": "New Category", "New Claim Stack Structure": "New Claim Stack Structure", "New Product after ETI": "New {{product,product}} after ETI", "New Product after Paid Up": "New {{product,product}} after Paid Up", "New Version": "New Version", "Next": "Next", "NLG Condition": "NLG Condition", "NLG Period": "NLG Period", "NLG Period Factor": "NLG Period Factor", "NLG Period Type": "NLG Period Type", "NLG Period Type is required": "NLG Period Type is required", "NLG Period Value": "NLG Period Value", "NLG Period Value is required": "NLG Period Value is required", "no": "No", "No Claim Bonus": "No Claim Bonus", "No Claim Discount": "No Claim Discount", "No component is selected. Please modify.": "No component is selected. Please modify.", "No Content": "No Content", "No Data": "No Data", "No impact on original formula when editing formula copies": "No impact on original formula when editing formula copies", "No Lapse Guarantee": "No Lapse Guarantee", "No match result.": "No match result.", "No Valid Data": "No Valid Data", "No.": "No.", "No. Claim Times": "No. Claim Times", "No. of Completed Policy Years for Bonus Vesting on Claim": "No. of Completed Policy Years for Bonus Vesting on Claim", "No. of Completed Policy Years for CB Becoming Payable": "No. of Completed Policy Years for CB Becoming Payable", "No. of Completed Policy Years for Surrender Bonus Allowed": "No. of Completed Policy Years for Surrender Bonus Allowed", "No.of Installment": "No.of Installment", "Non-Fleet Grade and Accident Index of Japan Motor": "Non-Fleet Grade and Accident Index of Japan Motor", "Non-Fleet Grade Formula": "Non-Fleet Grade Formula", "Nonforfeiture Option Type": "Nonforfeiture Option Type", "Normal Policy Date Check Rule Compare to Master Policy": "Normal Policy Date Check Rule Compare to Master Policy", "Normal Policy Expiry Date Rule": "Normal Policy Expiry Date Rule", "Not Applicable": "Not Applicable", "Not Cascade": "Not Cascade", "Note: The relationship defined here is only applicable when the package is configured as single-main-benefit.If the package is marked as supporting multiple main benefits, this relationship will be ignored and have no effect.": "Note: The relationship defined here is only applicable when the {{package,package,lowerCase}} is configured as single-main-benefit.If the {{package,package,lowerCase}} is marked as supporting multiple main benefits, this relationship will be ignored and have no effect.", "Notice": "Notice", "Notice Date Compare with Due Date": "Notice Date Compare with Due Date", "Notice Rule": "Notice Rule", "Notice Rule cannot be repeated": "Notice Rule cannot be repeated", "Notification": "Notification", "number": "No.", "Number": "Number", "Number Item": "Number Item", "Number Items": "Number Items", "Number of Files/URLs": "Number of Files/URLs", "Number1": "Number1", "Number2": "Number2", "Object": "Object", "Object Category": "Object Category", "Object Category & Object": "Object Category & Object", "Object Category & Type": "Object Category & Type", "Object Category Code": "Object Category Code", "Object Category Name": "Object Category Name", "Object Component": "Object Component", "Object Element-Auto-Vehicle": "Object Element-Auto-Vehicle", "Object Enumerated Values": "Object Enumerated Values", "Object Info": "Object Info", "Object Sub Category": "Object Sub Category", "Object.etc. Driver": "Object.etc. Driver", "Obtain Template": "<PERSON><PERSON><PERSON>", "Occupation Risk Category": "Occupation Risk Category", "Occurrence Type（Accommodation)": "Occurrence Type（Accommodation)", "Occurrence Type（Transportation)": "Occurrence Type（Transportation)", "Occurrence Type（Transportation）": "Occurrence Type（Transportation）", "Offset Date": "Offset Date", "Offset from X Days Compare to Expired Date": "Offset from X Days Compare to Expired Date", "Offset from X days prior to due date": "Offset from X days prior to due date", "Once allowed, the client don't need to pay full amount for total overdue premium bills during reinstatement. Only a minimum premium payment is required to pay all the charges and keep the policy effective. The POS formula needs to be defined under this condition.": "Once allowed, the client don't need to pay full amount for total overdue premium bills during reinstatement. Only a minimum premium payment is required to pay all the charges and keep the policy effective. The POS formula needs to be defined under this condition.", "Once opened, system allow policyholder to rebalance their fund account (TIV) based on specific ratio regularly.": "Once opened, system allow policyholder to rebalance their fund account (TIV) based on specific ratio regularly.", "Once opened, system allow user to trigger adanced DCA arrangement. Premium will put into a pre-defined low risk fund first, and then switch out and buy target fund on a regular basis.": "Once opened, system allow user to trigger adanced DCA arrangement. Premium will put into a pre-defined low risk fund first, and then switch out and buy target fund on a regular basis.", "Only components with the same stack type can be in one template. Different stack types are detected in your submission. Please check the configuration.": "Only components with the same stack type can be in one template. Different stack types are detected in your submission. Please check the configuration.", "Only display formulas that are directly defined and associated under Package Management (e.g., Tax Setting, Premium Adjustment...)": "Only display formulas that are directly defined and associated under {{package,package}} Management (e.g., Tax Setting, Premium Adjustment...)", "Only for Commission & Service Fee formula, it is supported to query linked Goods": "Only for Commission & Service Fee formula, it is supported to query linked Goods", "Only for Commission & Service Fee formula, it is supported to query linked Goods.": "Only for Commission & Service Fee formula, it is supported to query linked Goods.", "Only for Levy and Stamp duty formula, it is supported to query linked package": "Only for Levy and Stamp duty formula, it is supported to query linked {{package,package,lowerCase}}", "Only one formula allowed for each fund, no duplicated allowed.": "Only one formula allowed for each fund, no duplicated allowed.", "only one formula type either rate or premium could be configured for one product.": "only one formula type either rate or premium could be configured for one {{product,product,lowerCase}}.", "Only one modal factor allowed for each payment frequency, no duplicate allowed. Please check.": "Only one modal factor allowed for each payment frequency, no duplicate allowed. Please check.", "Only one record is allowed for each calculation method. No duplicate allowed.": "Only one record is allowed for each calculation method. No duplicate allowed.", "Only perform rebalancing when the variance exceeds": "Only perform rebalancing when the variance exceeds", "operation failure": "operation failure", "Optional": "Optional", "Optional / Required": "Optional / Required", "Order": "Order", "Original Currency": "Original Currency", "Out of No Lapse Guarantee": "Out of No Lapse Guarantee", "Overdue Auto Deduction": "Overdue Auto Deduction", "Overdue Handling": "Overdue Handling", "Overdue Status": "Overdue Status", "Package": "{{package,package}}", "Package Code": "{{package,package}} Code", "PACKAGE CONFIGURATION": "PACKAGE CONFIGURATION", "Package Name": "{{package,package}} Name", "PACKAGE RELATED CONFIGURATION": "PACKAGE RELATED CONFIGURATION", "Package V2": "{{package,package}} V2", "packages": "{{package,package,lowerCase}}s", "Paid Up": "Paid Up", "Paid Up SA Formula": "Paid Up SA Formula", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.", "Parameter or result code have been changed. The uploaded ratetable will be cleared. Please confirm.": "Parameter or result code have been changed. The uploaded ratetable will be cleared. Please confirm.", "Parameters relevant to multiple object can not be used directly in steps without list function.": "Parameters relevant to multiple object can not be used directly in steps without list function.", "Parent Field": "Parent Field", "Parent Field Name": "Parent Field Name", "Partial": "Partial", "Partial withdrawal allowed": "Partial withdrawal allowed", "Participating Agreement": "Participating Agreement", "Payment & Collection Method": "Payment & Collection Method", "Payment Defer Period": "Payment Defer Period", "Payment Defer Period Type": "Payment Defer Period Type", "Payment Frequency": "Payment Frequency", "Payment Frequency & Modal Factor": "Payment Frequency & Modal Factor", "Payment Option": "Payment Option", "Payment Period": "Payment Period", "Payment Period Type": "Payment Period Type", "Payment Period Value Type": "Payment Period Value Type", "Payment/Collection Method & Account": "Payment/Collection Method & Account", "Pealse select Formula Code": "Pealse select Formula Code", "Pending Case Enumerated Values": "Pending Case Enumerated Values", "Percentage": "Percentage", "Period": "Period", "Period Limit Matrix": "Period Limit Matrix", "Period Type": "Period Type", "Period Value": "Period Value", "Permium Handling Method Within Premium Holiday": "Permium Handling Method Within Premium Holiday", "Pin": "<PERSON>n", "Place of Incurred(Allowance)": "Place of Incurred(Allowance)", "Place of Incurred(Medical Bill)": "Place of Incurred(Medical Bill)", "Plan Info": "Plan Info", "Planned Premium": "Planned Premium", "Planned Premium by Layer": "Planned Premium by Layer", "Planned Premium Layer Info": "Planned Premium Layer Info", "Please": "Please", "Please add at least one formula step": "Please add at least one formula step", "Please Add Premium Notice Rule": "Please Add Premium Notice Rule", "Please add stack correlation": "Please add stack correlation", "Please add Terminal Bonus formula.": "Please add Terminal Bonus formula.", "Please at least select one Option": "Please at least select one Option", "Please check the required name set first": "Please check the required name set first", "Please check whether you need to modify the following Formula:": "Please check whether you need to modify the following Formula:", "Please choose at least one type of name connection method & name.": "Please choose at least one type of name connection method & name.", "Please complete coverage period agreement": "Please complete coverage period agreement", "Please config Enumeration Value": "Please config Enumeration Value", "Please Configure a Formula for Vehicle Market Value": "Please Configure a Formula for Vehicle Market Value", "Please configure at least one of the agreement.": "Please configure at least one of the agreement.", "Please configure at least one record.": "Please configure at least one record.", "Please configure before submit.": "Please configure before submit.", "Please configure calculation logic for risk sub-category.": "Please configure calculation logic for risk sub-category.", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration -> Premium Notice Reminder\".": "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration -> Premium Notice Reminder\".", "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration\".": "Please configure the rule in \"Configuration Center - New Business & Renewal Configuration\".", "Please configure this agreement, otherwise insured will not be default deactivated after fully claimed of the selected liability.": "Please configure this agreement, otherwise insured will not be default deactivated after fully claimed of the selected liability.", "Please configure this agreement, otherwise liability will not be default terminated after claim.": "Please configure this agreement, otherwise liability will not be default terminated after claim.", "Please configure transaction type.": "Please configure transaction type.", "Please confirm changing the default option from": "Please confirm changing the default option from", "Please confirm setting the default option to": "Please confirm setting the default option to", "Please confirm to wipe out all uploaded benefit details and all versions.": "Please confirm to wipe out all uploaded benefit details and all versions.", "Please continue to finish Premium holiday related configuration in the \"ILP Premium Holiday\" Section.": "Please continue to finish Premium holiday related configuration in the \"ILP Premium Holiday\" Section.", "Please copy your File URL first.": "Please copy your File URL first.", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.", "Please define the allowed investment strategy.": "Please define the allowed investment strategy.", "Please do not enter a space in result code": "Please do not enter a space in result code", "Please ensure at least one record is entered in Factor Definition or Ratetable Replacement.": "Please ensure at least one record is entered in Factor Definition or Ratetable Replacement.", "Please fill in the mandatory items before submitting.": "Please fill in the mandatory items before submitting.", "Please first define the Object Category and Object under Configuration Center → Tenant Data Configuration → Basic Rule → Object Category & Object.": "Please first define the Object Category and Object under Configuration Center → Tenant Data Configuration → Basic Rule → Object Category & Object.", "Please input": "Please input", "Please input at least one field.": "Please input at least one field.", "Please input at least three characters.": "Please input at least three characters.", "Please input condition": "Please input condition", "Please input end number": "Please input end number", "Please input expression": "Please input expression", "Please input in this format: 'HH:mm'. e.g. 12:00": "Please input in this format: 'HH:mm'. e.g. 12:00", "Please input liability handling.": "Please input liability handling.", "Please input number": "Please input number", "Please input or paste the URL here": "Please input or paste the URL here", "Please input positive value": "Please input positive value", "Please input result": "Please input result", "Please input the range in [{{minimum}}, {{maximum}}]": "Please input the range in [{{minimum}}, {{maximum}}]", "Please input variable": "Please input variable", "Please input variable and start number": "Please input variable and start number", "Please input Vehicle DataBase Name": "Please input Vehicle DataBase Name", "Please make sure that both medical bill type and medical bill item have been configured.": "Please make sure that both medical bill type and medical bill item have been configured.", "Please make sure you define how to calculate this middle factor when using in formula configuration.": "Please make sure you define how to calculate this middle factor when using in formula configuration.", "Please note: After premium holiday, cash rider will be terminated from next premium due date (of first unpaid bill), while unit deducting rider (if any) will keep effecitve.": "Please note: After premium holiday, cash rider will be terminated from next premium due date (of first unpaid bill), while unit deducting rider (if any) will keep effecitve.", "Please note: Current detail information of Recurring Single Top Up will be overwritten by Premium Frequency & Installment Detail of Planned Premium.": "Please note: Current detail information of Recurring Single Top Up will be overwritten by Premium Frequency & Installment Detail of Planned Premium.", "Please note: There is no refund after termination and policy is not allowed to be reinstated.If there are any active riders, they will also be terminated.": "Please note: There is no refund after termination and policy is not allowed to be reinstated.If there are any active riders, they will also be terminated.", "please retain at least two branches": "please retain at least two branches", "Please save the changes to cancellation reason first.": "Please save the changes to cancellation reason first.", "Please save the changes to cancellation type first.": "Please save the changes to cancellation type first.", "please select": "please select", "Please select": "Please select", "Please select at least one advance criteria for ANFO setting.": "Please select at least one advance criteria for ANFO setting.", "Please select at least one related liability.": "Please select at least one related liability.", "Please select at least two fields.": "Please select at least two fields.", "Please select effective date": "Please select effective date", "Please select Formula Code": "Please select Formula Code", "Please select formula existed in steps": "Please select formula existed in steps", "Please select language": "Please select language", "Please select one record at least!": "Please select one record at least!", "Please select one reocrd": "Please select one reocrd", "Please select parameter": "Please select parameter", "Please select ratetable existed in steps": "Please select ratetable existed in steps", "Please select related Formula for POS": "Please select related Formula for POS", "Please select the sections to export:": "Please select the sections to export:", "Please select the sections to import:": "Please select the sections to import:", "Please select to:": "Please select to:", "Please set the deduction sequence if corresponding premium type is insufficient for charge deduction.": "Please set the deduction sequence if corresponding premium type is insufficient for charge deduction.", "Please submit change before test": "Please submit change before test", "Please tick the additional condition that should be met before enjoy no lapse guarantee": "Please tick the additional condition that should be met before enjoy no lapse guarantee", "please upload excel or csv": "please upload excel or csv", "Please upload the file in XLSX or XLX format!": "Please upload the file in XLSX or XLX format!", "Please* configure at least one set of name rules.": "Please* configure at least one set of name rules.", "Policy Currency": "Policy Currency", "Policy Effective Without Collection (NB)": "Policy Effective Without Collection (NB)", "Policy Effective Without Collection (Renewal)": "Policy Effective Without Collection (Renewal)", "Policy Enumerated Values": "Policy Enumerated Values", "Policy Holder Info": "Policy Holder Info", "Policy Illustration": "Policy Illustration", "Policy Info": "Policy Info", "Policy Loan": "Policy Loan", "Policy Month": "Policy Month", "Policy No.": "Policy No.", "Policy Type": "Policy Type", "Policy will be lapsed after grace period ends.": "Policy will be lapsed after grace period ends.", "Policy will be terminated after grace period ends.": "Policy will be terminated after grace period ends.", "Policyholder Age Comparison": "Policyholder Age Comparison", "Policyholder Elements Relationship Matrix": "Policyholder Elements Relationship Matrix", "Policyholder’s Age Range(NB)": "Policyholder’s Age Range(NB)", "Policyholder’s Age Range(Renewal)": "Policyholder’s Age Range(Renewal)", "PolicyStatusEnum.POLICY_EFFECT": "Effective", "policySuspense（Mock）": "policy<PERSON>us<PERSON>se（<PERSON><PERSON>）", "Portfolio Rebalancing": "Portfolio Rebalancing", "POS": "POS", "POS additional/refund net premium = (product net premium after POS - product net premium before) * unearned period / coverage period": "POS additional/refund net premium = ({{product,product,lowerCase}} net premium after POS - {{product,product,lowerCase}} net premium before) * unearned period / coverage period", "POS Item": "POS Item", "Post-sale Illustration": "Post-sale Illustration", "Post-sale Illustration Trigger Point": "Post-sale Illustration Trigger Point", "Pre-definition": "Pre-definition", "Pre-set Data Setting": "Pre-set Data Setting", "Premium Adjustment": "Premium Adjustment", "Premium Adjustment Level": "Premium Adjustment Level", "Premium Adjustment Type": "Premium Adjustment Type", "Premium Agreement": "Premium Agreement", "Premium Calculation Method": "Premium Calculation Method", "Premium Discount": "Premium Discount", "Premium Discount Type": "Premium Discount Type", "Premium Due Date Rule": "Premium Due Date Rule", "Premium End Date Calculation Rule": "Premium End Date Calculation Rule", "Premium Frequency": "Premium Frequency", "Premium Holiday": "Premium Holiday", "Premium Investment Strategy": "Premium Investment Strategy", "Premium Limit": "Premium Limit", "Premium Limit Type": "Premium Limit Type", "Premium Notice Date Compare with Due Date": "Premium Notice Date Compare with Due Date", "Premium Notice Rule": "Premium Notice Rule", "Premium Notice Rule cannot be repeated": "Premium Notice Rule cannot be repeated", "Premium Notice Rule(Legacy)": "Premium Notice Rule(Legacy)", "Premium Period": "Premium Period", "Premium Period & Installment": "Premium Period & Installment", "Premium Period Type": "Premium Period Type", "Premium Period Value Type": "Premium Period Value Type", "Premium Period(title)": "Premium Period", "Premium Type": "Premium Type", "Premium Type Deduction Method": "Premium Type Deduction Method", "Premium Type Deduction Sequence": "Premium Type Deduction Sequence", "Premium Unpaid Handling": "Premium Unpaid Handling", "Preview Ratetable": "Preview Ratetable", "Previous": "Previous", "Pro Rata Calculation Basis": "Pro Rata Calculation Basis", "Product": "{{product,product}}", "Product & Liability Relationship Matrix": "{{product,product}} & Liability Relationship Matrix", "Product Agreement": "{{product,product}} Agreement", "Product Category": "{{product,product}} Category", "Product Center": "{{product,product}} Center", "Product Center - Product Management": "Product Center - {{product,product}} Management", "Product Class": "{{product,product}} Class", "Product Code": "{{product,product}} Code", "Product Code already exists": "{{product,product}} Code already exists", "PRODUCT CONFIGURATION": "PRODUCT CONFIGURATION", "Product Enumerated Values": "{{product,product}} Enumerated Values", "Product Info": "{{product,product}} Info", "Product Label": "{{product,product}} Label", "Product Label Configuration": "{{product,product}} Label Configuration", "Product Liability": "{{product,product}} Liability", "Product Line": "{{product,product}} Line", "Product Management": "{{product,product}} Management", "Product Name": "{{product,product}} Name", "Product Overdue Status": "{{product,product}} Overdue Status", "PRODUCT RELATED CONFIGURATION": "PRODUCT RELATED CONFIGURATION", "Product SA": "{{product,product}} SA", "Product SA Priority": "{{product,product}} SA Priority", "Product SA Setting": "{{product,product}} SA Setting", "Product Status": "{{product,product}} Status", "Product Template": "{{product,product}} Template", "Product V2": "{{product,product}} V2", "Product Validation": "{{product,product}} Validation", "products": "{{product,product,lowerCase}}s", "Progression Table": "Progression Table", "Property Bill Item": "Property Bill Item", "Property Bill Object": "Property Bill Object", "Property Bill Objects": "Property Bill Objects", "Property Claim Info": "Property Claim Info", "Publish": "Publish", "Publish Successfully": "Publish Successfully", "Published": "Published", "Quick Menu": "Quick Menu", "Range": "Range", "Rate Formula Code": "Rate Formula Code", "Rate Range": "Rate Range", "Rate Source": "Rate Source", "Rate Table": "Rate Table", "Ratetable": "Ratetable", "Ratetable Category": "Ratetable Category", "Ratetable Code": "Ratetable Code", "RateTable Code": "RateTable Code", "Ratetable Code could not start with \"G_\".": "Ratetable Code could not start with \"G_\".", "Ratetable Code should not include \"_ProductCorrelationMatrix、_PeriodLimitMatrix、G_BenefitScheduleMatrix\".": "Ratetable Code should not include \"_ProductCorrelationMatrix、_PeriodLimitMatrix、G_BenefitScheduleMatrix\".", "Ratetable Description": "Ratetable Description", "Ratetable Introduction": "Ratetable Introduction", "Ratetable Management": "Ratetable Management", "Ratetable Name": "Ratetable Name", "Ratetable Replacement": "Ratetable Replacement", "Ratetable Sub Category": "Ratetable Sub Category", "ratetables": "ratetables", "Re-Define Effective Date Rule": "Re-Define Effective Date Rule", "Rebalance Date": "Rebalance Date", "Rebalance Frequency": "Rebalance Frequency", "Recalculate SA or Premium": "Recalculate SA or Premium", "Recalculate SA or Premium Except for Change Reason": "Recalculate SA or Premium Except for Change Reason", "record": "Records", "record(s) has been uploaded successfully.": "record(s) has been uploaded successfully.", "records": "Records", "Recount Result": "Recount Result", "Recurring Single Top Up Detail": "Recurring Single Top Up Detail", "Reduce cash value to zero": "Reduce cash value to zero", "Refund Confirmed Premium from": "Refund Confirmed Premium from", "Refund Formula": "Refund Formula", "Regional Function Management": "Regional Function Management", "Regular Top Up Frequency": "Regular Top Up Frequency", "Regular Top Up Frequency configure at least one": "Regular Top Up Frequency configure at least one", "Regular Top Up payment needs to use separated frequency": "Regular Top Up payment needs to use separated frequency", "Regular withdrawal allowed": "Regular withdrawal allowed", "Reinstatement Period": "Reinstatement Period", "Reinstatement Period Unit": "Reinstatement Period Unit", "Related Stacks": "Related Stacks", "Related Stacks within the Same Order": "Related Stacks within the Same Order", "Related Termination": "Related Termination", "Related to Bone Fracture/Dislocation Item and Bone Fracture/Dislocation Level": "Related to Bone Fracture/Dislocation Item and Bone Fracture/Dislocation Level", "Relation Policy Info": "Relation Policy Info", "Relation Policy Template": "Relation Policy Template", "Relational Type": "Relational Type", "Relationship": "Relationship", "Rename": "<PERSON><PERSON>", "Renewable or Not": "Renewable or Not", "Renewal": "Renewal", "Renewal Agreement": "Renewal Agreement", "Renewal Extraction Period": "Renewal Extraction Period", "Renewal Grace Period": "<PERSON><PERSON>", "Renewal Policy Effective Date Rule": "Renewal Policy Effective Date Rule", "Renewal Policy Effective Without Collection": "Renewal Policy Effective Without Collection", "Renewal Proposal Submit Date": "Renewal Proposal Submit Date", "Renewal Reminder Date Compare to Policy Expiry Date": "Renewal Reminder Date Compare to Policy Expiry Date", "Renewal Reminder Rule": "<PERSON><PERSON> Reminder Rule", "Renewal Reminder Rule cannot be repeated": "Renewal Reminder Rule cannot be repeated", "Renewal Together": "Renewal Together", "Replacement Ratetable": "Replacement Ratetable", "Replacement Ratetable is required": "Replacement Ratetable is required", "Required": "Required", "Required / Optional": "Required / Optional", "Reserve Provision trigger point": "Reserve Provision trigger point", "Reserve Provision Trigger Point": "Reserve Provision Trigger Point", "Reset": "Reset", "Result Code": "Result Code", "Result Details": "Result Details", "Retirement Age (Insured)": "Retirement Age (Insured)", "Retirement Option": "Retirement Option", "Retirement Option Start Date": "Retirement Option Start Date", "Reversionary Bonus": "Reversionary Bonus", "Reversionary Bonus Allowed": "Reversionary Bonus Allowed", "Rider Code": "Rider Code", "Rider Handling After ETI": "Rider Handling After ETI", "Rider Handling After Paid Up": "Rider Handling After Paid Up", "Rider Handling After Triggering Premium Holiday": "Rider Handling After Triggering Premium Holiday", "Rider Name": "Rider Name", "Rider Name(Rider1)": "Rider Name(Rider1)", "Rider Name(Rider2)": "Rider Name(Rider2)", "Rider Relationship Matrix": "Rider Relationship Matrix", "Rider Relationship with Main Product": "Rider Relationship with Main {{product,product}}", "Rider Type": "Rider Type", "Risk Category": "Risk Category", "Risk SA for Risk Category": "Risk SA for Risk Category", "Risk SA for Risk Sub-category": "Risk SA for Risk Sub-category", "Risk Sub-category": "Risk Sub-category", "Room Level": "Room Level", "Round": "Round", "Rounding Digits": "Rounding Digits", "Rounding Type": "Rounding Type", "Rule Category": "Rule Category", "Rule Code": "Rule Code", "Rule Name": "Rule Name", "Rule/Rule Set": "Rule/Rule Set", "Rule/Rule Set Code": "Rule/Rule Set Code", "Rule/Rule Set Name": "Rule/Rule Set Name", "Rule1：Corresponding to effective date or period end date": "Rule1：Corresponding to effective date or period end date", "Rule2：Corresponding to effective date or next date of period end date": "Rule2：Corresponding to effective date or next date of period end date", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date", "rules": "rules", "SA Agreement": "SA Agreement", "SA by Layer": "SA by Layer", "SA Calculation Method": "SA Calculation Method", "SA Decreasing Order": "SA Decreasing Order", "SA Limit": "SA Limit", "SA Limit Type": "SA Limit Type", "SA Multiplier": "SA Multiplier", "SA Unit": "SA Unit", "SA Unit Content": "SA Unit Content", "SA Valuation": "SA Valuation", "SA Valuations": "SA Valuations", "Same Premium Adjustment strategy for All Goods": "Same Premium Adjustment strategy for All Goods", "Same TB type Formula has duplicate record. Please check.": "Same TB type Formula has duplicate record. Please check.", "Save": "Save", "Save as Draft": "Save as Draft", "Save Illustration Sequence": "Save Illustration Sequence", "Save Sort": "Save Sort", "Save successfully": "Save successfully", "Save Successfully": "Save Successfully", "Saved successfully": "Saved successfully", "Scenario Code": "Scenario Code", "Scenario Code & Scenario Name": "Scenario Code & Scenario Name", "Scenario code can not be duplicated": "Scenario code can not be duplicated", "Scenario Name": "Scenario Name", "Scheduled Arrival Time": "Scheduled Arrival Time", "Scheduled Departure Time": "Scheduled Departure Time", "Search": "Search", "Search by Policy": "Search by Policy", "Search by Transportation": "Search by Transportation", "Search Result": "Search Result", "Second Date": "Second Date", "Select": "Select", "Select All": "Select All", "Select Benefit Factors": "Select Benefit Factors", "Select Cancellation Type": "Select Cancellation Type", "Select Claim Stack(Template)": "Select <PERSON><PERSON><PERSON>(Template)", "Select Component": "Select Component", "Select Factor": "Select Factor", "Select Formula": "Select Formula", "Select Liability": "Select Liability", "Select Parameter": "Select Parameter", "Select Ratetable": "Select Ratetable", "Select the enumeration value you want.": "Select the enumeration value you want.", "Select the Format you want": "Select the Format you want", "Selection": "Selection", "Sequence": "Sequence", "Serial No.": "Serial No.", "Service Fee Clawback": "Service Fee Clawback", "Set": "Set", "Set Default Time": "Set Default Time", "Set Fixed Time": "Set Fixed Time", "Set Name": "Set Name", "Set1": "Set1", "Set2": "Set2", "Set3": "Set3", "Severity": "Severity", "Severity Definition": "Severity Definition", "Sex": "Sex", "Short Term Premium": "Short Term Premium", "Short Term Premium Calculation Method": "Short Term Premium Calculation Method", "Single Top Up Type": "Single Top Up Type", "Source": "Source", "Specify the calculation order": "Specify the calculation order", "Stack Code": "Stack Code", "Stack Component Name": "Stack Component Name", "Stack Name": "Stack Name", "Stack Template Code": "Stack Template Code", "Stack Template Name": "Stack Template Name", "Stack Type": "Stack Type", "Stack Unit": "Stack Unit", "Stack Unit Type": "Stack Unit Type", "Stack Value": "Stack Value", "Stack Value Type": "Stack Value Type", "Stacks Related to Special Agreements": "Stacks Related to Special Agreements", "Standard Premium": "Standard Premium", "Start date": "Start date", "Start Date": "Start Date", "Starting Scenario": "Starting <PERSON><PERSON><PERSON>", "Status": "Status", "Step": "Step", "Step 1": "Step 1", "Step 1 Download Test Data Template": "Step 1 Download Test Data Template", "Step 1: Input Value for Factors in the Page": "Step 1: Input Value for Factors in the Page", "Step 2": "Step 2", "Step 2 Upload Test Data File": "Step 2 Upload Test Data File", "Step 2: Input Value for Factors in the Excel": "Step 2: Input Value for Factors in the Excel", "Step 3 Download Test Result": "Step 3 Download Test Result", "Step 3: Testing Result": "Step 3: Testing Result", "Step result is duplicated with paramater, please modify.As follows": "Step result is duplicated with paramater, please modify.As follows", "Strategy": "Strategy", "Strategy Code": "Strategy Code", "Strategy Name": "Strategy Name", "Sub Formula": "Sub Formula", "Sub POS Effective Date": "Sub POS Effective Date", "Submit": "Submit", "Submit Product": "Submit {{product,product}}", "Submit successfully": "Submit successfully", "Submit Successfully": "Submit Successfully", "Summary": "Summary", "Support entering a regular expression to control the input rules for Account Number by each Bank. For example, if Account Number should only allow 10 to 12 digits(number), you can configure it as ^\\d{10,12}$ or ^[0-9]{10,12}$.": "Support entering a regular expression to control the input rules for Account Number by each Bank. For example, if Account Number should only allow 10 to 12 digits(number), you can configure it as ^\\d{10,12}$ or ^[0-9]{10,12}$.", "support list calculation including +,-,*,/": "support list calculation including +,-,*,/", "Surgery": "Surgery", "Surgery Configuration Method": "Surgery Configuration Method", "Surgery Customized Data Details": "Surgery Customized Data Details", "Surgery Level": "Surgery Level", "Surgery Level cannot be empty": "Surgery Level cannot be empty", "Surgery Tag1 cannot be empty": "Surgery Tag1 cannot be empty", "Surgery Tag2 cannot be empty": "Surgery Tag2 cannot be empty", "Survival Benefit": "Survival Benefit", "Survival Benefit Leading Days": "Survival Benefit Leading Days", "Switching the method of diagnosis configuration will auto clear the configured data.": "Switching the method of diagnosis configuration will auto clear the configured data.", "Switching the method of surgery configuration will auto clear the configured surgery data.": "Switching the method of surgery configuration will auto clear the configured surgery data.", "System Data": "System Data", "System Data Value": "System Data Value", "System error": "System error", "System is abnormal, please try again later.": "System is abnormal, please try again later.", "System pre-set data": "System pre-set data", "System support to configure linkage between following fields": "System support to configure linkage between following fields", "System will automatically conduct batch test when test data is uploaded successfully.": "System will automatically conduct batch test when test data is uploaded successfully.", "System will generate a test data template automatically based on parameters used in the formula.": "System will generate a test data template automatically based on parameters used in the formula.", "tables": "tables", "Tag Definition": "Tag Definition", "Tag definition cannot be empty": "Tag definition cannot be empty", "Tag1": "Tag1", "Tag2": "Tag2", "Target Currency": "Target Currency", "Target Date": "Target Date", "Tax Collection Rule": "Tax Collection Rule", "Tax Rate": "Tax Rate", "Tax Setting": "Tax Setting", "Tax Type": "Tax Type", "Tax Value Type": "Tax Value Type", "TB Payable for Claim from": "TB Payable for Claim from", "TB Payable for Surrender from": "TB Payable for Surrender from", "TB Type": "TB Type", "Template change will influence object and liability setup.": "Template change will influence object and liability setup.", "Template Code": "Template Code", "Template Code / Name": "Template Code / Name", "Template Content": "Template Content", "Template Correlation Info": "Template Correlation Info", "Template Description": "Template Description", "Template Name": "Template Name", "Template Status": "Template Status", "tenant": "Tenant", "Tenant Data Configuration": "Tenant Data Configuration", "Terminal Bonus": "Terminal Bonus", "Terminal Bonus Indicator": "Terminal Bonus Indicator", "Terminate Liability After Claim Waiver": "Terminate Liability After <PERSON><PERSON><PERSON>", "Terminate Policy After Terminate Liability": "Terminate Policy After Terminate Liability", "Terminate Product After Terminate Liability": "Terminate {{product,product}} After Terminate Liability", "Terminate Related Liability": "Terminate Related Liability", "Terminated Reason": "Terminated Reason", "Test": "Test", "Test Result": "Test Result", "The “DCA frequency” table should at least have one record.": "The “DCA frequency” table should at least have one record.", "The automatic fund rebalancing will only occur when the portfolio variance from the pre-specified premium allocationt exceeds certain range.": "The automatic fund rebalancing will only occur when the portfolio variance from the pre-specified premium allocationt exceeds certain range.", "The code could only contain letters, numbers, and underscores (_).": "The code could only contain letters, numbers, and underscores (_).", "The component code could only contain letters, numbers, and underscores (_).": "The component code could only contain letters, numbers, and underscores (_).", "The content of the page has been modified, are you sure you want to leave": "The content of the page has been modified, are you sure you want to leave", "The content of the page has been modified, are you sure you want to leave without saving?": "The content of the page has been modified, are you sure you want to leave without saving?", "The coverage period agreement will be deleted": "The coverage period agreement will be deleted", "The current configuration cannot be modified after submission, confirm to continue?": "The current configuration cannot be modified after submission, confirm to continue?", "The fixed due date refers to the same day of each month. If certain month doesn't have the date, it will be the last day of that month.": "The fixed due date refers to the same day of each month. If certain month doesn't have the date, it will be the last day of that month.", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.", "The following benefit option(s) have been configured. The uploaded template will overwrite the data. Please confirm to proceed.": "The following benefit option(s) have been configured. The uploaded template will overwrite the data. Please confirm to proceed.", "The following calculation factors are not found in structure.": "The following calculation factors are not found in structure.", "The following products have used this formula. Editing formula and the following product will be affected. Please check before change:": "The following {{product,product,lowerCase}}s have used this formula. Editing formula and the following {{product,product,lowerCase}} will be affected. Please check before change:", "The formula code could only contain letters, numbers, and underscores (_).": "The formula code could only contain letters, numbers, and underscores (_).", "The General category can only be referenced in formula definitions and is not limited to any specific formula category.": "The General category can only be referenced in formula definitions and is not limited to any specific formula category.", "The indicator of include or exclude is mandatory for diagnosis code configuration.": "The indicator of include or exclude is mandatory for diagnosis code configuration.", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59", "The name format will follow the sequence below, the sequence can be manully adjusted.": "The name format will follow the sequence below, the sequence can be manully adjusted.", "The object configured in {{liabilityId}} {{liabilityName}} does not exist in the product.": "The object configured in {{liabilityId}} {{liabilityName}} does not exist in the {{product,product,lowerCase}}.", "The operator used for exponentiation. It raises a number to the power of another number. For example: 2*4 = 2*2*2*2 = 16": "The operator used for exponentiation. It raises a number to the power of another number. For example: 2*4 = 2*2*2*2 = 16", "The policy of GroupEB does not support the process of deactivating the insured": "The policy of GroupEB does not support the process of deactivating the insured", "The product code could only contain letters, numbers, and underscores (_).": "The {{product,product,lowerCase}} code could only contain letters, numbers, and underscores (_).", "The ratetable code could only contain letters, numbers, and underscores (_).": "The ratetable code could only contain letters, numbers, and underscores (_).", "The rateTable is used and affected in several places below. Please confirm changes.": "The rateTable is used and affected in several places below. Please confirm changes.", "The result of formula is repeated naming(case insensitive)": "The result of formula is repeated naming(case insensitive)", "The second value should be greater than or equal to the first value.": "The second value should be greater than or equal to the first value.", "The second value should greater than the first value": "The second value should greater than the first value", "The selected formula has been updated. Please reselect.": "The selected formula has been updated. Please reselect.", "The selected rate table has been updated. Please reselect.": "The selected rate table has been updated. Please reselect.", "The stack code could only contain letters, numbers, and underscores (_).": "The stack code could only contain letters, numbers, and underscores (_).", "The table contains an extensive amount of data and cannot be previewed here. Please use the download button to obtain and review the file locally": "The table contains an extensive amount of data and cannot be previewed here. Please use the download button to obtain and review the file locally", "The template code could only contain letters, numbers, and underscores (_).": "The template code could only contain letters, numbers, and underscores (_).", "The template has been referenced and cannot be deleted.": "The template has been referenced and cannot be deleted.", "The template has been referenced and cannot be edited.": "The template has been referenced and cannot be edited.", "The template has been used by following products. Please confirm if any deletion is required.": "The template has been used by following {{product,product,lowerCase}}s. Please confirm if any deletion is required.\n{{productCodes}}", "The template has been used by following products. Please confirm if any modification is required.": "The template has been used by following {{product,product,lowerCase}}s. Please confirm if any modification is required.\n{{productCodes}}", "The Usage Premium Start Date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "The Usage Premium Start Date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.", "The value of the last step must be 'final', and the value of other steps cannot be 'final', 'final' is case insensitive": "The value of the last step must be 'final', and the value of other steps cannot be 'final', 'final' is case insensitive", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "There is unfinished editing in <{{title}}>. Please complete before proceeding.", "There no found.": "There no found.", "Third Party Agreement": "Third Party Agreement", "This agreement is replaced by \"Claim Eligibility and Policy Matching\". Please use the new one if needed.": "This agreement is replaced by \"Claim Eligibility and Policy Matching\". Please use the new one if needed.", "This component has already been used and cannot be modified or deleted.": "This component has already been used and cannot be modified or deleted.", "This conditional factor's value already exists, please modify it.": "This conditional factor's value already exists, please modify it.", "This configuration can only be configured once, and cannot be modified after the configuration is completed.": "This configuration can only be configured once, and cannot be modified after the configuration is completed.", "This formula is referenced as a sub-formula in below. Please update the following parent formulas after saving this formula.": "This formula is referenced as a sub-formula in below. Please update the following parent formulas after saving this formula.", "This Illustration Scenario is already in use under a Benefit Illustration Item. Please disassociate it from the Benefit Illustration Item first.": "This Illustration Scenario is already in use under a Benefit Illustration Item. Please disassociate it from the Benefit Illustration Item first.", "This insurable interest has been used in the following package: [{{packages}}], please delete insurable interest in Package first.": "This insurable interest has been used in the following {{package,package,lowerCase}}: [{{packages}}], please delete insurable interest in {{package,package}} first.", "This liability has been used in the following package: [{{packages}}], please delete liability in Package first.": "This liability has been used in the following {{package,package,lowerCase}}: [{{packages}}], please delete liability in {{package,package}} first.", "This product is read only.": "This {{product,product,lowerCase}} is read only.", "This ratetable is relied on": "This ratetable is relied on", "This record has been deleted from premium period configuration. Please manually delete this record to ensure data consistency.": "This record has been deleted from premium period configuration. Please manually delete this record to ensure data consistency.", "This record was configured based on old version. Currently, viewing and editing are not supported.": "This record was configured based on old version. Currently, viewing and editing are not supported.", "This rider product has been used in the following package: [{{packages}}], please delete rider product in Package first.": "This rider {{product,product,lowerCase}} has been used in the following {{package,package,lowerCase}}: [{{packages}}], please delete rider {{product,product,lowerCase}} in {{package,package}} first.", "This section configures the system rules when installment premium is not paid on time.": "This section configures the system rules when installment premium is not paid on time.", "This section configures the system rules when the total investment value (policy value) is insufficent to pay charges.": "This section configures the system rules when the total investment value (policy value) is insufficent to pay charges.", "This section is required for regular paid product. No need for single premium product.": "This section is required for regular paid {{product,product,lowerCase}}. No need for single premium {{product,product,lowerCase}}.", "Time Zone": "Time Zone", "Times Type": "Times Type", "Tip1： Support calculation sign including": "Tip1： Support calculation sign including", "Tip2： Support embedded calculation method including": "Tip2： Support embedded calculation method including", "Tip3： Date Format only accept": "Tip3： Date Format only accept", "Titile": "<PERSON>itile", "Tiv（Mock）": "<PERSON><PERSON>（<PERSON><PERSON>）", "to": "to", "To define the premium allocation strategy.": "To define the premium allocation strategy.", "To define whether claim reserve equals 0 is allowed for claim acceptance.": "To define whether claim reserve equals 0 is allowed for claim acceptance.", "To reduce duplicate configurations, for POS Items applying to the entire policy, please go to [Package Management - Policy Change] section.": "To reduce duplicate configurations, for POS Items applying to the entire policy, please go to [{{package,package}} Management - Policy Change] section.", "Top Up Frequency": "Top Up Frequency", "Trace": "Trace", "Transaction Type": "Transaction Type", "Transportation Data": "Transportation Data", "Transportation Date": "Transportation Date", "Transportation Facility": "Transportation Facility", "Transportation No.": "Transportation No.", "Transportation Number": "Transportation Number", "Transportation Status": "Transportation Status", "Transportation Type": "Transportation Type", "Treatment Condition": "Treatment Condition", "Trigger Additional Maturity Benefit": "Trigger Additional Maturity Benefit", "Trigger Cash Refund": "Trigger Cash Refund", "Trigger Type": "Trigger Type", "Try a different keyword.": "Try a different keyword.", "Type": "Type", "Type of Amount Per Period": "Type of Amount Per Period", "Type of Loss": "Type of Loss", "Uncollected Premium Deduction": "Uncollected Premium Deduction", "Under this scenario, the policy Lapse process will be trigger when TIV is un-sufficient. Please make related configuration at \"Handling of TIV Insufficient\" section.": "Under this scenario, the policy Lapse process will be trigger when TIV is un-sufficient. Please make related configuration at \"Handling of TIV Insufficient\" section.", "Under this scenario, the policy will keep effective when enter premium holiday. And once the allowed premium holiday period is used up, system will lapse policy when premium is unpaid.": "Under this scenario, the policy will keep effective when enter premium holiday. And once the allowed premium holiday period is used up, system will lapse policy when premium is unpaid.", "Underwriting Enumerated Values": "Underwriting Enumerated Values", "Unit Type": "Unit Type", "Unpaid Bill Cancellation Rule": "Unpaid Bill Cancellation Rule", "Upload check failed, please download the error file to check!": "Upload check failed, please download the error file to check!", "Upload Data": "Upload Data", "Upload Failed": "Upload Failed", "Upload File": "Upload File", "Upload File Infomation": "Upload File Infomation", "Upload Files": "Upload Files", "Upload Ratetable": "Upload Ratetable", "Upload Success": "Upload Success", "Upload successfully": "Upload successfully", "Upload Successfully": "Upload Successfully", "Uploading, please refresh the page to check the results later.": "Uploading, please refresh the page to check the results later.", "URL": "URL", "Usage Based": "Usage Based", "Usage Based Insurance Agreement": "Usage Based Insurance Agreement", "Usage Based Product": "Usage Based {{product,product}}", "Usage Premium Start Date": "Usage Premium Start Date", "Use customized data": "Use customized data", "Using Insurable Interest": "Using Insurable Interest", "UW Risk SA Setting": "UW Risk SA Setting", "Validation Details": "Validation Details", "value": "value", "Value": "Value", "Value Assignment": "Value Assignment", "Value Type": "Value Type", "Vehicle Claim Info": "Vehicle Claim Info", "Vehicle Database Model": "Vehicle Database Model", "Vehicle Database Model needs to be configured as “Cascade” if there exists multiple vehicle databases.": "Vehicle Database Model needs to be configured as “Cascade” if there exists multiple vehicle databases.", "Vehicle Database Name": "Vehicle Database Name", "Vehicle Database Name has already existed.": "Vehicle Database Name has already existed.", "Vehicle Databases": "Vehicle Databases", "Vehicle Elements Relationship Matrix": "Vehicle Elements Relationship Matrix", "Vehicle Market Value": "Vehicle Market Value", "Version": "Version", "Version Time": "Version Time", "Vesting Agreement": "Vesting Agreement", "View": "View", "View Claim Stack Template": "View Claim Stack Template", "View Component": "View Component", "View Detail": "View Detail", "View Enumerated Fields Dependency": "View Enumerated Fields Dependency", "View Formula": "View Formula", "View Liability": "View Liability", "View My Creations": "View My Creations", "View Ratetable": "View Ratetable", "View Reference": "View Reference", "View Structure": "View Structure", "VIRTUAL MAIN": "VIRTUAL MAIN", "Virtual Main Benefit": "Virtual Main Benefit", "Wait Until Grace Period Expires": "Wait Until Grace Period Expires", "Waiting Period": "Waiting Period", "Waiting Period of New Business": "Waiting Period of New Business", "Waiting Period of Reinstatement": "Waiting Period of Reinstatement", "Warning Notification": "Warning Notification", "Week": "Week", "When add a record for Liability A and Liability B as 1,000": "When add a record for Liability A and Liability B as 1,000", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month)": "When charge free month is available on {{product,product,lowerCase}}, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month)", "When charge free month is available on product, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month) ": "When charge free month is available on {{product,product,lowerCase}}, it may not start from policy effective. Charge free month will calculate starting from X policy month defined here. (At least start from 1st policy month) ", "When customer choose pre-defined investment strategy, the fund appointment rate for both premium direction and portfolio rebalance will follow the configuration on strategy.": "When customer choose pre-defined investment strategy, the fund appointment rate for both premium direction and portfolio rebalance will follow the configuration on strategy.", "When issue a policy, save illustration data in the policy.": "When issue a policy, save illustration data in the policy.", "When it is closed,  system will buy target fund chosen by customer directly after policy issuance. No delay of the investment.": "When it is closed,  system will buy target fund chosen by customer directly after policy issuance. No delay of the investment.", "When premium comes in, system will fill in the installment premium due": "When premium comes in, system will fill in the installment premium due", "Whether After Campaign Discount": "Whether After Campaign Discount", "Whether to use premium adjustment": "Whether to use premium adjustment", "With Extra Premium": "With Extra Premium", "With Guarantee Period": "With Guarantee Period", "With No Lapse Guarantee": "With No Lapse Guarantee", "Within No Lapse Guarantee": "Within No Lapse Guarantee", "Work Time": "Work Time", "Year": "Year", "Year(s)": "Year(s)", "yes": "Yes", "Yes，Charge Amount is": "Yes，Charge Amount is", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG", "You can only upload xls, xlsx": "You can only upload xls, xlsx", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.", "You can only upload XSL": "You can only upload XSL", "You don't have to configure it if you don't need it": "You don't have to configure it if you don't need it", "You have unsubmitted information. Do you want to discard it?": "You have unsubmitted information. Do you want to discard it?", "You haven,t configured your bank information yet": "You haven,t configured your bank information yet"}