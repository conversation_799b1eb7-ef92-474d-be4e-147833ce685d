//  在全局的样式文件中



// 如果用到nagrand Modal, 那antd5 modal样式问题覆盖
.#{$product-prefix}-modal.nagrand-modal,
.#{$product-prefix}-modal.nagrand-method-modal {
  .#{$product-prefix}-modal-confirm-btns {
    margin-top: 24px;
  }

  // 表单label颜色
  .#{$product-prefix}-form-item .#{$product-prefix}-form-item-label>label {
    color: var(--text-color);
  }
}

// Form的vertical布局中，radio会被control-input包住，导致radio的高度有22px，导致间距过大
// 将min-height设置为radio的高度，Input本身具有32px的高度，会自动撑开，所以不会影响Input
.#{$product-prefix}-form-vertical {
  .#{$product-prefix}-form-item .#{$product-prefix}-form-item-control-input {
    min-height: 22px;
  }
}

.#{$product-prefix}-input-disabled[disabled] {
  color: $disabled-color;
}

.horizontal-radio-group {
  margin-bottom: 0px;

  .#{$product-prefix}-row {
    flex-direction: row !important;
    flex-wrap: nowrap;
    align-items: baseline;
  }

  .#{$product-prefix}-col {
    margin-right: 16px !important;
  }

  .#{$product-prefix}-form-item-label {
    padding-right: 24px !important;
    white-space: nowrap !important;
  }

  .#{$product-prefix}-form-item-control {
    width: auto !important;
  }

}

.#{$product-prefix}-modal-confirm-content {
  word-break: break-all;
}