import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Form, Layout, Skeleton, Space, message } from 'antd';

import { useRequest } from 'ahooks';
import classNames from 'classnames/bind';
import { unionBy } from 'lodash-es';

import { Modal } from '@zhongan/nagrand-ui';

import { ClaimStackComponent, ClaimStackTemplateInfo } from 'genesis-web-service';
import { MultiLanguageContent, StackTemplateStatus } from 'genesis-web-service/service-types/product-types/package';

import LeftArrowIcon from '@product/asset/svg/left-arrow.svg';
import { ModeType } from '@product/common/interface';
import { useDrawerState } from '@product/hook/common';
import { RouteComponentProps } from '@product/router';
import { NewProductService } from '@product/services/product/product.service.new';

import { ClaimStackManagementQueryType } from '../ClaimStackManagement/interface';
import ClaimStackComponentDrawer from '../ClaimStackTemplate/components/ClaimStackComponentDrawer/ClaimStackComponentDrawer';
import { ClaimStackTemplateProvider } from './ClaimStackTemplateProvider';
import styles from './Layout.module.scss';
import SelectComponent from './components/SelectComponent/SelectComponent';
import TemplateDetail from './components/TemplateDetail/TemplateDetail';
import { useTemplateFactors } from './hooks/useTemplateFactors';
import { StackTemplateValues } from './interface';
import { convertComponent, convertLanguageContents, revertComponent, revertLanguageContents } from './util';

const cx = classNames.bind(styles);

const { Sider, Content } = Layout;

const ClaimStackTemplateDetail = ({ navigate, location }: RouteComponentProps): JSX.Element => {
  const [t] = useTranslation(['product', 'common']);
  const parsedUrl = new URL(window.location.href);
  const templateCode = parsedUrl.searchParams.get('templateCode');
  const urlMode = parsedUrl.searchParams.get('mode');
  const mode = location?.state?.mode || urlMode || ModeType.Add;

  const readonly = mode === ModeType.View;
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();
  const [componentDrawerState, setComponentDrawerState] = useDrawerState<ClaimStackTemplateInfo>();
  const [components, setComponents] = useState<ClaimStackComponent[]>([]);

  const { getTemplateFactors } = useTemplateFactors();

  const { run: fetchComponents, loading: componentsLoading } = useRequest(
    () => {
      const param = {
        page: 0,
        size: 10000,
      };
      return NewProductService.StackTemplateService.listComponents(param);
    },
    {
      manual: true,
      onSuccess: result => {
        if (result?.content) {
          setComponents(result.content);
        }
      },
    }
  );

  const { runAsync: updateTemplate } = useRequest(
    (id, data) => NewProductService.StackTemplateService.updateTemplate(id, data),
    {
      manual: true,
    }
  );
  const { runAsync: createTemplate } = useRequest(data => NewProductService.StackTemplateService.createTemplate(data), {
    manual: true,
  });
  const { data } = useRequest(
    () => {
      if (templateCode) {
        return NewProductService.StackTemplateService.getTemplate(templateCode);
      }
      return Promise.resolve({});
    },
    { refreshDeps: [templateCode] }
  );

  const templateDetail = data as StackTemplateValues;

  const selectedCodes = useMemo(
    () => templateDetail?.components?.map((comp: ClaimStackComponent) => comp.code),
    [templateDetail?.components]
  );

  const headerTitle = useMemo(() => {
    switch (mode) {
      case ModeType.Add:
        return t('Add New Claim Stack Template');
      case ModeType.Edit:
        return t('Edit Claim Stack Template');
      case ModeType.View:
        return t('View Claim Stack Template');
      default:
        return t('Add New Claim Stack Template');
    }
  }, [mode, t]);

  const publishOrSaveDraftCallback = (status: StackTemplateStatus, tCode: string) => {
    // publish : back to template management
    if (status === StackTemplateStatus.PUBLISHED) {
      message.success(t('Publish Successfully'));
      navigate(`/product/claim-stack/template?queryType=${ClaimStackManagementQueryType.CLAIM_STACK_TEMPLATE_QUERY}`);
    } else {
      message.success(t('Save Successfully'));
      if (!templateCode) {
        // 首次save , 刷新url
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('templateCode', tCode);
        window.location.href = currentUrl.toString();
      }
    }
  };

  const publishOrSaveDraft = async (status: StackTemplateStatus) => {
    const values = (await form.validateFields()) as StackTemplateValues;
    const { components: templateComponents, multiLanguageContents: languageContents } = values;
    if (!templateComponents?.length) {
      message.error(t('No component is selected. Please modify.'));
      return;
    }
    const formattedComponents = templateComponents?.map(revertComponent);
    const multiLanguageContents = revertLanguageContents(languageContents as Record<string, string>);

    const templateFactors = getTemplateFactors(formattedComponents);
    const params = {
      ...templateDetail,
      ...values,
      status,
      components: formattedComponents,
      factors: templateFactors,
      multiLanguageContents,
      content: multiLanguageContents?.[0]?.content || '',
    };

    // 需要校验该 template 下所引用的 component 是否为同一类型
    const isSameStackTypes = unionBy(formattedComponents, 'stackType')?.length === 1;
    if (!isSameStackTypes) {
      Modal.warning({
        title: StackTemplateStatus.DRAFT ? t('Save as Draft') : t('Publish'),
        content: t(
          'Only components with the same stack type can be in one template. Different stack types are detected in your submission. Please check the configuration.'
        ),
        okText: t('Got it'),
      });
      return;
    }

    if (templateDetail?.id) {
      // update -> put
      updateTemplate(templateDetail.id, params).then(res => {
        if (res) {
          publishOrSaveDraftCallback(status, values.code);
        }
      });
      return;
    }
    // create -> post
    createTemplate(params).then(res => {
      if (res) {
        publishOrSaveDraftCallback(status, values.code);
      }
    });
  };

  useEffect(() => {
    const {
      components: stackComps,
      multiLanguageContents: templateLanguageContents,
      ...restTemplate
    } = templateDetail || {};
    const stackTemplateValues = {
      ...restTemplate,
      components: stackComps?.map(convertComponent),
      multiLanguageContents: convertLanguageContents(templateLanguageContents as MultiLanguageContent[]),
    };
    form.setFieldsValue({ ...stackTemplateValues });
  }, [form, templateDetail]);

  useEffect(() => {
    fetchComponents();
  }, []);

  return (
    <ClaimStackTemplateProvider>
      <Form
        layout="vertical"
        form={form}
        colon={false}
        name="template_detail"
        autoComplete="off"
        className={cx('layout')}
        initialValues={templateDetail} // Set initialValues here
      >
        <div className={cx('stack-template-title')}>
          <div
            onClick={() => {
              navigate(
                `/product/claim-stack/template?queryType=${ClaimStackManagementQueryType.CLAIM_STACK_TEMPLATE_QUERY}`
              );
            }}
            className={cx('back-to-search')}
          >
            <LeftArrowIcon />
            <span>{t('Back to Search')}</span>
          </div>
          <span className="text-@disabled-color">|</span>
          <span>{headerTitle}</span>
        </div>
        <Layout className={cx('stack-template-content-layout')}>
          <Sider width={264} className="p-6">
            <Skeleton loading={loading}>
              <SelectComponent
                disabled={readonly}
                setComponentDrawer={setComponentDrawerState}
                components={components}
                initialCodes={selectedCodes}
              />
            </Skeleton>
          </Sider>
          <Content className={cx('stack-template-content')}>
            <Skeleton loading={loading} paragraph={{ rows: 12 }}>
              <Divider type="vertical" className={cx('stack-template-middle-divider')} />
              <div className="h-full p-6 overflow-y-auto">
                <TemplateDetail readonly={readonly} templateDetail={templateDetail} />
              </div>
            </Skeleton>
          </Content>
        </Layout>
        <footer className={cx('stack-template-footer')}>
          <Space>
            <Button disabled={readonly} size="large" onClick={() => publishOrSaveDraft(StackTemplateStatus.DRAFT)}>
              {t('Save as Draft')}
            </Button>
            <Button
              disabled={readonly}
              size="large"
              type="primary"
              onClick={() => publishOrSaveDraft(StackTemplateStatus.PUBLISHED)}
            >
              {t('Publish')}
            </Button>
          </Space>
        </footer>
        <ClaimStackComponentDrawer
          {...componentDrawerState}
          onClose={() => {
            setComponentDrawerState({
              visible: false,
              mode: ModeType.Add,
              record: undefined,
            });
            fetchComponents();
          }}
        />
      </Form>
    </ClaimStackTemplateProvider>
  );
};

export default ClaimStackTemplateDetail;
