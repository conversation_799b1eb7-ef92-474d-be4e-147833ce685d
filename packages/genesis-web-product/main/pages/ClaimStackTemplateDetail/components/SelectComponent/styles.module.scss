.select-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  :global {
    .no-result {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .no-data-icon {
        margin: -20px 0;
        transform: scale(0.55, 0.55);
        font-size: 88px;
      }

      .no-data-text {
        font-size: var(--font-size-root);
        color: var(--text-color);
        margin-top: var(--gap-sm);
        font-weight: 500;
        text-align: center;

        .hint {
          margin-top: 8px;
          color: var(--text-disabled-color);
        }
      }
    }
  }

  .stack-type-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :global {
      .#{$ant-prefix}-tabs-nav {
        padding: 0 !important;
      }

      .#{$ant-prefix}-tabs-tab-btn {
        font-size: 14px !important;
        font-weight: 500 !important;
      }

      .#{$ant-prefix}-tabs-content-holder,
      .#{$ant-prefix}-tabs-content,
      .#{$ant-prefix}-tabs-tabpane-active {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
      }
    }
  }
}