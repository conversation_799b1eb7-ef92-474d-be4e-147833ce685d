import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Checkbox } from 'antd';

import cls from 'classnames';
import { cloneDeep, debounce, isEmpty, isString } from 'lodash-es';

import { Icon, Input, Tabs, TextBody } from '@zhongan/nagrand-ui';

import { ClaimStackTemplateInfo } from 'genesis-web-service';

import { ModeType } from '@product/common/interface';
import { useBizDict } from '@product/hook/bizDict';
import { DrawerState } from '@product/hook/common';

import { useClaimStackTemplateContext } from '../../ClaimStackTemplateProvider';
import { ComponentValues } from '../../interface';
import styles from './styles.module.scss';

interface SelectComponentProps {
  disabled?: boolean;
  initialCodes?: string[]; // 初始选中的值
  components: ComponentValues[];
  setComponentDrawer: (params: DrawerState<ClaimStackTemplateInfo>) => void;
}

const SelectComponent: React.FC<SelectComponentProps> = ({
  disabled,
  initialCodes,
  components,
  setComponentDrawer,
}) => {
  const [t] = useTranslation(['product', 'common']);
  const stackTypeBizDict = useBizDict('stackType');
  const [hasSearchResult, setHasSearchResult] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [selectedCodes, setSelectedCodes] = useState<string[]>(initialCodes ?? []);
  const [filteredData, setFilteredData] = useState<ComponentValues[]>([]);
  const { setSelectedComponents } = useClaimStackTemplateContext();

  const onSearchByContent = debounce((searchTxt: string) => {
    if (searchTxt === '') {
      setHasSearchResult(true);
      setFilteredData([...(components ?? [])]);
      return;
    }
    const loopFilter = (filterData: ComponentValues[]) =>
      filterData?.filter(data => {
        if (isString(data.name) && data.name.toLowerCase()?.includes(searchTxt.toLowerCase())) {
          return true;
        }
        return false;
      });

    const loopResult = loopFilter(cloneDeep(components) ?? []);
    setFilteredData(loopResult);
    setHasSearchResult(loopResult?.length > 0);
  }, 500);

  useEffect(() => {
    if (components?.length) {
      onSearchByContent(searchValue);
    }
  }, [components?.length, searchValue]);

  useEffect(() => {
    if (initialCodes && initialCodes.length > 0) {
      setSelectedCodes(initialCodes);
    }
  }, [initialCodes]);

  const onSelectedChange = (selectedComponentCodes: string[]) => {
    setSelectedCodes(selectedComponentCodes);
    const filteredComponents = selectedComponentCodes
      .map(code => components.find(component => component.code === code))
      .filter(component => component !== undefined);

    setSelectedComponents(filteredComponents);
  };

  return (
    <div className={styles.selectComponent}>
      <TextBody type="body" weight="bold" style={{ marginBottom: 16, fontSize: 16 }}>
        {t('Select Component')}
      </TextBody>
      <Tabs
        defaultActiveKey={stackTypeBizDict?.[0]?.enumItemName}
        onChange={() => setSearchValue('')}
        type="line-with-bg"
        className={styles.stackTypeTab}
      >
        {stackTypeBizDict?.map(item => {
          const { enumItemName, itemName } = item || {};
          return (
            <Tabs.TabPane tab={itemName} key={enumItemName}>
              <div className="flex mb-4">
                <Input
                  disabled={disabled}
                  value={searchValue}
                  prefix={<Icon type="search" />}
                  placeholder={t('Search')}
                  className="search-input mr-2 flex-1"
                  onChange={event => setSearchValue(event.target.value)}
                />
                <Button
                  disabled={disabled}
                  onClick={() => {
                    setComponentDrawer({
                      visible: true,
                      mode: ModeType.Add,
                    });
                  }}
                  icon={<Icon type="add" />}
                />
              </div>
              {hasSearchResult ? (
                <div className="flex-1 overflow-auto">
                  <Checkbox.Group
                    disabled={disabled}
                    value={selectedCodes}
                    onChange={onSelectedChange}
                    className="flex flex-col"
                  >
                    {components?.map(component => (
                      <div
                        key={component.id}
                        className={cls('mb-2 items-center flex', {
                          hidden: isEmpty(
                            filteredData.find(
                              ({ code, stackType }) => code === component.code && stackType === enumItemName
                            )
                          ),
                        })}
                      >
                        <Checkbox value={component.code} />
                        <span
                          title={component.name}
                          className="ml-2 font-medium max-w-[180px] cursor-pointer inline-block overflow-hidden whitespace-nowrap text-ellipsis"
                          onClick={() => {
                            setComponentDrawer({
                              visible: true,
                              mode: ModeType.View,
                              record: component,
                            });
                          }}
                        >
                          {component.name}
                        </span>
                      </div>
                    ))}
                  </Checkbox.Group>
                </div>
              ) : (
                <div className="no-result">
                  <Icon className="no-data-icon" type="no-data" />
                  <div className="no-data-text">
                    <div>{t('There no found.')}</div>
                    <div className="hint">{t('Try a different keyword.')}</div>
                  </div>
                </div>
              )}
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </div>
  );
};

export default SelectComponent;
