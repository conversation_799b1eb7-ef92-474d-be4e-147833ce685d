import type { CSSProperties, ReactNode } from 'react';

import type { SelectProps } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import type { Subscription } from '@umijs/max';

import { RenderMode } from '@zhongan/nagrand-ui';

export enum Mode {
  EDIT = 'EDIT',
  READ = 'READ',
  ADD = 'ADD',
}

export const DefaultPaginationMapping = {
  [RenderMode.Card]: 12,
  [RenderMode.Table]: 10,
};
export interface ConnectModel<T> {
  namespace: string;
  state: T;
  subscriptions?: {
    setup: Subscription;
  };
}

export interface BizDictValue {
  parentId: number;
  dictKey: string;
  dictKeyName?: string;
  dictValue: string;
  dictValueName: string;
  dictDesc?: string;
  enumItemName?: string;
  childList: any[];
  itemExtend1?: string;
  itemExtend2?: string;
  itemExtend3?: string;
  itemExtend3Desc?: string;
  itemName?: string;
}

export interface SortItemProps {
  className?: string;
  style?: CSSProperties;
  'data-row-key': string;
  children: ReactNode[];
}

export enum DataTypeEnum {
  DATETIME = 'DATETIME',
  INPUT = 'INPUT',
  SELECT = 'SELECT',
  DATE = 'DATE',
  NUMBER = 'NUMBER',
  FILE = 'FILE',
  FORMULA = 'FORMULA',
  MULTISELECT = 'MULTISELECT',
  AUTOCOMPLETE = 'AUTOCOMPLETE',
}

export enum SelectType {
  UPLOAD = 'upload',
  DOWNLOAD = 'download',
}

// Customer name group format related types
export enum CustomerNameConnectedType {
  FullWidth = '1',
  HalfWidth = '2',
  NoSpace = '3',
}

export interface CustomerNameGroupFormat {
  character: CustomerNameConnectedType;
  childKeys: string[];
  sequence: string[];
}

export enum ModeEnum {
  EDIT = 'EDIT',
  READ = 'READ',
  ADD = 'ADD',
}

export type ColumnEx<T> = ColumnProps<T> & {
  editable?: boolean;
  editChildren?: ReactNode;
  placeholder?: string;
};

export interface SchemaProps {
  label: string;
  key: string;
  type?: string;
  ctrlProps?: {
    options?: { label: string; value: string | number }[];
    mode?: SelectProps<unknown>['mode'];
  };
}

export enum Step {
  Process = 'process',
  Wait = 'wait',
  Finish = 'finish',
  Error = 'error',
}
