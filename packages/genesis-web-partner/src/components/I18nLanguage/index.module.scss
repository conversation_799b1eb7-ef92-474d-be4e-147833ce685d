@import '../../variables.scss';

.language {
  :global {
    .#{$antd-prefix}-input-group-addon {
      background-color: #fff !important;
      cursor: pointer !important;
    }
  }
}
.language-drawer {
  :global {
    .#{$antd-prefix}-input-group-addon:first-child {
      .#{$antd-prefix}-select {
        .#{$antd-prefix}-select-selector {
          color: var(--text-color);
        }
        .#{$antd-prefix}-select-arrow {
          display: none;
        }
      }
      background: var(--white);
    }
  }
}
