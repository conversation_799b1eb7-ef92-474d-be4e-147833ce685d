{"0": "", "50": "", "--": "--", "(n)th": "{{n}}号", "{{frequency}}(s)": "{{frequency}}(s)", "{{name}} Account": "{{name}}账户", "{{name}} Account Status": "{{name}}账户状态", "{{year}} Work Schedule": "{{year}} 值班表", "00": "00", "1st": "1号", "21st": "21号", "22nd": "22号", "23rd": "23号", "2nd": "2号", "31st": "31号", "3rd": "3号", "500+": "500多", "A total of {totalNumber} documents were uploaded, {failNumber} of them failed to upload.": "本次共上传{totalNumber}份文件，其中有{failNumber}份上传失败。", "Abbreviation": "缩写", "Accept": "", "Accept Successful": "", "Account": "账户", "Account Information Settings": "账户信息设置", "Account Name": "", "Account No.": "", "account number": "账户号", "Account Type": "账户类型", "Account Usage": "账户用途", "Accumulated to Premium": "累计至保费", "Action": "", "Active": "", "Add": "新增", "Add {{Team}} Information": "新增{{team}}信息", "Add Account Information Settings": "新增账户信息设置", "Add Address": "新增地址", "Add Branch": "新增销售分支", "Add Contact Person Information": "新增联系人信息", "Add EmployeeBranch": "新增分公司", "Add New": "新增", "Add New Address": "新增地址", "Add New Agency": "", "Add New Agent": "新增代理人", "Add New at the Same Level": "在同级别新增", "Add New at the Sub Level": "在子级别新增", "Add New Bank Account": "新增账号", "Add New Branch": "新增销售分支", "Add New Branch Manager": "新增销售分支经理", "Add New Channel": "", "Add New Contacts Person": "新增联系人", "Add New Department": "新增部门", "Add New Doctor": "新增医生", "Add New Employee": "新增雇员", "Add New Group": "新增组", "Add New Headquarter": "新增总部", "Add New Insurance Company": "", "Add New Level": "添加新级别", "Add New License Type": "新增执照类型", "Add New Manager": "添加新经理", "Add New Partner": "", "Add New Relative": "新增亲属", "Add New Retail": "新增销售总部", "Add New Sales Agreement": "添加新的销售协议", "Add New Sales Channel": "", "Add New Service Agreement": "添加新服务协议", "Add New Service Company": "", "Add New Staff": "添加新员工", "Add New Team": "新增{{team}}", "Add New Team Manager": "新增{{team}}经理", "Add Reconciliation": "", "Add Region": "新增销售区域", "Add successfully": "", "Address Type": "地址类型", "Address Zip Code": "地址邮政编码", "After the agent type is configured, it can not be modified. Please confirm.": "代理人类型配置后将无法修改。请确认。", "After the commission/commission clawback calculation range is configured, it cannot be modified. Please confirm.": "", "Agency": "代理店", "Agency Code": "代理店代码", "Agency Company": "代理店", "Agency Name": "代理店名称", "Agency No": "", "Agent": "代理人", "Agent Code": "代理人代码", "Agent Information": "代理人信息", "Agent Name": "代理人姓名", "Agent Sales Hierarchy": "代理人销售层级", "Agent Sales Hierarchy Configuration": "代理人销售层级配置", "Agent Type": "代理人类型", "Agent Type Configuration": "代理人类型配置", "All affiliated partner information will be deleted at the same time. Are you sure to delete this record?": "所有附属的partner信息都会同时被删除，是否确定删除？", "All selected tasks perform the following operations": "", "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula": "在公式<commGST>中，我们可以使用在顺序1中计算的净佣金模式因子来配置公式。", "and process this task.": "", "API Content:": "", "Approval": "", "Approval | {{count}} Option(s)": "", "Are you sure to {{type}} this task?": "", "Are you sure to cancel this record?": "", "Are you sure to change Partner status to {status}?": "您确定要将合作伙伴状态更改为{status}吗?", "Are you sure to change the status? The resigned agent will not be able to continue to issue policies.": "您确定要更改状态吗?辞职的代理人将不能继续出单。", "Are you sure to delete this bank and all relevant information?": "你确定要删除该银行和所有相关信息吗？", "Are you sure to delete this branch and all relevant information?": "您确定要删除这个分公司和所有相关信息吗吗？", "Are you sure to delete this branch?": "您确定要删除这个销售分支吗？", "Are you sure to delete this broker company and all relevant information?": "确定删除该保险经纪公司及所有相关信息吗？", "Are you sure to delete this front line channel and all relevant information?": "", "Are you sure to delete this headquarter and all relevant branch information?": "您确定要删除该总部和所有分公司的信息吗？", "Are you sure to delete this key account channel and all relevant information?": "", "Are you sure to delete this record?": "您确定要删除此记录吗？", "Are you sure to delete this region and all relevant branch information?": "您确定要删除此销售区域和所有相关的销售分支信息吗？", "Are you sure to delete this relationship info?": "", "Are you sure to delete this retail and all relevant region/branch information?": "您确定要删除该销售总部和所有销售区域/销售分支的信息吗？", "Are you sure to delete?": "你确定要删除吗?", "Are you sure to Submit?": "您确定要提交吗?", "Are you sure you want to transfer these selected sales channels to the new team? This transfer will also move all policies managed by these sales channels. If some policies need to remain with the original team or be transferred to another team, please complete the policy transfer first.": "你确定要将这些选中的销售渠道转移到新的团队吗？本次转移会将这些销售渠道负责的所有保单都一起转移过去。如果部分保单需要留在原有团队或转移到别的团队，请先完成保单转移。", "Assessment Company": "", "Assessment Company Code": "定损公司代码", "Assessment Company Comments": "定损公司意见", "Assessment Company Name": "定损公司名称", "Associated": "关联", "at the Same Level": "在同级别新增", "at the Sub Level": "在子级别新增", "Attachment": "附件", "Back": "返回", "Back to task pool": "返回任务池", "Bank": "银行", "bank account": "账号", "Bank Branch Code": "", "Bank Branch Name": "", "Bank Code": "银行代码", "Bank Name": "银行名称", "Base on Sales Channel": "", "Basic Info": "基本信息", "Basic Information": "基本信息", "Batch Approval": "", "Batch Number": "批次编号", "Batch tips Date Range": "", "Batch tips Day": "", "Batch Upload": "批量上传", "Belonging Agency": "", "Blank Template": "空白模板", "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete employee information and its internal details.": "", "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete employee information and its internal details.'": "", "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete partner information and its internal details (deletion applies only to some related information).": "", "BP Number in SAP": "SAP记帐号", "Branch": "销售分支", "Branch Code": "销售分支代码", "Branch Information": "销售分支信息", "Branch Manager Information": "销售分支经理信息", "Branch manager is existing, you can’t add more branch manager": "销售分支经理已存在，不能新增销售分支经理。", "Branch Name": "销售分支名称", "Broker Company": "保险经纪公司", "Broker Company Code": "保险经纪公司代码", "Broker Company Name": "保险经纪公司名称", "Browse": "", "Business Activity Description": "", "Cancel": "取消", "Cancel Successfully": "", "Category": "类型", "Certificate No": "证书编号", "Certificate Type": "证书类型", "Change the status failed.": "更改状态失败。", "Change the status successfully.": "成功更改状态。", "Changing the classification will clear all the details. Confirm to change?": "变更分类会清除已配置的信息。确认变更吗？", "Changing the goods name will affect the selling authority, please confirm.": "变更商品名字会影响销售权限，请确认。", "channel": "栏目", "Channel Code": "栏目代码", "Channel Name": "栏目名称", "Channel No": "", "channel.common.required": "{{- label}}是必填项", "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.": "召回的计算方法在此处配置，而召回计算的触发点将在其他业务模块定义，如保全项约定模块等", "Click or drag the file here to upload": "在此点击或拖动文件至此来上传", "Click to add channel/insurance company": "", "Click to add relationship": "", "Clinic": "", "Close": "关闭", "Code": "代码", "Collapse": "折叠", "collection service fee(%)": "", "Comments": "评论", "Commission Collection Service Fee(%)": "", "Commission Settlement Method": "", "Commission Type": "佣金类型", "Commission&Commission Clawback Rule Configuration": "佣金/佣金召回规则配置", "Complete Data Set": "完整数据集", "Complete Data Set: An upload template that contains all data. Users can select portions of this data for processing.": "", "Completed": "完成", "Condition Query": "条件查询", "Configuration": "", "Configure the association between fields": "", "confirm": "", "Confirm": "确认", "Confirmation Required": "", "Contact Number": "联系电话", "Contact Person": "联络人", "Contact Person ID": "联络人证件号", "Contact Person ID Type": "联系人证件类型", "Contact Person Information": "联系人信息", "Contacts Name": "联系人姓名", "Contacts Person": "联系人", "Contacts Person Birthday": "联系人生日", "Contacts Person Type": "联系人类型", "Content": "内容", "Contract Service": "合同服务", "Country": "国家", "Country Code": "国家代码", "Create": "创建", "Create Time": "编辑时间", "Currency": "", "Current Handler": "", "Current Work Schedule": "当前值班表", "Custom Cycle": "", "Customized Query Conditions Setting": "自定义查询条件设置", "Data cannot be selected repeatedly": "", "Date of Birth": "出生日期", "Day(s)": "天", "Default Settlement Method": "", "Delete": "删除", "Delete successfully": "", "Department": "部门", "Department & Doctor": "科室&医生", "Department Code": "部门代码", "Department Name": "科室名字", "Department Type": "部门类型", "Description": "描述", "Details": "详细", "Diagnosis": "诊断", "Display Name": "显示名", "Doctor": "医生", "Doctor ID type": "医生身份证类型", "Doctor Name": "医生姓名", "Document": "文件", "Does the settled commission need to be transferred to the new sales channel": "已结算的佣金是否需要转移到新的销售渠道？", "Does the settled commission need to be transferred to the new sales channel?": "", "download": "下载", "Download": "下载", "Download Template": "下载模板", "Download to View Details": "下载以查看详情", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "同一公式类别和子类别存在重复的公式配置。请编辑原始配置。", "Duplicate Order. Please change.": "", "During from": "区间", "E-mail": "邮箱", "Edit": "修改", "Edit Account Information Settings": "编辑账户信息设置", "Edit Address": "编辑地址", "Edit Agent": "编辑代理人", "Edit Branch": "编辑销售分支", "Edit Branch Information": "编辑销售分支信息", "Edit Branch Manager": "编辑销售分支经理信息", "Edit Contact Person Information": "编辑联系人信息", "Edit Department Information": "修改部门信息", "Edit Doctor information": "编辑医生信息", "Edit Employee Information": "编辑雇员信息", "Edit Group": "修改组", "Edit Headquarter Information": "编辑总部信息", "Edit License Type": "修改执照类型", "Edit Manager": "修改经理", "Edit Reconciliation": "", "Edit Relative Information": "编辑亲属信息", "Edit Sales Agreement": " 编辑销售协议", "Edit Service Agreement": "编辑服务协议", "Edit Staff information": "修改员工信息", "Edit Team": "编辑{{team}}", "Edit Team Information": "编辑{{team}}信息", "Edit Team Manager": "编辑{{team}}经理", "Edited successfully": "", "Effective": "有效的", "Effective Date": "生效日期", "Effective Period": "有效期", "Email": "邮箱", "Email is invalid": "", "Employee": "雇员", "Employee Code": "雇员代码", "Employee Department Name": "部门名字", "Employee Information": "雇员信息", "Employee Management": "组织&雇员管理", "Employee Name": "雇员名字", "EmployeeBranch": "分公司", "End of month (default value: 31st)": "※ 月末", "Error": "错误", "Error Detail": "", "Error Message": "", "EU member": "欧盟成员国", "Every": "", "Every {{dayInterval}} day {{time}}": "", "Every {{n}}": "每 {{n}}", "Every {{weekInterval}} weeks on {{weekday}}": "", "Every day {{time}}": "", "Every other day {{time}}": "", "Every other week on {{weekday}}": "", "Every week on {{weekday}}": "", "everyday": "", "Exclusive Agency or Not": "是否保司专属代理店", "Existed rows": "", "Expand All": "展开全部", "Experienced Fee": "经验费用", "Experienced Fee is required; {{name}} is required": "经验费用为必填字段; {{name}}为必填字段", "Expiry Date": "到期日", "Export": "", "External Code": "外部代码", "External Insurance Company": "外部保险公司", "External Insurance Company Code": "外部保险公司代码", "External Insurance Company Name": "外部保险公司名称", "Extraction Date": "", "Extraction Day": "X天后执行", "Extraction Duration": "", "Extraction Frequency": "", "Extraction Schedule Date": "", "Fax Number": "传真号码", "File Creator": "创建者", "file name": "文件名", "File Name": "文件名", "File Path": "", "File Size": "", "File Upload Time": "上传时间", "Fill in the existing sales agreement": "填写已有销售协议", "Fill Information": "填写信息", "Financial Institutions": "", "Financial Institutions Branch": "", "First Name": "名", "First Name2": "", "Fixed Rate": "固定费率", "Formula Category": "公式种类", "Formula Category (Clawback)": "公式种类（召回）", "Formula Code": "公式代码", "Formula Name": "公式名称", "Formula Name (Clawback)": "公式名称（召回）", "Formula Sub Category": "公式子类别", "Formula Sub Category (Clawback)": "公式子类别（召回）", "Frequency": "", "Front Line Channel": "前线渠道", "Front Line Channel Code": "", "Front Line Channel Name": "前线渠道名称", "Front Line Code": "", "Frozen": "冻结", "Full Re-generation": "", "Gender": "性別", "Goods": "商品", "Goods Name": "商品名称", "Group": "团体", "Group By Selection": "", "Group Name": "组名称", "GST Registered Organization": "商品与服务税登记组织", "GST Registration Number": "商品与服务税登记号码", "Headquarter": "总部", "Headquarter Code": "总部代码", "Headquarter Information": "总部信息", "Headquarter Name": "总部名称", "History": "历史", "Hospital": "", "Hospital Discount": "", "Hotline": "热线", "IBAN": "", "ID No": "医生身份证号码", "ID Number": "证件号码", "ID Type": "身份证明类型", "If Commission GST is configured based on NetCommission, then we could configure as": "如果按净佣金配置佣金商品与服务税（GST），我们可以配置如下", "If other sales channels have used this sales agreement, editing it will impact the related sales channels. Please check before making changes.": "如果其他销售渠道使用了此销售协议，编辑此销售协议将影响相关的销售渠道。更改之前请检查。", "If some months do not have this date, the configured date automatically becomes the last day of the month.": "如果某些月份没有这一日期，配置的日期则自动变为该月的最后一天。", "If the switch turns on, sub-channel data will be reconciled together.": "开关打开之后，子渠道的数据会一起对账。", "If the value is equal to Yes, the premium collection or refund will through Claim.": "如果值等于“是”，则通过理赔退费。", "If turn on the switch, then all historical un-settled data will be included.": "", "Immediately": "", "Import": "", "Import Failed!": "", "Import Operation Fail": "", "Import Successful!": "", "in": "", "In": "", "In Progress": "进行中", "In Total": "总计", "Individual or Organization Agency": "个人代理店/法人代理店", "Institute": "机构", "Institute Code": "机构代码", "Institute Grade": "", "Institute Name": "机构名称", "Institute Status": "", "Institute Type": "机构类型", "Insurance Company": "保险公司", "Insurance Company Code": "保险公司代码", "Insurance Company Name": "保险公司名称", "Insurance Company No": "", "Introduction": "介绍", "Invalid rows": "", "Investigation Area": "调查区域", "Investigation Hospital": "调查医院", "Investigator": "调查机构", "Investigator Code": "", "Issue Date": "签发日期", "Issue without payment": "是否允许非见费出单", "It should be noted that switching the agent sales hierarchy will cause the agent information to be cleared.": "需要注意切换代理人销售层级会导致代理人信息清空。", "Key Account Channel": "大客户渠道", "Key Account Channel Code": "", "Key Account Channel Name": "大客户渠道名称", "Last Name": "姓", "Last Name2": "", "Leasing Channel": "租赁渠道", "Leasing Channel Code": "租赁渠道代码", "Leasing Channel Name": "租赁渠道名字", "Legal Service Company": "法律服务机构", "Legal Service Company Code": "法律服务机构代码", "Legal Service Company Name": "法律服务公司名字", "Level": "层级", "License Effective Date": "许可证生效日期", "License No.": "许可证号", "loading": "", "Login Account Type": "登录帐号类型", "Logo": "图标", "Long Term": "", "Malaysia Standard Industrial Classification (MSIC) Code": "", "Manager": "经理", "Mapping with Organization": "与组织结构映射", "Mobile Phone": "手机号码", "month": "", "Monthly": "", "months": "", "More Partners below": "以下{{count}}家合作伙伴", "Name": "姓名", "Name is required": "名字是必填项", "Name of Account Holder": "", "Name_Code": "{{name}}_{{code}}", "Nationality": "国籍", "Native Account": "本地账户", "Network": "", "New Sales Agreement": "新的销售协议", "New Sales Channel": "新销售渠道", "next": "下一步", "Next": "", "Next {{n}}": "下 {{n}}", "No": "否", "No content yet": "", "No search results found": "没有找到搜索结果", "Number of Records": "记录数量", "of the Current Month": "", "of the Next 2 Month": "", "of the Next 2 Week": "", "of the Next Month": "", "of the Next Week": "", "Office City": "办公城市", "Office Hour": "办公时间", "OK": "OK", "On": "", "On {{date}} of every {{yearInterval}} year": "", "On {{date}} of every other year": "", "On {{date}} of every year": "", "On day {{date}} of every {{monthInterval}} months": "", "On day {{date}} of every month": "", "On day {{date}} of every other month": "", "Onboard Date": "入职日期", "onRecurrenceDate": "", "Opening Date": "开设日期", "Operated successfully": "", "Operation Time": "操作时间", "Operation Type": "", "Operator": "操作者", "Order": "排序编号", "Organization ID No.": "业务号码", "Organization ID Type": "企业证件类型", "Original Sales Agreement Code": "原销售协议代码", "Original Sales Channel Code": "原销售渠道代码", "Other Insurance Company Name": "代理申报的其他保司", "Other Related Business": "代理店其他关联业务", "out": "", "Out": "", "Overriding Commission": "抽佣", "pageIndex Handler": "", "Panel Hospital": "合作医院", "Params are invalid": "参数无效", "Partial Re-generation": "", "Partner": "合作方", "Partner Code": "合作伙伴代码", "Partner Details": "合作伙伴详情", "Partner List": "合作伙伴列表", "Partner List Upload History": "合作伙伴列表上传历史", "Partner Management": "合作伙伴管理", "Partner Management Approval": "", "Partner Name": "合作伙伴名称", "Partner Status": "", "Partner Type": "Partner Type", "Pay Method": "支付方式", "Payment Method": "支付方案", "Payment Method / Account Type": "支付方式 / 账户类型", "Payment/Collection": "", "Payment/Collection Method": "付费/收费方式", "Period of Validity": "有效期间", "Period Start Date": "", "period_validity": "", "Personal Work Schedule": "个人值班表", "Phone No.": "电话号码", "Phone Number": "电话号码", "Please complete branch information first.": "请先完善销售分支信息。", "Please complete Team Information first": "请先完善{{team}}信息。", "Please configure insurance company, agency, sales channel and relationship before saving": "", "Please create the basic information first.": "请先创建基本信息。", "Please Download Template First": "请先<1>下载模板</1>", "Please edit the work schedule by uploading the excel file.": "", "Please enter a valid character": "", "Please enter at least one condition": "请输入至少一个条件", "Please enter at least one search criteria": "请至少输入一项查询条件", "Please finish editing first": "", "please input": "", "Please Input": "", "Please input correct firstname.": "请输入正确的名字。", "Please input correct format": "请输入正确的格式", "Please input correct lastname.": "请输入正确的姓氏。.", "Please input Employee Code/Name": "请输入雇员代码/姓名", "Please input in correct format": "请输入正确格式", "Please input Sales Channel Code/Name": "请输入销售渠道代码/名字", "Please review the": "", "Please review the uploaded file and process this task.": "", "Please Select a company": "", "Please Select a Sales Channel": "", "Please select a sales channel first.": "请先选择一个销售渠道", "Please select a specific date first.": "请先选择具体日期。", "Please select an employee first.": "请先选择雇员。", "Please select an Organization ID Type": "", "Please select at least one item.": "", "Please select correct upload template(xlsx)": "请选择正确的文件类型(xlsx)", "Please select related formula for Commission": "请为佣金选择相关的公式", "Please select related formula for Commission Clawback": "请为佣金召回选择相关的公式", "Please select the number of sales hierarchy": "请选择销售层级数量", "Please select to add at least one relationship": "", "Please Upload File": "请上传文件", "Policy Effective Date": "保单有效日", "Policy No.": "保单编号", "Policy Transfer": "保单转移", "Policy Transfer Effective Date": "", "Policy Transfer Effective Date Type": "", "Policy Transfer Reason": "保单转移理由", "Policyholder Name": "保单持有人姓名", "Position": "职务", "Premium Collection and Refund for Customer": "客户保费收取及退款", "Private": "", "Public": "", "Public/Private": "", "Rate for Additional": "附加费率", "Rate for Initial": "初始费率", "Rate per KM": "每公里费率", "Re-generation Option": "", "Re-Upload": "重新上传", "reconciled": "", "Reconciliation": "", "Reconciliation Code": "", "Reconciliation Configuration": "", "Reconciliation Data: data that already reconciled.": "", "Reconciliation Data: data that already reconciled. Reconciliation Data: data that already reconciled": "", "Reconciliation Detail": "", "Reconciliation Fields": "", "Reconciliation File Format": "", "Reconciliation Frequency": "", "Reconciliation Goods Code": "", "Reconciliation Method": "", "Reconciliation Method & File": "", "Reconciliation Period": "对账区间", "Reconciliation Policy Dimension": "", "Reconciliation Recurrence": "", "Reconciliation Recurrence Date": "", "Reconciliation Result Display": "", "Reconciliation Time Zone": "对账时区", "Reconciliation& Settlement": "", "Reconciliation& Settlement Configuration": "", "Reconciliation&Settlement": "", "Reconciliation&Settlement Configuration": "", "Record": "条", "Records": "", "Records / Number of total records": "记录 / 总记录数", "Records_TotalRecords": "{{records}}/{{totalRecords}}", "Recurrence Date": "", "Region": "销售区域", "Register": "注册", "Registration": "注册", "Registration Date": "注册时间", "Reject Successful": "", "Relation Setting": "", "Relationship": "关系", "Relationship Basic Info": "", "Relationship Code": "", "Relationship List": "关系列表", "Relationship Management": "关系管理", "Relationship Name": "", "Relationship No.": "", "Relationship of Agency & Channel": "", "Relationship of Insurance Company & Agency": "", "Relationship of Insurance Company & Service Company": "", "Relationship Setting": "", "Relationship With Employee": "与雇员的关系", "Relative": "亲属", "Relative Name": "亲属名字", "Remark": "备注", "Repeat Every": "", "Resign Date": "辞职日期", "Response Message": "", "Result": "结果", "Retail": "销售总部", "Saleable Insurance Product Type": "可以代理的保险种类", "Sales Agreement": "销售协议", "Sales Agreement Code": "销售协议编码", "Sales Agreement List": "销售协议清单", "Sales Agreement Name": "销售协议名称", "Sales Agreement Name/Code": "销售协议名称/代码", "Sales Agreement Signing Party": "销售协议签署方", "Sales Agreement Status": "销售协议状态", "Sales Agreement(After Change)": "变更后销售协议", "Sales Agreement(Before Change)": "变更前销售协议", "Sales Authority Information": "销售权限信息", "Sales Channel": "销售渠道", "Sales Channel Code": "销售渠道代码", "Sales Channel Information": "销售渠道信息", "Sales Channel Management": "", "Sales Channel Name": "销售渠道名称", "Sales Channel Name/Code": "销售渠道名称/代码", "Sales Channel Type": "销售渠道类型", "Sales Channel(After Change)": "销售渠道（变更后）", "Sales Channel(Before Change)": "销售渠道（变更前）", "Sales Hierarchy": "销售层级", "Sales License Type": "销售执照类型", "Sales Platform": "销售平台", "Sales Related Information": "销售相关信息", "Same Level": "", "Save": "保存", "Save Success": "", "Save successfully!": "保存成功", "Search": "检索", "Search by name": "根据名字搜索", "Search Criteria {{n}}": "搜索标准 ({{n}})", "Select All": "", "Select Employee": "选择雇员", "Select Employee or Channel Staff": "选择员工或渠道人员", "Select Reconciliation": "", "Selected Reconciliation Code": "", "Self Agent": "自主代理", "Self-agency": "", "Self-channel": "", "Service Agreement": "服务协议", "Service Agreement Code": "服务协议代码", "Service Agreement information has been updated. Please submit a new service agreement code.": "服务协议信息已更新，请提交新的服务协议代码。", "Service Agreement Name": "服务协议名称", "Service Agreement Status": "服务协议状态", "Service Company": "服务机构", "Service Company Code": "服务机构代码", "Service Company Name": "服务机构名称", "Service Related Information": "服务协议相关信息", "Setting Code": "", "settled": "", "settlement": "结算", "Settlement": "", "Settlement Configuration": "", "Settlement Currency": "", "Settlement File Format": "", "Settlement Frequency": "", "Settlement Method": "", "Settlement Method Category": "", "Settlement Only": "", "Settlement Option": "", "Settlement Period": "结算区间", "Settlement Recurrence": "", "Settlement Recurrence Date": "", "Settlement Rule": "结算规则", "Settlement Scope": "", "Settlement Sources": "", "Settlement Time Type": "", "Settlement Time Zone": "结算时区", "Settlement Type": "结算类型", "Short Institute Name": "机构简称", "Size": "", "Sorry, the transfer can't be conducted, because failed to find the same  Agent Type, Sales Channel Code or Sales Agreement Code on the chosen policies, please check again.": "抱歉，由于未能在选定的保单中找到相同的代理人类型、销售渠道代码或销售协议代码，无法执行保单转移，请检查。", "Source Data: data that do not need reconciliation.": "", "SSO Account": "SSO帐号", "SST Registration Number": "", "Staff": "员工", "Staff Code": "员工代码", "Staff ID": "员工编码", "Staff Name": "员工姓名", "StaffId is required": "", "Start from": "开始", "Status": "状态", "Structure Details": "结构详情", "Sub Bill Item": "子账单项", "Sub Level": "", "Sub-level Usage Permission": "子级使用权限", "Submit": "提交", "Surgery": "手术", "SWIFT Code": "银行国际代码", "System will delete the hospital and the doctors, experienced fee under the hospital. Confirm to delete?": "将会删除机构下的医生和费用，确定删除吗？", "System will delete the investigator. Confirm to delete?": "", "t - 1": "t - 1", "Task Creation Date": "", "Task Status": "", "Taxpayer ID number": "纳税人识别号", "Team Code": "团队代码", "Team Information": "{{team}}信息", "Team Manager": "{{team}}经理", "Team Manager Information": "{{team}}经理信息", "Team manager is existing, you can’t add more team manager": "{{team}}经理已存在，不能新增{{team}}经理。", "Team Name": "团队名", "Team-Code": "{{team}}代码", "Team-Name": "{{team}}名称", "Telephone": "电话", "Terminated": "终止", "The maximum number of exports is {{max}}": "", "The modification has been submitted for review.": "信息的修改已提交审核。", "The new added address information cannot be empty": "新增地址信息不能为空。", "The new added Contact Person Information cannot be empty": "新增联系人信息不能为空。", "The policy has been transferred successfully.": "保单已经成功转移。", "The same partner already exists. If you click \"Save\", the new information will overwrite the old information, please confirm.": "已存在相同的渠道，如果点击“Save”，则新的信息会覆盖旧的信息，请确认。", "The same partner already exists. If you click Save\"": "", "The same partner already exists. If you click Save\",The same partner already exists. If you click Save\"\"": "", "The upload file size is too large. Upload failed.": "上传文件过大，上传失败。", "The vacation end time needs to be after the current time.": "休假结束时间需要在当前时间之后。", "There are still modification tasks pending approval. Please complete the approval first.": "仍有修改任务在等待审核，请先完成审核。", "There is a duplicate department name in the newly added information, please confirm.Please": "", "There is a duplicate department name in the uploaded file. Upload fails.": "上传的文件中存在重复的科室名，上传失败。", "There is still required information that has not been saved, please fill in all required information first!": "仍有必填信息还未保存，请先填写完所有必填信息！", "Third-party assessment company user name": "第三方定损公司用户名", "This flow will run": "", "Tied Agent": "内部代理人", "TIN": "", "Tips": "提示", "Title": "标题", "to": "到", "To config the columns that displayed in downloaded reconciliation result file.": "", "To config the reconciliation fields, only when all selected fields were matched, the reconciled record is marked as Matched.": "", "To config the unique key to directing one same business transaction within provided reconciliation file and local data source.": "", "Today": "今日", "Total upload number": "总上传数量: {{num}}", "Total:": "总共：{{total}} 个数据", "Transaction Business Type": "", "Transaction Type": "", "Transfer": "", "Unique Combination Fields": "", "Unknown error, Please check your file or retry later": "未知错误, 请检查文件或稍后再试", "Upload": "上传", "Upload Date": "上传日期", "Upload failed information": "上传失败信息：", "Upload File": "上传文件", "Upload History": "", "Upload Record": "上传记录", "Upload Size Limit": "", "Upload successfully number": "上传成功数量: {{num}}", "Uploaded failed": "上传失败", "uploaded file": "", "Uploaded successfully": "上传成功", "Uploading files will overwrite historical data, please confirm whether it needs to be modified.": "上传文件会覆盖历史数据，请确认是否需要修改。", "Vacation Date": "休假日期", "VAT Number": "VAT税号", "View": "观光", "View Account Information Settings": "查看账户信息设置", "View Address": "查看地址", "View Agent": "查看代理人", "View Branch": "查看销售分支", "View Branch Manager": "查看销售分支经理", "View Contact Person Information": "查看联系人信息", "View Department Information": "查看部门信息", "View Doctor information": "查看医生信息", "View Employee Information": "查看雇员信息", "View Headquarter Information": "查看总部信息", "View Manager": "查看经理", "View Relative Information": "查看亲属信息", "View Sales Agreement": "查看销售协议", "View Service Agreement": "查看服务协议", "View Staff information": "查看员工信息", "View Structure": "查看结构", "View Team": "查看{{team}}", "View Team Manager": "查看{{team}}经理", "Warning": "提醒", "Website": "网站", "week": "", "Weekly": "", "weeks": "", "Whether Base on Sales Channel": "", "Whether including all historical data": "", "Whether Settlement": "", "Whether to create {{name}} account?": "是否需要创建{{name}}账号？", "Whether to generate debit note details list?": "是否生成结算单明细清单？", "Whether to Include 2nd level Channel": "是否包含二级渠道", "Work Schedule Management": "值班表管理", "Working Status": "工作状态", "Working Type": "工作类型", "Working Type Detail": "工作类型明细", "Workload Rate": "工作负荷率", "Yes": "是", "You can only upload  CSV/EXCEL/JPG/WORD/PDF": "您可以上传的文件格式为： CSV/EXCEL/JPG/WORD/PDF", "You can only upload PDF/DOC/XLS/PNG/JPG": "您可以上传的文件格式为： PDF/DOC/XLS/PNG/JPG", "You can only upload PNG/JPG": "", "You do not have permission to process this task.": "你没有权限处理这个任务。", "You need to select specific agent, please check.": "您需要选择具体的代理人，请检查。 ", "You should choose a policy first!": "您需要先选择一份保单！", "Your file has been submitted. Please wait patiently for the result.": "您的文件已提交，请耐心等待处理结果。", "Zip Code": "子账单项"}