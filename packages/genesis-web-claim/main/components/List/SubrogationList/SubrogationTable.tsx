// package
import { FC, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

import { TableRowSelection } from 'antd/lib/table/interface';

import { RenderMode, Table, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import { SpecialTips } from '@claim/components/business-components/SpecialTips';
import { SubrogationCaseStatus } from '@claim/components/business-components/SubrogationCaseStatus';
import {
  PageMode,
  SubrogationStatus as SubrogationStatusEnum,
} from '@claim/enums/phase2';
import { useRenderDateTime } from '@claim/hooks/format';
import { SubrogationTask } from '@claim/services';
import { ColumnTypeExt, TaskPoolType } from '@claim/types/common';

interface SubrogationListProps {
  loading: boolean;
  pageMode?: PageMode;
  dataSource?: SubrogationTask[];
  rowSelection?: TableRowSelection<SubrogationTask>;
  renderActions?: (task: SubrogationTask) => ReactElement;
}

// task pool / watch list 多处使用，action 业务逻辑不通
export const SubrogationTable: FC<SubrogationListProps> = ({
  loading,
  dataSource,
  rowSelection,
  pageMode,
  renderActions,
}) => {
  /* =========== References Begin ========== */
  const { t } = useTranslation();
  const renderDateTime = useRenderDateTime();

  const columns = (
    [
      {
        title: t('Subrogation Task No.'),
        dataIndex: 'subrogationNo',
        fixed: 'left',
        render: (subrogationNo: string, record) => (
          <>
            {subrogationNo}
            <SpecialTips
              specialTips={record?.specialTips}
              taskType={TaskPoolType.Self} // task operation和watch list都需要展示，故写死一个能展示的taskType
              listType={RenderMode.Table}
            />
          </>
        ),
      },
      {
        title: t('Debtor Name'),
        dataIndex: 'fullNameList',
        render: (nameList: string[]) => {
          const text = nameList?.join('; ');
          return text ? (
            <TextEllipsisDetect text={text} maxWidth={240} />
          ) : null;
        },
      },
      {
        title: t('Claim Case No.'),
        dataIndex: 'caseNo',
      },
      {
        title: t('Subrogation Task Status'),
        dataIndex: 'status',
        render: (status: SubrogationStatusEnum) =>
          status && <SubrogationCaseStatus status={status} needDot />,
      },
      // watch list才展示
      {
        title: t('Current Operator'),
        dataIndex: 'currentOperatorName',
        hidden: pageMode !== PageMode.WatchList,
      },
      {
        title: t('Subrogation Start Date'),
        dataIndex: 'startDate',
        render: (date: string) => renderDateTime(date),
      },
      {
        title: t('Subrogation End Date'),
        dataIndex: 'endDate',
        render: (date: string) => renderDateTime(date),
      },
      {
        title: t('Actions'),
        width: 98,
        fixed: 'right',
        render: (_, task) => renderActions(task),
        align: 'right',
        hidden: !renderActions,
      },
    ] as ColumnTypeExt<SubrogationTask>[]
  ).filter(item => !item.hidden);

  /* =========== References End ========== */

  return (
    <Table
      rowKey="subrogationNo"
      pagination={false}
      loading={loading}
      dataSource={dataSource}
      rowSelection={rowSelection}
      columns={columns}
      scroll={{
        x: 'max-content',
      }}
      emptyType="icon"
    />
  );
};
