// package
import { FC, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

import { TableRowSelection } from 'antd/lib/table/interface';

import { Table, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import { SubrogationWriteOffStatus } from '@claim/components/business-components/SubrogationWriteOffStatus';
import { SubrogationWriteOffStatus as SubrogationWriteOffStatusEnum } from '@claim/enums/phase2';
import { useCurrencyAmountRender } from '@claim/hooks/format';
import { SubrogationWriteOffTask } from '@claim/services';
import { ColumnTypeExt } from '@claim/types/common';

interface SubrogationWriteOffListProps {
  loading: boolean;
  dataSource?: SubrogationWriteOffTask[];
  rowSelection?: TableRowSelection<SubrogationWriteOffTask>;
  renderActions?: (task: SubrogationWriteOffTask) => ReactElement;
}

// task pool / watch list / common query 多处使用，action 业务逻辑不通
export const WriteOffTable: FC<SubrogationWriteOffListProps> = ({
  loading,
  dataSource,
  rowSelection,
  renderActions,
}) => {
  /* =========== References Begin ========== */
  const { t } = useTranslation();
  const renderAmount = useCurrencyAmountRender();

  const columns = (
    [
      {
        title: t('Subrogation Task No.'),
        dataIndex: 'subrogationNo',
        fixed: 'left',
      },
      {
        title: t('Debtor Name'),
        dataIndex: 'fullNameList',
        render: (nameList: string[]) => {
          const text = nameList?.join('; ');
          return text ? (
            <TextEllipsisDetect text={text} maxWidth={240} />
          ) : null;
        },
      },
      {
        title: t('Claim Case No.'),
        dataIndex: 'caseNo',
      },
      {
        title: t('Write Off Task Status'),
        dataIndex: 'status',
        render: (status: SubrogationWriteOffStatusEnum) =>
          status && <SubrogationWriteOffStatus status={status} needDot />,
      },
      {
        title: t('Current Write Off Amount'),
        dataIndex: 'writeOffAmount',
        align: 'right',
        render: (_, { writeOffAmount, currency }) =>
          renderAmount(currency, writeOffAmount),
      },
      {
        title: t('Current Operator'),
        dataIndex: 'operatorName',
      },
      {
        title: t('Actions'),
        width: 98,
        fixed: 'right',
        render: (_, task) => renderActions(task),
        align: 'right',
        hidden: !renderActions,
      },
    ] as ColumnTypeExt<SubrogationWriteOffTask>[]
  ).filter(item => !item.hidden);

  /* =========== References End ========== */

  return (
    <Table
      rowKey="id"
      pagination={false}
      loading={loading}
      dataSource={dataSource}
      rowSelection={rowSelection}
      columns={columns}
      scroll={{
        x: 'max-content',
      }}
      emptyType="icon"
    />
  );
};
