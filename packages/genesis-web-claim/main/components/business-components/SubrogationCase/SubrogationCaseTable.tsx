import React, { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Space, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';

import { flatMap } from 'lodash-es';

import {
  EditAction,
  TableActionsContainer,
  TextEllipsisDetect,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { useWorkspace } from '@claim/components/Workspace';
import { SubrogationCaseStatus } from '@claim/components/business-components/SubrogationCaseStatus';
import { SubrogationStatus } from '@claim/enums/phase2';
import { useRenderDateTime } from '@claim/hooks/format';
import { useLoginUser } from '@claim/hooks/login-user';
import {
  SubrogationCaseInfo,
  SubrogationDebtors,
} from '@claim/services/subrogation/interface';

/**
 * @description Subrogation case table
 */
export const SubrogationCaseTable: FC<{
  data: SubrogationCaseInfo[];
  loading?: boolean;
  onEdit?: (record: SubrogationCaseInfo) => void;
  onView?: (record: SubrogationCaseInfo) => void;
  canEdit?: boolean;
}> = ({ data, loading, onEdit, onView, canEdit }) => {
  const { t } = useTranslation();
  const renderDateTime = useRenderDateTime();
  const { readonly } = useWorkspace();
  const { data: loginUser } = useLoginUser();

  const columns: ColumnsType<SubrogationCaseInfo> = useMemo(
    () => [
      {
        title: t('Subrogation Task No.'),
        dataIndex: 'subrogationNo',
        fixed: 'left',
      },
      {
        title: t('Debtor Name'),
        dataIndex: 'debtorDetails',
        render: (debtors: SubrogationDebtors[]) => {
          const debtorsNames = flatMap(debtors, item => {
            return item?.customer?.fullName || item?.customer?.organizationName;
          });
          const text = debtorsNames.join('; ');

          return text ? (
            <TextEllipsisDetect text={text} maxWidth={180} />
          ) : null;
        },
      },
      {
        title: t('Subrogation Task Status'),
        dataIndex: 'status',
        width: 240,
        render: (text: SubrogationStatus) =>
          text && (
            <Space>
              <SubrogationCaseStatus status={text} needDot={true} />
            </Space>
          ),
      },
      {
        title: t('Current Operator'),
        dataIndex: 'currentOperatorName',
      },
      {
        title: t('Subrogation Start Date'),
        dataIndex: 'startDate',
        render: (text: string) => renderDateTime(text),
      },
      {
        title: t('Subrogation End Date'),
        dataIndex: 'endDate',
        render: (text: string) => renderDateTime(text),
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'center',
        render: record => {
          // 以下情况同时满足可编辑 1.具有编辑权限 2.subrogation case非closed状态 3.subrogation case若为Draft则不看currentOperator，否则只有currentOperator为本人是才可编辑
          const editAble =
            canEdit &&
            record?.status !== SubrogationStatus.Closed &&
            (record?.status === SubrogationStatus.Draft ||
              loginUser?.id === record?.currentOperatorId);
          return (
            <TableActionsContainer>
              <ViewAction onClick={() => onView(record)} />
              <EditAction disabled={!editAble} onClick={() => onEdit(record)} />
            </TableActionsContainer>
          );
        },
      },
    ],
    [t, readonly, renderDateTime, canEdit, loginUser?.id, onView, onEdit]
  );

  return (
    <Table
      rowKey="subrogationNo"
      pagination={false}
      loading={loading || false}
      dataSource={data}
      columns={columns}
      scroll={{ x: 'max-content' }}
    />
  );
};
