import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { TextEllipsisDetect } from '@zhongan/nagrand-ui';

import { useCurrencyAmountRender } from '@claim/hooks/format';
import { useWriteOffApproval } from '@claim/store';

import styles from './style.module.scss';

/**
 *
 * @description write off approval summary信息
 */
export const Summary = () => {
  const { t } = useTranslation();
  const { writeOffDetail } = useWriteOffApproval();
  const text = writeOffDetail?.fullNameList?.join('; ');

  const formatCurrencyAmount = useCurrencyAmountRender();

  const summaryList = useMemo(
    () => [
      {
        label: t('Debtor'),
        content: text ? (
          <TextEllipsisDetect text={text} style={{ maxWidth: '80%' }} />
        ) : null,
      },
      {
        label: t('Written Off Amount'),
        content: formatCurrencyAmount(
          writeOffDetail?.currency,
          writeOffDetail?.writtenOffAmount
        ),
      },
      {
        label: t('Current Write Off Amount'),
        content: formatCurrencyAmount(
          writeOffDetail?.currency,
          writeOffDetail?.currentWriteOffAmount
        ),
      },
    ],
    [writeOffDetail]
  );

  return (
    <div className={styles.summaryContainer}>
      {summaryList?.map(summaryItem => (
        <div className={styles.summaryItem} key={summaryItem.label}>
          <div className={styles.label}>{summaryItem.label}</div>
          <div className={styles.content}>{summaryItem.content}</div>
        </div>
      ))}
    </div>
  );
};
