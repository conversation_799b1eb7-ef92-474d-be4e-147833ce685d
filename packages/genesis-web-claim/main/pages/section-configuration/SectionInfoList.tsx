import { useCallback, useMemo, useState } from 'react';

import { message } from 'antd';
import { ColumnsType } from 'antd/lib/table';

import useSWR from 'swr';

import { Table, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import {
  SectionConfigurationItem,
  SectionDiffsConfigurationItem,
} from '@claim/services/claim-case.interface';
import { useClaimCaseService } from '@claim/services/inversify.config';

import styles from './index.module.scss';

export const SectionInfoList = ({ activeTab }) => {
  const swrKey = `/api/claim/v2/page/${activeTab}/section-differences`;
  const { data: sectionInfoList, isValidating } = useSWR(swrKey, () =>
    claimCaseService.getSectionDiffsConfiguration({ stage: activeTab })
  );

  const [loading, setLoading] = useState(false);
  const [childataSource, setChildataSource] = useState([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  const claimCaseService = useClaimCaseService();

  // table 单元格合并
  const tableShowRenders = (currentData: SectionConfigurationItem) => {
    const tableRenders = currentData?.sections?.reduce(
      (
        allRenders: SectionConfigurationItem[],
        colRender: SectionConfigurationItem
      ) =>
        allRenders.concat(
          {
            ...colRender,
            ...colRender.sectionAttribute,
            rowSpan: 2,
          },
          {
            ...colRender,
            ...colRender.tenantSectionAttribute,
            rowSpan: 0,
          }
        ),
      []
    );
    return tableRenders;
  };

  const dataSource = useMemo(() => {
    return tableShowRenders(sectionInfoList);
  }, [sectionInfoList]);

  const renderTable = () => {
    const columns: ColumnsType<SectionDiffsConfigurationItem> = [
      {
        title: 'Section',
        dataIndex: 'section',
        onCell: record => ({ rowSpan: record.rowSpan ?? 1 }),
      },
      {
        title: 'Section',
        dataIndex: 'section',
      },
      {
        title: 'conditions',
        dataIndex: 'conditions',
      },
      {
        title: 'Product Category',
        dataIndex: 'productCategories',
        render: (nameList: string[]) => {
          const text = nameList?.join('; ');
          return text ? (
            <TextEllipsisDetect text={text} maxWidth={240} />
          ) : null;
        },
      },
      {
        title: 'Actions',
        align: 'center',
        dataIndex: 'actions',
        render: (nameList: string[]) => {
          const text = nameList?.join('; ');
          return text ? (
            <TextEllipsisDetect text={text} maxWidth={240} />
          ) : null;
        },
      },
    ];

    const expandedRowRender = useCallback<
      (record: SectionDiffsConfigurationItem) => React.ReactNode
    >(
      () => (
        <Table
          className={styles.innerTableContainer}
          columns={columns}
          dataSource={childataSource}
          bordered
          loading={loading}
          pagination={false}
          rowClassName={getRowClassName}
        />
      ),
      [columns]
    );

    const onExpand = useCallback<
      (expanded: boolean, record: SectionDiffsConfigurationItem) => void
    >(
      (expanded, record) => {
        setLoading(true);
        const expandedKeys = [...expandedRowKeys];
        if (expanded) {
          expandedKeys.push(record.section);
        } else {
          expandedKeys.splice(expandedKeys.indexOf(record.section), 1);
        }
        setExpandedRowKeys(expandedKeys);
        claimCaseService
          .getSectionDiffsConfiguration({
            stage: activeTab,
            parentSection: record?.section,
          })
          .then(res => {
            setChildataSource(tableShowRenders(res));
          })
          .catch(error => message.error(error?.message ?? 'request failed'))
          .finally(() => setLoading(false));
      },
      [activeTab]
    );

    const getRowClassName = (
      record: SectionDiffsConfigurationItem,
      index: number
    ) => {
      if (index % 2 === 0) {
        return 'bg-[#f0f0f0]';
      }
    };

    return (
      <Table
        columns={columns}
        dataSource={dataSource}
        bordered
        loading={isValidating}
        pagination={false}
        rowClassName={getRowClassName}
        rowKey={record => record.section}
        expandable={{
          expandedRowRender,
          onExpand,
          expandedRowKeys: expandedRowKeys,
          rowExpandable: record =>
            record?.hasChildSections && record.rowSpan === 0,
        }}
      />
    );
  };
  return <div>{renderTable()}</div>;
};
