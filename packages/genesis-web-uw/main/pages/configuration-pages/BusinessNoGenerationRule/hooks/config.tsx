import {
  ReactElement,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Trans } from 'react-i18next';

import {
  Button,
  Cascader,
  Col,
  Form,
  FormInstance,
  Modal,
  Row,
  Select,
  Space,
  Tooltip,
  Upload,
  UploadProps,
} from 'antd';
import { ColumnProps } from 'antd/es/table';

import { MathJax, MathJaxContext } from 'better-react-mathjax';
import { uniq } from 'lodash-es';

import { Icon, Input, StatusTag, TextBody, message } from '@zhongan/nagrand-ui';

import { FileItem } from 'genesis-web-component/lib/components';
import { TextLink } from 'genesis-web-component/lib/components/TextLink';
import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';
import {
  BusinessNumberConfiguration,
  BusinessNumberConfigurationService,
} from 'genesis-web-service';
import { MetadataService } from 'genesis-web-service/lib/metadata';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import LinkSvg from '@uw/assets/new-icons/link.svg';
import { useBizDict } from '@uw/hook/useBizDict';
import { useDict } from '@uw/hook/useDict';
import { isCombinationStrategy } from '@uw/pages/configuration-pages/BusinessNoGenerationRule/components/RuleEditDrawer';
import { getFields } from '@uw/util/getFieldsQueryForm';
import { i18nFn } from '@uw/util/i18nFn';
import { messagePopup } from '@uw/util/messagePopup';
import { transferBizDictToOption } from '@uw/util/transferBizDictToOption';

import styles from '../BusinessNoGenerationRule.module.scss';
import { RuleAccumulation, RuleStrategy } from '../interface';

const handleDownloadTemplate = async (
  biznoRuleTypeMap: Record<string, string>,
  ruleType: string,
  factorList: string[]
) => {
  if (!factorList?.[0]) {
    messagePopup(i18nFn('Please select factor first'), 'warn');
    return;
  }
  try {
    const fileName =
      biznoRuleTypeMap?.[ruleType].replace(/\s*/g, '').split('.')[0] ?? 'file';
    const res = await BusinessNumberConfigurationService.queryTemplate(
      fileName,
      factorList
    );
    downloadFile(res);
  } catch (e) {
    messagePopup((e as Error)?.toString(), 'error');
  }
};

export const useRuleFields = (
  inDrawer: boolean,
  disabled: boolean,
  maxtrixFileMap:
    | Record<
        string,
        Omit<
          BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue,
          'factorList'
        >
      >
    | undefined,
  form?: FormInstance,
  checkDigitStrategy?: any[]
) => {
  const combinationSequenceEnums = useBizDict('combinationSequenceType');
  const accumulationRuleEnums = useBizDict(
    'biznoDynamicRuleSequenceAccumulationRule'
  );
  const [accumulationRuleMap] = useDict(
    'biznoDynamicRuleSequenceAccumulationRule'
  );
  const [reaccumulatePeriodMap] = useDict('biznoDynamicRuleReaccumulatePeriod');
  const bizNoDynamicRuleTimeStampFormatEnums = useBizDict(
    'bizNoDynamicRuleTimeStampFormat'
  );
  const bizNoDynamicRuleFactorEnums = useBizDict('bizNoDynamicRuleFactor');
  const [bizNoDynamicRuleFactorEnum] = useDict('bizNoDynamicRuleFactor');
  const biznoDynamicRuleReaccumulatePeriodEnums = useBizDict(
    'biznoDynamicRuleReaccumulatePeriod'
  );
  const [biznoRuleTypeMap] = useDict('biznoRuleType');
  const previousPolicyOperationEnums = useBizDict('previousPolicyOperation');

  const uploadRef = useRef<HTMLElement>();
  const [fileMap, setFileMap] =
    useState<
      Record<
        string,
        Omit<
          BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue,
          'factorList'
        >
      >
    >();

  const [businessValue, setBusinessValue] = useState<Record<string, boolean>>(
    {}
  );

  const accumulateOptions = useMemo(
    () =>
      transferBizDictToOption(
        accumulationRuleEnums?.map(bizDict => ({
          ...bizDict,
          childList:
            bizDict.enumItemName ===
            RuleAccumulation.RE_ACCUMULATE_BY_TIME_STAMP
              ? biznoDynamicRuleReaccumulatePeriodEnums
              : [],
        })) ?? []
      ),
    [accumulationRuleEnums, biznoDynamicRuleReaccumulatePeriodEnums]
  );

  const uploadProps = useCallback<
    (ruleTypeNo: string, formItemIndex: string) => UploadProps
  >(
    (ruleTypeNo, formItemIndex) => ({
      name: 'attachmentType',
      accept: '.xls, .xlsx',
      multiple: false,
      showUploadList: false,
      disabled: false,
      customRequest: async cb => {
        const formData = new FormData();
        formData.append('file', cb.file);
        try {
          const res = await BusinessNumberConfigurationService.uploadFile(
            ruleTypeNo,
            formData
          );
          const fileInfo = {
            fileName: res?.attachmentName,
            fileUniqueCode: res?.fileUniqueCode,
            fileType: 'xlsx',
          };
          setFileMap({
            ...fileMap,
            [formItemIndex]: fileInfo,
          });
          form?.setFieldValue([formItemIndex, 'fileUpload'], fileInfo);
        } catch (e) {
          messagePopup((e as Error).toString(), 'error');
        }
      },
    }),
    [fileMap, form]
  );

  useEffect(() => {
    setFileMap(maxtrixFileMap);
  }, [maxtrixFileMap]);

  const editSequenceValue = useCallback(
    (namePath: Array<string | number>) => {
      let combinationValues = form?.getFieldValue(namePath)?.join('\n') ?? '';
      return Modal.confirm({
        width: 572,
        title: i18nFn('Sequence Value'),
        className: '[&_.anticon-exclamation-circle]:hidden',
        content: (
          <div key={namePath[0]}>
            <span className="absolute z-[2] left-[12px] top-[24px] text-placeholder">
              {i18nFn(
                'Press enter to record enums, duplicated keys will be ignored.'
              )}
            </span>
            <Input.TextArea
              rows={18}
              style={{ paddingTop: 32 }}
              defaultValue={combinationValues}
              onChange={event => {
                combinationValues = event.target.value;
              }}
            />
          </div>
        ),
        onOk() {
          const value = uniq(combinationValues.split('\n').filter(Boolean));
          form?.setFieldValue(namePath, value);
        },
      });
    },
    [form]
  );

  const getRuleType = useCallback<
    (
      values: BusinessNumberConfiguration.RuleDetailListType,
      ruleTypeNo?: string,
      data?: BusinessNumberConfiguration.RuleDetailListType[]
    ) => JSX.Element | string
  >(
    (values, ruleTypeNo, data) => {
      const { fileName } =
        values as BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue;
      const configSchema = {
        label: '',
        placeholder: i18nFn('Please input'),
        type: FieldType.Input,
        allowClear: true,
        col: 12,
        required: true,
        rules: [
          {
            required: true,
            message: i18nFn('Please input'),
          },
        ],
      };
      const { elementStrategy } = values;
      const formIndexName = `${elementStrategy}-${values.sortNumber}`;
      // 此处因子需要筛选，筛选出common共用的因子以及该ruleType所特有的因子
      const curRuleFactorEnums = bizNoDynamicRuleFactorEnums?.filter(factor => {
        // itemExtend2字段是正向过滤逻辑，COMMON表示全部
        const includeArr = factor.itemExtend2?.split(',') ?? [];
        // itemExtend3是排除逻辑，如果extend2是COMMON，则需要用到extend3字段，排除一些选项
        const excludeArr = factor.itemExtend3?.split(',') ?? [];
        if (includeArr.includes('COMMON')) {
          return !excludeArr.includes(ruleTypeNo?.toString() || '');
        }
        return includeArr.includes(ruleTypeNo?.toString() || '');
      });
      const file = fileMap?.[formIndexName];

      switch (elementStrategy) {
        case RuleStrategy.FIXED_VALUE:
          return inDrawer ? (
            <Row>
              <Col span={configSchema.col}>
                <Form.Item
                  className="mr-[8px]"
                  name={[formIndexName, 'fixedValue']}
                >
                  {getFields({
                    ...configSchema,
                    key: 'fixedValue',
                    placeholder: i18nFn('Please input'),
                  })}
                </Form.Item>
              </Col>
            </Row>
          ) : (
            (values as BusinessNumberConfiguration.FixedValueRuleValue)
              .fixedValue
          );
        case RuleStrategy.CHECK_DIGIT:
          return inDrawer ? (
            <>
              <Row className="flex-nowrap w-[calc(100%-22px)]">
                <Col span={12}>
                  <Form.Item name={[formIndexName, 'digitLength']}>
                    {getFields({
                      ...configSchema,
                      key: 'digitLength',
                      type: FieldType.InputNumber,
                      addonBefore: i18nFn('Digit Length'),
                    })}
                  </Form.Item>
                </Col>
                <span className="my-0 mx-[8px] leading-[30px]">-</span>
                <Col span={12}>
                  <Space.Compact block>
                    <Form.Item name={[formIndexName, 'checkDigitStrategy']}>
                      {getFields({
                        ...configSchema,
                        key: 'checkDigitStrategy',
                        type: FieldType.Select,
                        options: checkDigitStrategy,
                        style: { width: 171 },
                      })}
                    </Form.Item>
                    <Form.Item name={[formIndexName, 'modulo']}>
                      {getFields({
                        ...configSchema,
                        key: 'modulo',
                        type: FieldType.InputNumber,
                      })}
                    </Form.Item>
                  </Space.Compact>
                </Col>
              </Row>
              <Row className="flex-nowrap w-[calc(100%-20px)] mt-[8px]">
                <Col span={12}>
                  <Space.Compact block>
                    <Form.Item
                      name={[formIndexName, 'checkSegmentStart']}
                      dependencies={[[formIndexName, 'checkSegmentEnd']]}
                      rules={[
                        ({ getFieldValue }) => ({
                          validator() {
                            const startValue = getFieldValue([
                              formIndexName,
                              'checkSegmentStart',
                            ]);
                            const endValue = getFieldValue([
                              formIndexName,
                              'checkSegmentEnd',
                            ]);
                            if (startValue === 0 || endValue === 0) {
                              return Promise.reject(
                                new Error('Zero is invalid.')
                              );
                            }
                            if (!startValue && !endValue) {
                              return Promise.resolve();
                            }
                            if (!startValue || !endValue) {
                              return Promise.reject(
                                new Error(
                                  'The start and end values should be filled or empty at the same time.'
                                )
                              );
                            }
                            if (startValue && endValue) {
                              if (startValue < 1) {
                                return Promise.reject(
                                  new Error('The minimum is 1')
                                );
                              }
                              if (endValue <= startValue) {
                                return Promise.reject(
                                  new Error(
                                    'The end should be bigger than the start.'
                                  )
                                );
                              }
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      {getFields({
                        ...configSchema,
                        controls: false,
                        type: FieldType.InputNumber,
                        placeholder: i18nFn('Start'),
                        addonBefore: i18nFn('Check Segment'),
                        className: styles.inputRangeLeft,
                        style: { width: 232 },
                      })}
                    </Form.Item>
                    <Form.Item name={[formIndexName, 'checkSegmentEnd']}>
                      {getFields({
                        ...configSchema,
                        controls: false,
                        addonBefore: '-',
                        placeholder: i18nFn('End'),
                        type: FieldType.InputNumber,
                        className: styles.inputRangeRight,
                      })}
                    </Form.Item>
                  </Space.Compact>
                </Col>
              </Row>
            </>
          ) : (
            <span>
              {i18nFn('Length: {{length}}', undefined, {
                length: (
                  values as BusinessNumberConfiguration.CheckDigitsValue
                )?.digitLength?.toString(),
              })}
            </span>
          );
        case RuleStrategy.COMBINATION_VALUES:
          return inDrawer ? (
            <Row className="flex-nowrap w-full">
              <Col span={24}>
                <Space.Compact block>
                  <div
                    className={`leading-[30px] inline-table whitespace-nowrap border border-solid border-[var(--divider-color)] rounded-[8px_0_0_8px] ${disabled ? 'text-[#546e92] bg-[rgba(0,0,0,0.04)]' : 'text-[#102a43] bg-[#F5F7FA]'} px-[11px] py-0 font-[400] border-r-0`}
                  >
                    {i18nFn('Sequence Value')}
                  </div>
                  <Form.Item
                    name={[formIndexName, 'combinationValues']}
                    className="w-full [&_.antd-uw-select-selector]:!bg-white [&_.antd-uw-select-selector]:!cursor-text"
                  >
                    <Select
                      disabled
                      mode="tags"
                      open={false}
                      removeIcon={null}
                      placeholder={i18nFn('Please input')}
                      onClick={() => {
                        editSequenceValue([formIndexName, 'combinationValues']);
                      }}
                      tagRender={({ label }) => (
                        <StatusTag
                          statusI18n={label as ReactElement}
                          style={{
                            minWidth: 24,
                            marginRight: 8,
                            borderRadius: 4,
                          }}
                        />
                      )}
                    />
                  </Form.Item>
                </Space.Compact>
              </Col>
            </Row>
          ) : (
            <span>
              {i18nFn('Length: {{length}}', undefined, {
                length: (
                  values as BusinessNumberConfiguration.CombinationRuleValue
                )?.combinationValues?.length.toString(),
              })}
            </span>
          );
        case RuleStrategy.COMBINATION_SEQUENCE:
          return inDrawer ? (
            <>
              <Row className="flex-nowrap w-[calc(100%-20px)]">
                <Col span={12}>
                  <Form.Item name={[formIndexName, 'length']}>
                    {getFields({
                      ...configSchema,
                      key: 'length',
                      type: FieldType.InputNumber,
                      addonBefore: i18nFn('Sequence Length'),
                    })}
                  </Form.Item>
                </Col>
                <span className="mx-[8px] my-0 leading-[30px]">;</span>
                <Col span={12}>
                  <Form.Item name={[formIndexName, 'initialNumber']}>
                    {getFields({
                      ...configSchema,
                      key: 'initialNumber',
                      type: FieldType.InputNumber,
                      addonBefore: i18nFn('Initial Number'),
                    })}
                  </Form.Item>
                </Col>
              </Row>
              <Row className="mt-[8px] flex-nowrap w-[calc(100%-22px)]">
                <Col span={12}>
                  <Form.Item
                    name={[formIndexName, 'combinationRule']}
                    className="w-full [&_.antd-uw-select-selector]:!bg-primaryDisabled"
                  >
                    {getFields({
                      ...configSchema,
                      disabled: true,
                      showSearch: false,
                      allowClear: false,
                      type: FieldType.Select,
                      options: combinationSequenceEnums,
                      placeholder: i18nFn('Re-accumulate and affect target'),
                      rules: [
                        {
                          required: true,
                          message: i18nFn('Please select'),
                        },
                      ],
                    })}
                  </Form.Item>
                </Col>
                <span className="mx-[8px] my-0 leading-[30px]">-</span>
                <Col span={12}>
                  <Space.Compact block>
                    <div
                      className={`leading-[30px] inline-table whitespace-nowrap border border-solid border-[var(--divider-color)] rounded-[8px_0_0_8px] ${disabled ? 'text-[#546e92] bg-[rgba(0,0,0,0.04)]' : 'text-[var(--text-color)] bg-[#F5F7FA]'} px-[11px] py-0 font-[400] border-r-0`}
                    >
                      {i18nFn('Target Rule No.')}
                    </div>
                    <Form.Item
                      name={[formIndexName, 'targetRuleNo']}
                      className="w-full [&_.antd-uw-select-selector]:!bg-primaryDisabled"
                    >
                      {getFields({
                        ...configSchema,
                        disabled: true,
                        selectProps: {
                          showArrow: false,
                        },
                        type: FieldType.Select,
                        options: data?.map(({ sortNumber }) => ({
                          itemName: sortNumber,
                          enumItemName: sortNumber,
                        })),
                      })}
                    </Form.Item>
                  </Space.Compact>
                </Col>
              </Row>
            </>
          ) : (
            <span>
              {i18nFn('Length: {{length}}', undefined, {
                length: (
                  values as BusinessNumberConfiguration.SequenceRuleValue
                )?.length?.toString(),
              })}
            </span>
          );
        case RuleStrategy.SEQUENCE:
          return inDrawer ? (
            <>
              <Row className="flex-nowrap w-[calc(100%-22px)]">
                <Col span={12}>
                  <Form.Item name={[formIndexName, 'length']}>
                    {getFields({
                      ...configSchema,
                      key: 'length',
                      type: FieldType.InputNumber,
                      addonBefore: i18nFn('Sequence Length'),
                    })}
                  </Form.Item>
                </Col>
                <span className="mx-[8px] my-0 leading-[30px]">-</span>
                <Col span={12}>
                  <Space.Compact block>
                    <div
                      className={`leading-[30px] inline-table whitespace-nowrap border border-solid border-[var(--divider-color)] rounded-[8px_0_0_8px] ${disabled ? 'text-[#546e92] bg-[rgba(0,0,0,0.04)]' : 'text-[var(--text-color)] bg-[#F5F7FA]'} px-[11px] py-0 font-[400] border-r-0`}
                    >
                      {i18nFn('Pad with zeros')}
                    </div>
                    <Form.Item
                      name={[formIndexName, 'padWithZeros']}
                      className="w-full"
                    >
                      {getFields({
                        ...configSchema,
                        key: 'padWithZeros',
                        type: FieldType.Select,
                        showSearch: false,
                        allowClear: false,
                        defaultValue: true,
                        options: [
                          { itemName: i18nFn('Yes'), enumItemName: true },
                          { itemName: i18nFn('No'), enumItemName: false },
                        ],
                      })}
                    </Form.Item>
                  </Space.Compact>
                </Col>
              </Row>
              <Row className="flex-nowrap w-[calc(100%-20px)] mt-[8px]">
                <Col span={12}>
                  <Form.Item name={[formIndexName, 'initialNumber']}>
                    {getFields({
                      ...configSchema,
                      key: 'initialNumber',
                      type: FieldType.InputNumber,
                      addonBefore: i18nFn('Initial Number'),
                    })}
                  </Form.Item>
                </Col>
                <span className="mx-[8px] my-0 leading-[30px]">-</span>
                <Col span={12}>
                  <Form.Item name={[formIndexName, 'accumulationRule']}>
                    <Cascader
                      allowClear
                      options={accumulateOptions}
                      placeholder={i18nFn('Please select')}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </>
          ) : (
            <>
              <Tooltip
                title={
                  <>
                    {
                      accumulationRuleMap[
                        (
                          values as BusinessNumberConfiguration.SequenceRuleValue
                        ).accumulationRule
                      ]
                    }
                    {!!(values as BusinessNumberConfiguration.SequenceRuleValue)
                      .reaccumulatePeriod &&
                      `: ${
                        reaccumulatePeriodMap[
                          (
                            values as BusinessNumberConfiguration.SequenceRuleValue
                          ).reaccumulatePeriod
                        ]
                      }`}
                  </>
                }
              >
                <span>
                  {i18nFn('Length: {{length}}', undefined, {
                    length: (
                      values as BusinessNumberConfiguration.SequenceRuleValue
                    )?.length?.toString(),
                  })}
                </span>
              </Tooltip>
            </>
          );
        case RuleStrategy.PREVIOUS_POLICY:
          return inDrawer ? (
            <Row className="flex-nowrap w-[calc(100%-45px)]">
              <Col span={8}>
                <Form.Item
                  name={[formIndexName, 'digitPositionFrom']}
                  rules={[{ required: true, message: '' }]}
                >
                  {getFields({
                    ...configSchema,
                    key: 'length',
                    type: FieldType.InputNumber,
                    addonBefore: i18nFn('Digit Position'),
                  })}
                </Form.Item>
              </Col>
              <span className="mx-[8px] my-0 leading-[30px]">-</span>
              <Col span={4}>
                <Form.Item
                  name={[formIndexName, 'digitPositionEnd']}
                  rules={[{ required: true, message: '' }]}
                >
                  {getFields({
                    ...configSchema,
                    key: 'length',
                    type: FieldType.InputNumber,
                  })}
                </Form.Item>
              </Col>
              <span className="mx-[8px] my-0 leading-[30px]">-</span>
              <Col span={12}>
                <Form.Item name={[formIndexName, 'operation']}>
                  {getFields({
                    ...configSchema,
                    showSearch: false,
                    type: FieldType.Select,
                    placeholder: i18nFn('Please select'),
                    options: previousPolicyOperationEnums,
                  })}
                </Form.Item>
              </Col>
            </Row>
          ) : (
            <span>
              {`Position: ${
                (values as BusinessNumberConfiguration.PreviousPolicyRule)
                  .digitPositionFrom
              } - ${
                (values as BusinessNumberConfiguration.PreviousPolicyRule)
                  .digitPositionEnd
              }`}
            </span>
          );
        case RuleStrategy.TIME_STAMP:
          return inDrawer ? (
            <Row>
              <Col span={configSchema.col}>
                <Form.Item
                  className="mr-[8px]"
                  name={[formIndexName, 'format']}
                >
                  {getFields({
                    ...configSchema,
                    key: 'format',
                    type: FieldType.Select,
                    placeholder: i18nFn('Please select'),
                    options: bizNoDynamicRuleTimeStampFormatEnums,
                    labelProps: 'dictValue',
                    rules: [
                      {
                        required: true,
                        message: i18nFn('Please select'),
                      },
                    ],
                  })}
                </Form.Item>
              </Col>
            </Row>
          ) : (
            <>
              <span>
                {
                  (values as BusinessNumberConfiguration.TimeStampRuleValue)
                    .format
                }
              </span>
            </>
          );
        case RuleStrategy.BUSINESS_ELEMENTS:
          return inDrawer ? (
            <>
              <Form.Item name={[formIndexName, 'factorCode']}>
                {getFields({
                  ...configSchema,
                  key: 'factorList',
                  type: FieldType.Select,
                  placeholder: i18nFn('Please select'),
                  options: curRuleFactorEnums,
                  labelProps: 'dictValue',
                  onChange: (value: string) => {
                    if (value !== 'renewalCount') {
                      form?.setFieldValue(
                        [formIndexName, 'minimumLength'],
                        undefined
                      );
                    }
                    setBusinessValue(before => ({
                      ...before,
                      [formIndexName]: value === 'renewalCount',
                    }));
                  },
                  rules: [
                    {
                      required: true,
                      message: i18nFn('Please select'),
                    },
                  ],
                })}
              </Form.Item>
              {form?.getFieldValue([formIndexName, 'factorCode']) ===
                'renewalCount' || businessValue?.[formIndexName] ? (
                <Form.Item
                  name={[formIndexName, 'minimumLength']}
                  rules={[
                    {
                      required: true,
                      message: i18nFn('Please select'),
                    },
                  ]}
                >
                  <Input
                    className="mt-xs w-[300px]"
                    addonBefore={i18nFn('Minimum Length')}
                  />
                </Form.Item>
              ) : null}
            </>
          ) : (
            <span>
              {
                bizNoDynamicRuleFactorEnum?.[
                  (
                    values as BusinessNumberConfiguration.BusinessElementsRuleValue
                  ).factorCode ?? ''
                ]
              }
            </span>
          );
        case RuleStrategy.MATRIX_WITH_BUSINESS_ELEMENTS:
          return inDrawer ? (
            <>
              <Form.Item name={[formIndexName, 'factorList']}>
                {getFields({
                  ...configSchema,
                  key: 'factorList',
                  type: FieldType.MultiSelect,
                  placeholder: i18nFn('Please select'),
                  options: curRuleFactorEnums,
                  labelProps: 'dictValue',
                  rules: [
                    {
                      required: true,
                      message: i18nFn('Please select'),
                    },
                  ],
                })}
              </Form.Item>
              <TextBody type="caption" style={{ margin: `${styles.gapMd} 0` }}>
                <Trans i18nKey={'download_template'}>
                  Please{' '}
                  <TextLink
                    style={{
                      color: styles.primaryColor,
                    }}
                    onClick={() => {
                      handleDownloadTemplate?.(
                        biznoRuleTypeMap,
                        ruleTypeNo as string,
                        form?.getFieldValue(formIndexName)?.factorList
                      );
                    }}
                  >
                    Download Template
                  </TextLink>{' '}
                  First
                </Trans>
              </TextBody>
              <Form.Item
                name={[formIndexName, 'fileUpload']}
                valuePropName="file"
                getValueFromEvent={e => {
                  if (Array.isArray(e)) {
                    return e;
                  }
                  return e?.fileList;
                }}
                style={{
                  display: file ? 'none' : 'block',
                }}
              >
                <Upload {...uploadProps(ruleTypeNo as string, formIndexName)}>
                  <Button
                    ref={node => {
                      uploadRef.current = node as HTMLElement;
                    }}
                    icon={<Icon type="upload" />}
                  >
                    {i18nFn('Upload')}
                  </Button>
                </Upload>
              </Form.Item>
              {file && (
                <FileItem
                  fileName={file.fileName}
                  fileUrl={file.fileUniqueCode}
                  isShowHover={true}
                  hoverInfoList={
                    disabled
                      ? []
                      : [
                          {
                            icon: <Icon type="snyc" />,
                            key: 'reload',
                            onClick: () => {
                              uploadRef.current?.click();
                            },
                          },
                          {
                            icon: (
                              <Icon
                                type="download"
                                style={{ marginLeft: styles.gapXss }}
                              />
                            ),
                            key: 'download',
                            onClick: () => {
                              MetadataService.bizNoV2DownloadFiles({
                                ruleTypeCode: Number(ruleTypeNo),
                                sortNumber: values.sortNumber,
                                fileName: (
                                  values as BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue
                                ).fileName,
                              })
                                .then(response => {
                                  downloadFile(response).then(() => {
                                    message.success('Download successfully');
                                  });
                                })
                                .catch((error: Error) => {
                                  message.error(
                                    error.message || 'Download failed'
                                  );
                                });
                            },
                          },
                          {
                            icon: (
                              <Icon
                                type="delete"
                                style={{ marginLeft: styles.gapXss }}
                              />
                            ),
                            key: 'delete',
                            onClick: () => {
                              const fileMapTemp = { ...fileMap };
                              delete fileMapTemp?.[formIndexName];
                              setFileMap(fileMapTemp);
                              form?.resetFields([
                                [formIndexName, 'fileUpload'],
                              ]);
                            },
                          },
                        ]
                  }
                ></FileItem>
              )}
            </>
          ) : (
            <span className={styles.matrixCardContent}>
              <Icon type="paper-clip" />
              <span>{fileName}</span>
            </span>
          );
        default:
          return i18nFn('--');
      }
    },
    [
      previousPolicyOperationEnums,
      editSequenceValue,
      accumulateOptions,
      accumulationRuleMap,
      combinationSequenceEnums,
      bizNoDynamicRuleFactorEnum,
      bizNoDynamicRuleFactorEnums,
      bizNoDynamicRuleTimeStampFormatEnums,
      biznoRuleTypeMap,
      disabled,
      fileMap,
      form,
      inDrawer,
      reaccumulatePeriodMap,
      uploadProps,
      checkDigitStrategy,
      businessValue,
    ]
  );

  return { getRuleType };
};

const tableColumns = (
  enumItems: Record<string, Record<string, string>>,
  showMathJaxList: any[]
): ColumnProps<BusinessNumberConfiguration.RuleDetailListType>[] => [
  {
    title: i18nFn('No.'),
    dataIndex: 'sortNumber',
    width: 80,
    render: (text, record, index) => index + 1,
  },
  {
    title: i18nFn('Rule Type'),
    dataIndex: 'elementStrategy',
    render: (text, record) => {
      const TypeValue =
        enumItems?.biznoDynamicRuleElementStrategyEnum?.[text] ?? text;
      if (isCombinationStrategy(record.elementStrategy as RuleStrategy)) {
        return (
          <div className="gap-[4px]">
            <LinkSvg />
            {i18nFn('Combination Sequence')}
          </div>
        );
      }
      return text === RuleStrategy.CHECK_DIGIT ? (
        <>
          {TypeValue}
          <Tooltip
            arrowPointAtCenter
            overlayClassName="max-w-lg"
            placement="bottomLeft"
            title={showMathJaxList}
          >
            <Icon type="info-circle" className="ml-3" />
          </Tooltip>
          <div className="opacity-0 w-0 h-0 overflow-hidden">
            {showMathJaxList}
          </div>
        </>
      ) : (
        TypeValue
      );
    },
  },
];

export const useRuleColumns = (
  disabled: boolean,
  handleDelete: (index: number, filterConnect?: boolean) => void,
  maxtrixFileMap:
    | Record<
        string,
        Omit<
          BusinessNumberConfiguration.MatrixWithBusinessElementsRuleValue,
          'factorList'
        >
      >
    | undefined,
  ruleType?: string,
  form?: FormInstance,
  checkDigitStrategy?: any[],
  data?: BusinessNumberConfiguration.RuleDetailListType[]
): {
  columns: ColumnProps<BusinessNumberConfiguration.RuleDetailListType>[];
} => {
  const { getRuleType } = useRuleFields(
    true,
    disabled,
    maxtrixFileMap,
    form,
    checkDigitStrategy
  );
  const [
    biznoDynamicRuleElementStrategyEnum,
    bizNoDynamicRuleTimeStampFormatEnum,
    biznoDynamicRuleSequenceAccumulationRuleEnum,
    biznoDynamicRuleReaccumulatePeriodEnum,
  ] = useDict([
    'biznoDynamicRuleElementStrategy',
    'bizNoDynamicRuleTimeStampFormat',
    'biznoDynamicRuleSequenceAccumulationRule',
    'biznoDynamicRuleReaccumulatePeriod',
  ]);

  // 展示提示语的列表
  const showMathJaxList = useMemo(() => {
    const resultData = transferBizDictToOption(
      checkDigitStrategy ?? [],
      'itemExtend1',
      'itemName'
    );
    return resultData.map(item => (
      <MathJaxContext>
        <MathJax>{item.value}</MathJax>
      </MathJaxContext>
    ));
  }, [checkDigitStrategy]);

  const columns = useMemo<
    ColumnProps<BusinessNumberConfiguration.RuleDetailListType>[]
  >(
    () => [
      ...tableColumns(
        {
          biznoDynamicRuleElementStrategyEnum,
          bizNoDynamicRuleTimeStampFormatEnum,
          biznoDynamicRuleSequenceAccumulationRuleEnum,
          biznoDynamicRuleReaccumulatePeriodEnum,
        },
        showMathJaxList
      ),
      {
        title: i18nFn('Value'),
        dataIndex: 'value',
        render: (_, record) => getRuleType(record, ruleType, data),
      },
      ...(!disabled
        ? ([
            {
              title: '',
              dataIndex: '',
              width: 40,
              align: 'right',
              render: (_, record, index) => (
                <Icon
                  type="delete"
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    if (form && data?.length) {
                      const fields = form.getFieldsValue(true);
                      delete fields[
                        `${record.elementStrategy}-${record.sortNumber}`
                      ];
                      // 删除Combination时，两条一起删除
                      if (
                        isCombinationStrategy(
                          record.elementStrategy as RuleStrategy
                        )
                      ) {
                        const seqIndex = data.findIndex(
                          ({ elementStrategy }) =>
                            isCombinationStrategy(
                              elementStrategy as RuleStrategy
                            ) && elementStrategy !== record.elementStrategy
                        );
                        if (seqIndex > -1) {
                          delete fields[
                            `${data[seqIndex].elementStrategy}-${data[seqIndex].sortNumber}`
                          ];
                        }
                      }
                      form.setFieldsValue(fields);

                      const filterCombination = isCombinationStrategy(
                        record.elementStrategy as RuleStrategy
                      );
                      handleDelete(index, filterCombination);
                    }
                  }}
                />
              ),
            },
          ] as ColumnProps<BusinessNumberConfiguration.RuleDetailListType>[])
        : []),
    ],
    [
      biznoDynamicRuleElementStrategyEnum,
      bizNoDynamicRuleTimeStampFormatEnum,
      biznoDynamicRuleSequenceAccumulationRuleEnum,
      biznoDynamicRuleReaccumulatePeriodEnum,
      disabled,
      getRuleType,
      ruleType,
      form,
      handleDelete,
      showMathJaxList,
      data,
    ]
  );

  return {
    columns,
  };
};
