import { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip } from 'antd';

import BigNumber from 'bignumber.js';
import { useAtomValue } from 'jotai';
import { isNaN, toNumber } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import { isReadPrettyAtom } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';

import { DesignatedBeneficiaryProps } from './Beneficiary.interface';

export const RatioLabel: FC<{
  beneficiaryList: DesignatedBeneficiaryProps['beneficiaryList'];
}> = ({ beneficiaryList }) => {
  const { t } = useTranslation(['uw', 'common']);
  const readPretty = useAtomValue(isReadPrettyAtom);

  const totalRatio = beneficiaryList.reduce((acc, curr) => {
    const num = toNumber(curr?.beneficiaryRatio);
    return acc.plus(BigNumber(isNaN(num) ? 0 : num));
  }, new BigNumber(0));

  const isSuccess = totalRatio.isEqualTo(100);

  const color = isSuccess ? 'text-success' : 'text-error';

  const innerLabel = (
    <>
      {t('Beneficiary ratio is')}
      <div className={`ml-1 mr-1 ${color}`}>{`${totalRatio?.toFixed(2)}%`}</div>
    </>
  );

  return (
    <div className="flex items-center">
      {readPretty ? (
        <>
          <span className="mr-1 font-bold">
            {t('Designated Beneficiary :')}
          </span>
          {innerLabel}
        </>
      ) : (
        <>
          {innerLabel}
          {isSuccess ? (
            <Icon type="done" />
          ) : (
            <Tooltip title={t('beneficiaryRatioTooltip')}>
              <Icon type="exclamation-circle" className="text-error" />
            </Tooltip>
          )}
        </>
      )}
    </div>
  );
};
