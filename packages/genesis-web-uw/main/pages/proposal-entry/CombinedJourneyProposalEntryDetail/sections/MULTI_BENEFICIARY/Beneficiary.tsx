import { FC, useState } from 'react';

import { TabsProps } from 'antd';

import { useAtomValue } from 'jotai';
import { size } from 'lodash-es';

import {
  SectionContainer,
  SimpleSectionHeader,
  Tabs,
} from '@zhongan/nagrand-ui';

import { beneficiaryViewAtom } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/atom';
import { useGetCustomerName } from '@uw/pages/proposal-entry/CombinedJourneyProposalEntryDetail/components/NewCommonRender/hooks/useGetCustomerName';

import { SectionProps } from '../interface';
import { BeneficiaryChildren } from './BeneficiaryChildren';

const tabStyles = `
&_.antd-tabs-tab:!min-w-fit
&_.antd-tabs-nav-list_.antd-tabs-ink-bar:!h-0.5
&_.antd-tabs-nav:!p-0
&_.antd-divider:my-4
&_.antd-upload-btn:!p-0
&_.antd-tabs-content-holder:!mt-0
[&_.nagrand-body-text]:text-[var(--heading-color)]
`;

export const Beneficiary: FC<SectionProps> = props => {
  const { id, title } = props;
  const [activeKey, setActiveKey] = useState<string | undefined>(undefined);
  const beneficiaryView = useAtomValue(beneficiaryViewAtom);

  const getCustomerName = useGetCustomerName();

  const items: TabsProps['items'] = beneficiaryView.map((insured, index) => ({
    key: insured?.uniqueKey ?? index.toString(),
    label: getCustomerName(insured) as string,
    children: <BeneficiaryChildren {...props} insured={insured} />,
  }));

  const setCurrentActiveKey = (key: string | undefined): void => {
    setActiveKey(key);
  };

  // 如果没有insured数据，不显示整个模块
  if (!beneficiaryView || size(beneficiaryView) === 0) {
    return null;
  }

  return (
    <section id={id}>
      <SectionContainer>
        <SimpleSectionHeader weight={500} type={'h4'}>
          {title}
        </SimpleSectionHeader>
        <div className={tabStyles}>
          {size(beneficiaryView) === 1 ? (
            <BeneficiaryChildren {...props} insured={beneficiaryView[0]} />
          ) : (
            <Tabs
              type="line-with-bg"
              activeKey={activeKey}
              items={items}
              onChange={setCurrentActiveKey}
            />
          )}
        </div>
      </SectionContainer>
    </section>
  );
};
