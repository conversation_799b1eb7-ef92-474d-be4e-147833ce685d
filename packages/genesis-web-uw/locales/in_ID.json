{"": "", " has entered manual underwriting, with the underwriting case number": "", " has not successfully passed the automatic {{rule}} check and has been declined.": "", " has not successfully passed the automatic underwriting check and has been declined.": "", ".pdf, .xls, .xlsx, .png, .jpg, .jpeg, .doc, .docx": "", "(Group Policy No. {{groupPolicyNo}})": "", "(wording waiting to be provided)": "", "{{ objectType }} Info": "", "{{ stage }} Stage": "", "{{action}} UW Authority Configuration": "", "{{currentConditionEnumKey}} is not existed in factorEnums.": "", "{{docTypes}} is a multi-version file": "", "{{elapsedDay}} Days Elapsed": "", "{{fileName}} Log": "", "{{first}}_{{second}}": "{{-first}}_{{-second}}", "{{frequency}} {{type}}": "", "{{index}} Order No. {{orderNumber}}": "", "{{index}}. Location": "", "{{index}}. Location: {{title}}": "", "{{index}}. New Object": "", "{{index}}. Object: {{title}}": "", "{{levelname}} already exists": "", "{{mode}} Claim Stack Definition": "", "{{mode}} Details": "", "{{mode}} Invoice Details": "", "{{name}} company": "", "{{name}} Company": "", "{{name}}_v{{version}}": "", "{{objectKeyName}}: {{objectNumber}}": "", "{{objectName}} {{index}}": "", "{{objectTitle}} Info": "", "{{premiumType}} (including tax and discount)": "", "{{premiumType}} (without tax and discount)": "", "{{prev}}-{{next}}": "{{prev}} - {{next}}", "{{rate}}{{separator}}{{date}}": "", "{{role}} Info": "", "{{ruleTitle}} Generation Rule": "", "{{startDate}} ~ {{endDate}}": "", "{{text}} %": "", "{{type}}: {{No}}": "", "{Team Name} is not assigned any strategy, Confirm to submit?": "", "%": "", "+ Add": "", "+ Add loading/Discount": "", "+ Add New": "", "+ Add New Master Agreement": "", "+ Add New Rule": "", "+ Add New Team": "", "+ Add Rule Condition": "", "+ Upload": "", "< Back to Search": "", "< Back To task pool": "", "1st Screening Result": "", "2nd Screening Result": "", "A general rule without a specified Goods already exists. Please select a Goods.": "", "Abbreviation Name": "", "Accept": "", "Acceptance_in_Progess": "", "Acceptedby": "", "Accident Degree": "", "Accident Number in Last 3 Year": "", "Accident Number Last Year": "", "Accident Summary": "", "Account": "", "Account Balance": "", "Account Holder Name": "", "Account Info": "", "Account Name": "", "Account No.": "", "Account Number": "", "Account Transaction Details": "", "Account Transaction Type": "", "Account Type": "", "Accumulated days from all pending proposal status": "", "Accumulated RB Allocation Amount": "Accumulated RB Allocation Amount", "Accumulated Value": "", "Action(s)": "", "Activate": "", "Activate Case": "", "Activate Case On": "", "Activate\\Inactivate Flag": "", "Activate\\Inactivate History": "", "Actual Arrival Time": "", "Actual Delivery Time": "", "Actual Departure Time": "", "Actual Payable Amount": "", "Actual Premium": "", "Actual SA": "", "Actual Total No. of Vehicles {{number}}": "", "Actual Total Premium {{totalPremium}}": "", "Ad hoc Single top up": "", "Ad-hoc Notification": "", "Ad-hoc Single Top Up": "", "Add": "", "Add a Condition": "", "Add a Reminder": "", "Add Account Info": "", "Add Attachment Type": "", "Add Comments": "", "Add Employee Category": "", "Add Extra Loading": "", "Add Factors": "", "Add Insured Object": "", "Add Location Based Object": "", "Add Location-based Object": "", "Add New": "", "Add New Comments": "", "Add New Level": "", "Add New Master Agreement": "", "Add New Members": "", "Add New Organization": "", "Add New Proposal": "", "Add New Quotation": "", "Add New SME Application": "", "Add New Strategy": "", "Add New Team": "", "Add New Transaction": "", "Add Product": "", "Add/Update Time": "", "Additional Equipment": "", "Additional Limit & Deductible Info": "", "ADDITIONAL_LIMIT_DEDUCTIBLE": "", "Address": "", "Address Info": "", "Address Type": "", "Adj Annual Prem": "", "Adj Annual Prem (Rate)": "", "Adj Annual Prem（Rate）": "", "Adj Net Prem": "", "Adj Net Premium": "", "Adjusted Net Premium": "", "Adjustment Comments": "", "Adjustment Date": "", "Adjustment Information": "", "Adult Number": "", "Advanced Payment Amount": "", "After": "", "After add new authority level, you need to define the grade of the level. From top to bottom, the level grade is from lowest to highest.": "", "After modifying the information, it needs to be recalculated. Please confirm.": "", "Age": "", "Age: {{age}}": "年齢: {{age}}", "Agency": "", "Agency Name": "", "Agency: {{agencyName}}": "", "Agent": "", "Agent Code": "Agent Code", "Agent Name": "", "Aggregate Amount": "", "Aggregated Amount (Applying)": "", "Aggregated Amount (Inforce)": "", "Aggregated Amount (Total)": "", "Agreement Settlement Rule": "", "Airline Company": "", "Alert": "", "All": "", "All designated beneficiary information will be deleted. Please Confirm.": "", "All goods": "", "All goods categories": "", "All Information in current table will be deleted after switch, Individual & Organization could not co-exist. Are you sure to delete?": "", "All questions have been answered.": "", "All upload data you currently submit will be recorded. If the file verification failed, it will not be submitted.": "", "All upload data you currently submit will be recorded. If the file verified failed, it will not be saved.": "", "All upload data you currently submit will be recorded.If the file verification failed,it will not be submitted.": "", "Allocation": "", "Allocation Amount": "", "Allocation Date": "", "Allocation Frequency": "", "Almost!": "", "Amount": "", "Amount Name": "", "Ancestor Policy No.": "", "Annual Prem": "", "Annual Prem. Methods": "", "Annual Premium": "", "Annual Standard Planned Premium": "", "Annual Standard Premium": "", "Annualized Premium / Single Premium": "", "Annualized Regular Premium": "", "Annuity": "", "Annuity Allocation History": "", "Annuity Amount": "", "Annuity Defer Period Type": "", "Annuity guarantee Period": "", "Annuity Info": "", "Annuity liability": "", "Annuity Payment Account": "", "Annuity Payment Frequency": "", "Annuity Payment Option": "", "Annuity Payment Period Type": "", "ANNUITY_INFO": "", "Applicable": "", "Application": "", "Application Date": "", "Application Item": "", "Application No": "", "Application No {{ appNo }}": "", "Application No.": "", "Application Type": "", "Applied to waive premium liability by POS": "", "Applied Withdrawal Amount": "", "Appliedby": "", "Apply Master Plan": "", "Apply to all products": "", "Apply to partial paid proposal?": "", "Appointment Rate": "", "Approval": "", "Approval Comment": "", "Approval Date": "", "Approval History": "", "Approval Result": "", "Approval Time": "", "Approval_in_Progess": "", "Approve": "", "Approvedby": "", "Are you sure to activate this case?": "", "Are you sure to cancel?": "", "Are you sure to change plan?": "", "Are you sure to clear all the upload records?": "", "Are you sure to delete current level?  If delete, the all level order will change.": "", "Are you sure to delete selected type? It will remove all files under this type.": "", "Are you sure to delete the level?": "", "Are you sure to delete the level?  Current authority level is in force.": "", "Are you sure to delete the level? Current authority level is in force.": "", "Are you sure to delete this comment?": "", "Are you sure to delete this document?": "", "Are you sure to delete this product?": "", "Are you sure to delete this task?": "", "Are you sure to delete?": "", "Are you sure to remove it?": "", "Are you sure to submit current authority configuration? After submitting, the status of level will become effective.": "", "Are you sure you want to approve the insurance application?": "", "Are you sure you want to decline the insurance application?": "", "Are you sure you want to release this UW task?": "", "Are you sure you want to return the application for the current product?": "", "Are you sure you want to return the entire submission?": "", "Are you sure you want to save the current sort?": "", "Are you sure you want to submit this insurance application?": "", "Arrival Airport": "", "Arrival Delay Time": "", "Assign": "", "Assignee": "", "ASSIGNEE": "", "Assignee Type": "", "Associated goods": "", "Associated Policy": "", "Associated Proposal": "", "Associated UW Task": "", "Associated UW Task Application No.": "", "At least one item must be created under the address, please check.": "", "At least one should be selected": "", "at_least_one_input": "<PERSON><PERSON><PERSON><PERSON> set<PERSON>nya satu kriteria pencarian", "Attached To": "", "Attached To Product": "", "Attachment": "", "ATTACHMENT": "", "Attachment Configuration": "", "Attachment Quotation Stage": "", "Attachment Type": "", "ATTACHMENT_BUNDLE": "", "Attachment/Mandatory": "", "Attachments": "", "Authority Configuration": "", "Authorization Agreement": "", "Authorization Agreement No.": "", "Authorization Agreement Signing Date": "", "Auto": "", "Auto compliance rule result": "", "Auto Compliance Rule Result": "", "Auto Rebalancing": "", "Auto Rebalancing Close": "", "Auto Rebalancing Opened": "", "Auto Renewal": "", "Auto UW Rule Result": "", "Auto Verification Rule Result": "", "AUTO_UW_RULE_RESULT_V2": "", "Automated Fill Condition": "", "Automatic Compliance Result": "", "Automatic Premium Loan": "", "Automatic Premium Loan Detail": "", "Automatic Underwriting Result": "", "Autopay Flag": "", "Average Driving Distance": "", "Back": "", "Back to Edit": "", "Back to main page": "", "Back to Modify": "", "Back to Search": "", "Back To task pool": "", "Back To Worksheet": "", "Bank Account No.": "", "Bank Address": "", "Bank Branch Address": "", "Bank Branch Code": "", "Bank Branch Name": "", "Bank Code": "", "Bank Code/Bank Name": "", "Bank Name": "", "Bank Transfer / Account Type": "", "Based on Calendar Days": "", "basic info": "", "Basic Info": "", "Basic Info Info": "", "BASIC_INFO": "", "BASIC_INFO_VIEW": "", "BASIC_INFO_WITH_STAND_ALONE": "", "BASIC_INFO_WITHOUT_GOODS": "", "Batch list Goods": "", "Batch List Upload": "", "Batch List Upload History": "", "Batch Number": "", "Batch Reassign": "", "Batch Uploading": "", "BCP": "", "Be Attached By": "", "Before (Rate/Amount)": "", "Before Tax & Service Fee": "", "Beneficial Owner": "", "BENEFICIAL_OWNER": "", "Beneficiary": "", "BENEFICIARY": "", "Beneficiary Ratio": "", "Beneficiary ratio is": "", "Beneficiary Type": "", "BENEFICIARY_CN": "", "BeneficiaryRatio": "", "beneficiaryRatioTooltip": "Total rasio penerima manfaat untuk semua penerima manfaat harus sama dengan 100%. <PERSON><PERSON><PERSON> kon<PERSON><PERSON>.", "Benefit": "", "Benefit Account Balance": "", "Benefit Allocation History": "", "Benefit Amount": "", "Benefit info": "", "Benefit Info": "", "Benefit Next Due Date": "", "Benefit Option": "", "Benefit Payment Account Info": "", "Benefit Schedule": "", "BENEFITS_INFO": "", "Bill Amount": "", "Bill Information": "", "bill_amount_details": "", "Bill_No": "", "Bind Task Assignment Rule": "", "Birthday": "", "blacklist_search_result_unit": "<PERSON><PERSON><PERSON>", "Body Type": "", "BONUS": "", "Bonus Allocation History": "", "Bonus Amount": "", "Bonus Code": "", "Bonus Info": "", "Bonus Name": "", "Bonus Next Due Data": "", "Bonus Next Due Date": "", "Bonus Payment Account Info": "", "Bonus Total Balance": "", "Bonus Type": "", "Bonus/malus": "", "Booking Number": "", "Booking Time": "", "Both": "", "BOTH_GOODS_CANNOT_BE_ISSUED_SIMULTANEOUSLY {{primaryGoods}}{{secondaryGoods}}": "", "Branch Name: {{name}}": "Branch Name: {{name}}", "Building Info": "", "Building Size": "", "Building Type": "", "Building up the File, please wait patiently.": "", "Built Year": "", "Burglar Alarm": "", "Business Activity/Sector": "", "Business License No.": "", "Business Month": "", "Business No": "Business No", "Business No. Generation Rule": "", "Business No. n Generation Rule": "", "Business Scenario": "", "Business Transaction No": "", "Business Transaction No.": "", "Business Type": "Business Type", "Buy/Sell": "", "By Commission Category": "", "By Product": "", "BY_EVENT_INFO": "", "by:": "", "By: userName": "By: {{userName}}", "By:{{creator}}": "", "ByEvent_Policy_Data": "", "Calculate": "", "Calculate successfully": "", "Calculate Tax when Clear Account": "", "Calculation Basis": "", "Calculation Date": "", "Calculation Direction": "", "Calculation Frequency": "", "Calculation Level": "", "Calculation Order": "", "Calculation/Capitalization Period": "", "Campaign": "", "Campaign Discount": "", "Campaign Discount Type": "", "Campaign info": "", "Campaign Info": "", "Campaign Type": "", "Campaign_code": "", "Campaign_name": "", "Cancel": "", "Cancel pin": "", "Canceled": "", "Cancelled": "", "Car Owner": "", "Car Owner & Driver & Renter": "", "Car Owner Birthday": "", "Car Owner Gender": "", "Car Owner ID Number": "", "Car Owner ID Type": "", "Car Owner Name": "", "Car Owner Name2": "", "Car Owner Name3": "", "Carring Goods Type": "", "Case Operation": "", "Case Owner": "", "Cash Before Cover": "", "Cash Bonus": "", "Cash Bonus Account Transaction Details": "", "Cash Bonus Allocation Details": "", "Cash Value Saving Account": "", "Change Payment Info": "", "Change Principal": "", "Change the current process flow from the process flow template": "", "Change Underwriter": "", "Changes made after submission will not be revoked.": "", "Channel": "", "Channel Name": "", "Channel User No.": "", "channelCode": "", "channelCode{{channelCode}}": "", "channelUserNo": "", "channelUserNo{{channelUserNo}}": "", "Charge Amount": "", "Charge Code": "", "Charge Due Date": "", "Charge Period": "", "Charge Type": "", "Chassis No.": "", "Check relatives or not": "", "Check Segment": "", "Check staff is on duty or not": "", "Children Number": "", "City": "", "Claim": "", "CLAIM": "", "Claim Amount": "", "Claim Compensation": "", "Claim Experience": "", "Claim History": "", "Claim Name": "", "Claim No": "", "Claim No.": "", "Claim Number in Last 3 Year": "", "Claim Number Last Year": "", "Claim Ratio": "", "Claim Stack": "", "Claim Status": "", "Claim Summary": "", "Claim Type": "", "Claim Waive": "", "Claim_Archives_Room": "", "CLAIM_DETAILS": "", "CLAIM_HISTORY": "", "CLAIM_INFO": "", "Claimable Period": "", "Clause Information": "", "Clear all": "", "Clear All": "", "Clear successed!": "", "Clear Successfully": "", "Click Add another type": "", "Click on the policy version number below to switch policy version information.": "", "Click or drag file here area to upload": "", "Click or drag file here to upload": "", "Click or drag the file here to upload": "", "close": "", "Close": "", "Closed": "", "Code": "", "Code No. {{code}}": "", "Collapse": "", "Collapse All": "", "Collected Amount(Original Currency)": "", "Collected Amount(Policy Currency)": "", "Collection & Refund": "", "Collection Amount Detail": "", "Collection Method": "", "COLLECTION_AND_REFUND": "", "collection_and_refund_amount": "Collection&Refund Amount", "collection_or_refund_amount": "Collection/Refund Amount", "collection_refund_amount_details": "", "collection_refund_item": "Koleksi / Refund Barang", "Color of Plate No": "", "Combination Sequence": "", "Comment": "", "COMMENTS": "", "Comments: {{remark}}": "", "Commision": "", "Commission": "", "Commission (On Top of Premium)": "", "Commission & Service Fee": "", "Commission Details": "", "Commission Generated Date": "", "COMMISSION_AGENT": "", "COMMISSION_AND_SERVICE_FEE": "", "Commission（On Top of Premium)": "", "CommissionAmount": "", "CommissionType": "", "Commodity Information": "", "Common Info": "", "Compaign Discount": "", "Company Name": "", "Compiance History": "", "Complete": "", "Complete Date": "", "Complete Time": "", "Completed Date": "Completed Date", "compliance": "compliance", "Compliance": "", "Compliance Check": "", "Compliance Decision": "Compliance Decision", "Compliance Decision Details": "Compliance Decision Details", "Compliance History": "", "Compliance Info": "Compliance Info", "Compliance Result": "", "Compliance Status": "", "Compliance Task No.": "Compliance Task No.", "Compliance Type": "", "COMPLIANCE_INFO": "", "COMPLIANCE_RESULT": "", "Concurrent Case": "", "CONCURRENT_CASE": "", "Conditional Accepted": "", "Configure the Task Assignment Rule first": "", "Confim to delete this plan?": "", "Confirm": "", "Confirm Application": "", "Confirm Date": "", "Confirm to clear all data?": "", "Confirm to delete": "", "Confirm to delete current document?": "", "Confirm to delete?": "", "Confirmation Date": "", "Confirmation Required": "", "Confirmed Fund Price": "", "Consentee": "", "CONSENTEE": "", "Contact Address": "", "Contact Person": "", "Contact Person Info": "", "Contact Phone Info": "", "ContactPerson": "", "content": "", "Content": "", "Continue to Submit": "", "Contract Date": "", "Contract Number": "", "Copy": "", "copy failed": "", "Copy from": "", "Copy from {{tabName}} Product": "", "Copy from Motor Product": "", "Copy Master Agreement": "", "Copy master agreement  to Relational Policy": "", "Copy successful!": "", "Copy Successfully": "", "Copy To": "", "Copy to New Product": "", "Copy to New Version": "", "Country": "", "Country Code": "", "Country of Residence": "", "Coverage": "", "Coverage / Sub Coverage": "", "Coverage Detail": "", "Coverage Details": "", "Coverage Info": "", "Coverage Level": "", "Coverage Period": "", "Coverage Period Type": "", "Coverage Period Value": "", "Coverage Plan": "", "Coverage plan cannot be matched due to incompleted information, please complete the object information first.": "", "Coverage Plan Details": "", "Coverage plan will be refreshed, please confirm.": "", "Coverage Total Prem": "", "COVERAGE_GOODS_DETAIL_BUNDLE": "", "COVERAGE_INFO": "", "COVERAGE_INFO_DRAWER_CLAIM_STACK": "", "COVERAGE_INFO_DRAWER_COVERAGE_DETAILS": "", "COVERAGE_INFO_DRAWER_DCA_ARRANGEMENT": "", "COVERAGE_INFO_DRAWER_FUND_APPOINTMENT": "", "COVERAGE_INFO_DRAWER_LIABILITY": "", "COVERAGE_INFO_DRAWER_PORTFOLIO_REBALANCING": "", "COVERAGE_INFO_DRAWER_PREMIUM_INFO": "", "COVERAGE_INFO_DRAWER_RETIREMENT_OPTION": "", "COVERAGE_INFO_DRAWER_TOP_UP": "", "COVERAGE_PLAN": "", "COVERAGE_PLANS": "", "COVERAGE_PREMIUM": "", "COVERAGE_PREMIUM_BUNDLE": "", "Create Date": "Create Date", "Create Policy under Master Agreement": "", "Create task assignment rule to match {{module}}.": "", "Create team and bind task assignment rule. The {{module}} hit binded rule will be pushed into this team.": "", "create time": "", "Creation Date": "", "Creator": "", "Currency": "", "currency_amount_Ap": "", "currency_combine_amount": "", "Current Handler": "", "Current Policy Change Overview": "", "Current Policy Overview": "", "Current Premium Amount": "", "Current process flow": "", "Current system date": "", "Current Underwriting Level": "", "Current Version": "", "Currently policyholder & insured are different person. But after the change, we find their info is the same and system will treat them as one person. Confirm to merge them?": "", "currentOperator": "", "Customer": "", "Customer Grade": "", "Customer ID Number": "", "Customer List": "", "Customer Screening Result": "", "Customer Type": "", "Customer type not selected yet. Please confirm.": "", "CUSTOMER_INFO": "", "CUSTOMER_PROFILE": "", "Daily": "", "Data Entry in Progress, Pending Proposal Check, Waiting for Issuance": "", "Data is modified but not saved. Do you want to save the modified content?": "", "Date of birth": "", "Date Of Birth": "", "Date Of Witness": "", "Date Quote Needed": "", "DAY": "", "Day(s)": "", "Days": "", "DayS": "", "Days Types": "", "DCA": "", "DCA Amount (for each period)": "", "DCA Arrangement": "", "DCA Frequency": "", "Death Date: {{dateOfDeath}}": "", "Debit Note Amount": "", "Debit Note Information": "", "Debit Note Information ": "", "Debit Note No.": "", "Debit Note No. No. {{debitNoteNo}}": "", "Deceased": "", "Decision": "", "Decision Details": "", "Decision Fail Decline": "", "Decision Failed": "", "Decision Reason": "", "Decision Successfully": "", "Decision Time": "", "Declaration Date": "", "Declaration Stage": "", "Declaration_Information": "", "Decline": "", "Deducted From Investment": "", "Deductible": "", "Deductible Amount": "", "Deductible Info": "", "Deduction Source": "", "Deduction Type": "", "Defer Period": "", "Delete": "", "Delete success": "", "Delete Successfully": "", "Deleting a goods will also delete all corresponding data, please confirm.": "", "Delivery Status": "", "Department Code": "", "Departure": "", "Departure Airport": "", "Departure City": "", "Departure Country": "", "Departure Delay Time": "", "Departure Point": "Departure Point", "Departure Time Zone": "", "Deposit Account Balance": "", "Description": "", "Designated Beneficiary": "", "Designated Beneficiary :": "", "Designated beneficiary ratio must equal to 100%": "", "Destination": "", "Destination City": "", "Destination Country": "", "Destination Region": "", "Destination Time Zone": "", "detail": "Detil", "Details": "", "Device Abs": "", "Device AEB": "", "Device Airbag": "", "Device Alarm": "", "Device Brand": "", "Device Buyer ID": "", "Device Buyer Review Score": "", "Device Category": "", "Device Description": "", "Device Gear or steering lock": "", "Device GPS": "", "Device ID": "", "Device Immobiliser": "", "Device Info": "", "Device Installed": "", "Device Manufacturer": "", "Device Market Value": "", "Device Model": "", "Device Name": "", "Device Number": "", "Device Perchuase Time": "", "Device Price": "", "Device Seller ID": "", "Device Seller Review Score": "", "Device Status": "", "Device Tracking": "", "Device User": "", "Digit Length": "", "Digit Position": "", "Direct sales": "", "Disbursement Method Information": "", "Discount": "", "Discount Amount": "", "Discount Period (Premium Due No)": "", "discount_type": "", "Distance Confirmation Date": "", "Distribution Method": "", "Ditrh of birth": "", "Dividend Payment Method": "", "DOB": "", "Document": "", "Document Generation": "", "Document Generation Management": "", "Document Name": "", "Document Name {{currentImgTitle}}": "", "Document Type": "", "Document({{total}})": "", "Documents": "", "Dollar Cost Averaging": "", "Don't Need Reminder": "", "Don't Use Sub-item": "", "Down Sell SA": "", "Download ({{size}})": "Download ({{size}})", "Download {{size}}": "", "Download & Send Password": "", "Download All": "", "Download All Event Policies": "", "Download E-Policy": "", "Download failed": "", "Download successfully": "", "Download Template": "", "DRAFT": "", "Drive ID Type": "", "Driver": "", "Driver Birthday": "", "Driver Experience": "", "Driver Gender": "", "Driver ID Number": "", "Driver IdNumber": "", "Driver IdType": "", "Driver Industry": "", "Driver Information": "", "Driver License Number": "", "Driver License Registration Date": "", "Driver Marital Status": "", "Driver MaritalStatus": "", "Driver Name": "", "Driver Name2": "", "Driver Name3": "", "Driver Occupation": "", "Driver Tier": "", "Driver Type": "", "Driving Distance": "", "Driving Experience": "", "Driving License No.": "", "Driving License Registration Date": "", "Driving Licensen No.": "", "Due Date": "Due Date", "due to the change of insured information": "", "Due Unpaid Premium": "", "Duplicate configuration, please check.": "", "Duplicate Master Agreement No.": "", "Duplicated Tag": "", "Duration": "", "E-mail": "", "E-mail Info": "", "E-policy": "", "Each product chooses at least one liability": "", "EB_COVERAGE_GOODS_DETAIL": "", "EB_COVERAGE_PREMIUM": "", "EB_INSURED": "", "Edit": "", "Edit by": "", "Edit By": "", "Edit Document": "", "Edit Extra Loading": "", "Edit Name": "", "Edit Now": "", "Edit Process Flow": "", "Edit Strategy": "", "Edit Team": "", "Editing": "", "effective": "", "Effective": "", "Effective Date": "", "Effective Date Time Zone": "", "Effective Period": "", "Effective Sub Policy": "", "Effective Time": "", "EffectiveDate": "", "ELECTRONIC_EQUIPMENT": "", "Email": "", "Employee Category": "", "Employee Category {{No}}": "", "EMPLOYEE_GROUP": "", "End": "", "End Day": "", "End Time": "", "Engine No.": "", "Enrollment Transaction": "", "Entry Time": "", "Error": "", "Error File": "", "Error happened during upload.": "", "Error Notification": "", "Escalate": "", "Escalate or Reassign": "", "Escalate Stage": "", "Escalate Successfully!": "", "Escalation Task": "", "esclated/esclate": "", "Estimate Lapse Date": "", "Estimated Total  Premium": "", "Estimated Total No. of Vehicles": "", "Evaluatedby": "", "Evaluation Decision": "", "Evaluation_in_Progess": "", "Event Policy Issue Switch": "", "Event Policy No.": "", "Event Policy Upload": "", "Event Policy Upload History": "", "Every {{frequency}}": "", "Examination Date": "", "Examination Description": "", "Examination Result": "", "Exchange Rate": "", "Exchange Rate (PremCur/BaseCur)": "", "Exchange Rate (SaCur/BaseCur)": "", "Exchange Rate (SaCur/PremCur)": "", "Exchange Rate Date (PremCur/BaseCur)": "", "Exchange Rate Date (SaCur/BaseCur)": "", "Exchange Rate Date (SaCur/PremCur)": "", "Excluding Promotion Discount": "", "Exclusion": "Exclusion", "EXCLUSION": "", "Exclusion Category": "", "Exclusion Clause": "", "Exclusion Code": "", "Exclusion Content": "", "Exclusion List": "", "Exclusion Lists": "", "Exclusion Reason": "", "Expand": "", "Expand All": "", "Expired": "", "Expired Date": "", "Expiry": "", "Expiry Date": "", "Expiry Date should be later than effective date": "", "Expiry Date Time Zone": "", "ExpiryDate": "", "Export": "", "Export All": "", "Export Log": "", "Export Time": "", "External Name": "", "External_Doc_No": "", "Extra Loading": "", "Extra Loading amount": "", "Extra Loading Type": "", "Extra Premium": "", "Extra Premium Due Day": "", "Extra Setting": "", "Extract Period": "", "Extract period was entered error, please check!": "", "Extract Policy Condition": "", "Fail Reason": "", "Failed Reason": "", "Failed Record List": "", "Failed Records": "", "Falculative": "", "fee_status": "", "Feedback": "", "FeeType": "", "FF Weight": "", "Fidelity Guarantee": "", "Field can only be digital or letter": "", "Field Value": "", "Fields Name": "", "File Creator": "", "File Name": "", "File Name: {{fileName}}": "", "File size cannot exceed": "", "File Upload Time": "", "Fill In": "", "Fill in from Vehicle List": "", "Fill in the Exisiting Customer": "", "Fill in the Exisiting Customer >>": "", "Fill in the existing account": "", "Fill in the Existing Account": "", "Filter": "", "Final Decision": "", "Final Underwriting Level": "", "FINAL_DECISION": "", "Finance": "", "FINISH_PAYMENT": "", "Fire Alarm": "", "First Name": "", "first.": "", "Fold Menu": "", "Follow the Investment Strategy": "", "Follow the same appointment rate as planned premium and top ups.": "", "For Liability": "", "For List Data": "", "For prior condition, it will be matched in advance whether matches any ordinary condition or not, otherwise it will be ignored. For ordinary condition, it will be matched together otherwise the task will be pushed public pool.": "", "For Product": "", "For Regular Data": "", "For single cash value type, only one formula could be defined. No duplicate allowed.": "", "For Team": "", "For the same rule or rule set, only one record is allowed.": "", "Formula Category": "", "Formula Code": "", "FR Weight": "", "Free amount of Liability SA": "", "Free Investment Amount": "Free Investment Amount", "Free_amount_of_liability_sa": "", "Free_policy_info": "", "Free_policy_no": "", "Frequency": "", "Frequency of Payment": "", "Fronting": "", "Full Name": "", "Full Records": "", "Fund": "", "Fund Allocation": "", "Fund Application Date": "", "Fund Appointment": "", "Fund Appointment After Rebalancing": "Fund Appointment After Rebalancing", "Fund Appointment Before Rebalancing": "Fund Appointment Before Rebalancing", "Fund Appointment For Rebalancing": "", "Fund Balance": "", "Fund Code": "", "Fund Currency": "", "Fund Name": "", "Fund Price": "", "Fund Transaction Date": "", "Fund transaction details": "", "Fund Transaction Details": "", "Fund Value": "", "Fund Value After Rebalancing": "", "Fund Value Before Rebalancing": "", "G Weight": "", "Gender": "", "Generate": "", "Generate Offer": "", "Generate Policy Schedule": "", "Generate Premium": "", "Generate Quotation Form": "", "Generate/Regenerate Reason": "", "Generated Date": "Generated Date", "Generation Time": "", "Get Result": "", "Gift Code": "", "Gift Delivery Dimension": "", "Gift Delivery Method": "", "Gift Delivery Time": "", "Go to New Quote": "", "Go to Quote Bound": "", "Go to Quote Sent": "", "Goods": "", "GOODS": "", "Goods cannot be empty": "", "Goods Category": "", "Goods Code/GoodsName": "", "Goods in Transit": "", "Goods is not on sale, please check.": "", "Goods Name": "", "Goods Name and Model": "Goods Name and Model", "Goods Summary": "", "Goods Version": "", "GoodsName": "", "GoodsVersion": "", "Got it": "", "Green Card Fee": "", "Green Card No": "", "Group Level": "", "Group No": "", "Group Personal Accident": "", "Group Policy": "", "Group Policy No.": "", "Group Policy No. {{groupPolicyNo}}": "", "Group Policy Query": "", "Guarantee Period": "Guarantee Period", "HALFYEAR": "", "has not successfully passed the automatic underwriting check and has been declined.": "", "has Original Pol": "", "has successfully passed the automatic check.": "", "Haulage Permit No": "", "HEADER_MORE_INFO": "", "High Level": "", "Hight of Vehicle": "", "Historical Permium Holiday": "", "Historical Premium Holiday": "", "History": "", "History Type": "History Type", "HOLDER": "", "Home Protection Schema (HPS) Exemption: {{hpsExemption}}": "", "Hospital": "", "Hospital Name": "", "Hour(s)": "", "How to Deal with Balance Amount": "", "How to Use": "", "IBAN": "", "ID Card": "", "ID No.": "", "ID NO.": "", "ID No. ": "", "ID Type": "", "Identifier Info": "", "If": "", "If satisfy all of the following conditions": "", "If satisfy any of the following conditions": "", "If the file you want to upload does not belong to the above file type, click Add another type": "", "If the option is selected, the system will generate reminder based on days only. Otherwise, specific times will be considered.": "", "If the option is selected, the system will withdraw the proposal based on days only. Otherwise, specific times will be considered.": "", "Ignore the rule and proceed": "", "ILP Bonus": "", "Image": "", "Immediate Effect": "", "Import Type": "", "Inactivate Case": "", "Inactivate Case On": "", "Incident Date": "", "Incident Reason": "", "Including Promotion Discount": "", "Individual": "", "Individual Policy Upload": "", "Individual Policy Upload Under Master Agreement": "", "Industrial Classification": "Industrial Classification", "Info not Completed": "", "Initial Number": "", "Initial Premium Amount": "", "Initial Premium Collected": "", "Initial Premium Due": "", "Initial Premium Payment": "", "Initial Principal": "", "Initial_Premium_Payment_Method": "", "Initiallment_Premium_Payment_Method": "", "Initiate Manual Underwriting": "", "Input": "", "Inspection Expiry Date": "", "Installment": "", "Installment / Renewal Premium Payment": "", "Installment No.": "", "Installment Number": "", "Installment Premium": "", "Installment Premium (Before Campaign)": "", "Installment Premium (Before Tax & Service Fee)": "", "Installment Premium (Total)": "", "Installment Premium period": "", "Installment_Premium_Bill": "", "Installment_Premium_including_tax_discount": "", "Installment_Premium_without_tax_discount": "", "Instruction": "", "Insurable Interest": "", "Insurance": "Insurance", "Insured": "", "INSURED": "", "Insured (Main Product)": "", "Insured Certificate Type": "", "Insured Email": "", "Insured ID No": "", "Insured Id No.": "", "Insured Info": "", "Insured Info will be cleared and replaced by policyholder Info. Are you sure to proceed?": "", "Insured Location": "", "Insured Name": "", "Insured Name: {{insuredName}}": "", "Insured Name2": "", "Insured Object": "", "Insured Object Category": "", "Insured Object Information": "", "Insured Object Name": "", "Insured Object Name: {{name}}": "", "Insured Object of Building": "", "Insured Object of Device": "", "Insured Object of Loan Guarantee": "", "Insured Object of Order": "", "Insured Object of Pet": "", "Insured Object of Product": "", "Insured Object of Vehicle": "", "Insured Object Type": "", "Insured Query": "", "Insured Type": "", "INSURED_CN": "", "Insured_Object_Info": "", "INSURED_OBJECT_INFO": "", "INSURED_OBJECT_PROFILE": "", "insured_period": "<PERSON><PERSON> man<PERSON>", "InsuredIDNo": "", "Insuresd ID No.": "", "Insuresd Name": "", "Interest": "", "Interest Balance": "", "Interest Balance Change": "", "Interest Rate": "", "Internal Offset Amount": "", "Invalid": "", "Investment & Loan Info": "Investment & Loan Info", "Investment Horizon": "", "Investment Info": "", "Investment Strategy": "", "INVESTMENT_INFO": "", "Invoice No.": "Invoice No.", "Invoice Number": "", "Invoice Received Date": "", "Invoice Status": "", "Invoice Task Pool": "", "Invoice/Credit Note Number": "", "is Loan Vehicle": "", "is New Vehicle": "", "is Not Registed": "", "Is Renewal Policy": "", "Is renewal quote process required?": "", "is Special Shape Vehicle": "", "Issue": "", "Issue Date": "", "Issue Policy": "", "Issue policy under master agreement": "", "Issue successfully": "", "Issue Tips": "", "Issue without payment": "", "Issue Without Payment": "", "Issue without payment for individual policy": "", "ISSUE_AGENT": "", "Issued": "", "Issued successfully": "", "Issued successfully!": "", "Issured Name": "", "It is not supported to save exactly the same type information": "", "item": "", "Item Code": "", "Item Name": "", "Key Node": "", "Kindly search the Policy No. first.": "", "Label": "", "Label: Value": "", "Laibility Detail": "", "Landline": "", "Language": "", "Lapsed": "", "Lapsed Reason": "", "LapsedDate": "", "LapsedReason": "", "Last Decision": "", "Last Name": "", "Last Operation Time": "", "Last Price Date": "", "Last Update User": "", "Last Year Driving Distance": "", "Latest Interest Amount": "", "Latest Interest Calculation Date": "", "Latest Interest Capitalization Date": "", "Latest Status": "", "latitude": "", "Legal {{type}}": "", "Legal Beneficiary": "", "Legal Representative Info": "", "Legal Trustee": "", "Length of Vehicle": "", "Length: {{length}}": "", "Level": "", "Level Name": "", "Level Order": "", "Levy": "", "Liabilities": "", "Liability": "", "Liability Category": "", "Liability Coverage Period": "", "Liability ID": "", "Liability Name": "", "Liability Premium": "", "Liability SA": "", "Lien": "", "Lien Exclusion Clause": "", "Limit": "", "Limit Info": "", "Link Product": "", "Linked Investment Product": "", "List Data": "", "Loading": "", "Loading & Down Sell": "", "Loading List": "", "Loading Method": "", "Loading Period": "", "Loading Period Type": "", "Loading Type": "", "Loading Value": "", "Loan Balance": "", "Loan Balance Details": "", "Loan Company": "", "Loan Contract No.": "", "Loan Effective Date": "", "Loan Info": "Loan <PERSON>", "Loan Provider": "", "Loan Years": "", "Location {{index}}": "", "Location Based Object": "", "Location Details": "", "Location-based Object": "", "longitude": "", "Machinery Breaakdown": "", "MACHINERY_EQUIPMENT": "", "Main": "", "Main / Rider": "", "Main Benefit": "", "Main Condition Type": "<PERSON><PERSON><PERSON> utama Type", "Main Driving Area": "", "Main Insured": "", "Main Insured ID No.": "", "Main Insured ID Type": "", "Main Insured Name": "", "Main Insured Sub Policy No.": "", "Main Product": "", "Main_Rider": "", "Manager": "Manager", "Manager has been changed. Please check.": "", "Mandatory": "", "Manual Compliance Decision": "", "Manual Input": "", "Manual UW Query": "", "Manual UW Task Pool": "", "Marital Status": "", "Market Price": "", "MARKET_SEGMENTATION": "", "Marketing Goods Selection": "", "Marketing Goods Settings": "", "marriageStatus": "", "Master Agreement Change": "", "Master Agreement Effective Date": "", "Master Agreement Effective Time": "", "Master Agreement Expiry Date": "", "Master Agreement No {{busiNo}}": "", "Master Agreement No.": "", "Master Agreement No. {{masterPolicylNo}}": "", "Master Agreement No. {{masterPolicyNo}}": "", "Master Agreement No.: {{masterPolicyNo}}": "", "Master Agreement Status": "", "Master Agreement Sub-category": "", "Master Agreement Task Pool": "", "Master Insured Name": "", "Master Policy": "", "Master Policy No.": "", "Master Policyholder": "", "Master Policyholder Name": "", "MASTER_AGREEMENT_BASIC_INFO": "", "Master_policy_Status": "", "masterPolicyChangeProcessed": "", "masterPolicyChangeProcessing": "", "Matched Tag": "", "Mater Policy No": "", "Mater Policy No.": "", "Maturity Agreement": "", "Maturity Benefit": "", "Maturity Benefit Account Transaction Detail": "", "Maturity Date": "", "Maturity Reminder Date Compare to Policy Expiry Date": "", "MaximumPaymenttime": "", "Medical Examination": "", "Medical examination is not allowed to edit after issue, confirm to issue": "", "Medical examination is not allowed to edit after issue, confirm to issue?": "", "Medical Examination Item": "", "Medical Examination Item ": "", "Medical Examination Plan": "", "Medical Expense Invoice": "", "Medical Plan": "", "Medical Plan Code": "", "Medical Plan Name": "", "Medical Plan Value": "", "Medical Requirement Status": "", "Method of adding account": "", "Min Premium": "Min Premium", "Min-Premium Type / Min-Premium": "", "Min-Premium Type / Min-Premium should be completed or empty": "", "Minimum Investment Period": "", "Minimum Investment Period Type": "", "Minimum Investment Period Value": "", "Minimum Length": "", "Minimum Protection Value": "", "MIP End Date: {{endDate}}": "", "MIP Start Date: {{stateDate}}": "", "Mobile": "", "Mobile Phone": "", "Model Portfolio": "", "Model Portfolio Code": "", "Model Portfolio Details": "", "Model Portfolio Name": "Model Portfolio Name", "Modifying the selection of sub coverage will clear the configured limit and deductible information. Please confirm.": "", "MONTH": "", "Month(s)": "", "Monthly": "", "More Action": "", "More Info": "", "Motor NCD": "", "MOTOR_FLEET_POLICY_PREMIUM": "", "MOTOR_FLEET_VEHICLE_UPLOAD": "", "MPV": "", "Msg_back_to_policy_info": "Kembali ke Info Polis", "Msg_claim_claim_applied_by": "", "Msg_claim_claim_evaluated_by": "", "Msg_claim_claim_evaluation_date": "", "Msg_claim_claimant_id_no": "", "Msg_claim_claimant_id_type": "", "Msg_claim_claimant_name": "", "Msg_claim_insured_email": "", "Msg_claim_insured_id_no": "ID yang diasuransikan No.", "Msg_claim_Insured_ID_Type": "ID yang diasurans<PERSON>n <PERSON>", "Msg_claim_insured_mobile_number": "", "Msg_claim_insured_name": "", "Msg_claim_last_document_received_date": "", "Msg_claim_last_update_date": "", "Msg_claim_newly_received": "", "Msg_claim_over_30day": "", "Msg_claim_Payment_Method": "<PERSON>", "Msg_claim_pend_reason": "", "Msg_claim_pending_case_status": "", "Msg_claim_product_name": "", "Msg_claim_query_Claim_Query": "k<PERSON><PERSON>", "Msg_claim_registered_by": "", "Msg_claim_registration_date": "", "Msg_claim_report_date": "", "Msg_common_query_POS_Capture_Date": "POS Tangkap Tanggal", "Msg_common_query_POS_Captured_By": "POS Ditangkap oleh", "Msg_common_query_POS_Item": "POS Barang", "Msg_common_query_POS_No": "POS No.", "Msg_common_query_POS_Registered_By": "POS Terdaftar oleh", "Msg_common_query_POS_Registration_Date": "POS Tanggal Pendaftaran", "Msg_common_query_POS_Status": "Status POS", "Msg_common_query_Sort_by_POS_No": "Mengurutkan berdasarkan POS No.", "Msg_common_relationship_name": "", "Msg_customer_service_item": "Customer Service Barang", "Msg_day": "<PERSON>", "Msg_Days": "hari-hari", "Msg_detail_Contract_Information": "Informasi kontrak", "Msg_error_passwordToE": "", "Msg_Market_Master_Policy_No": "", "Msg_Month": "<PERSON><PERSON><PERSON>", "Msg_Months": "Months", "Msg_moreinfo": "Info lebih lanjut", "Msg_paymentperiod_single": "", "Msg_paymentperiod_wholelife": "", "Msg_paymentperiod_years_old": "", "Msg_please_input_right_format": "Silakan format yang tepat masukan", "Msg_Pos_basic_info": "", "Msg_Pos_change_reason": "", "Msg_Pos_Other_Change_Reason": "", "Msg_pos_query_posQuery": "POS Query", "Msg_query_Acount_Info": "Acount Info", "Msg_query_Actual_Amount": "Actual Amount", "Msg_query_Additional_Excess": "<PERSON><PERSON><PERSON><PERSON> tambahan", "Msg_query_answer": "", "Msg_query_Arrival_Place": "kedatangan Tempat", "Msg_query_Arrival_Place_ID": "Kedatangan Tempat ID", "Msg_query_Arrival_Place_Name": "Kedatangan Nama Tempat", "Msg_query_Ascending": "", "Msg_query_Ascending_Order": "Urutan Terkecil", "Msg_query_Auction_Item": "<PERSON><PERSON>", "Msg_query_Body_Type": "<PERSON><PERSON>e badan", "Msg_query_brand_premium_partner": "", "Msg_query_Claim_Documentations": "", "Msg_query_Claim_Number": "<PERSON><PERSON><PERSON>", "Msg_query_Claim_Workflow": "klaim Workflow", "Msg_query_collection": "Collection", "Msg_query_Contract_Effective_Date": "", "Msg_query_Contract_No": "Kontrak No.", "Msg_query_Contract_Termination_Date": "", "Msg_query_Contract_Type": "<PERSON><PERSON><PERSON>", "Msg_query_Contract_Value": "", "Msg_query_Create_Time": "", "Msg_query_data_source": "", "Msg_query_Delay": "Menunda", "Msg_query_Delivery_Information": "informasi <PERSON>", "Msg_query_Departure_Place": "keberangkatan Tempat", "Msg_query_Departure_Place_ID": "Keberangkatan Tempat ID", "Msg_query_Departure_Place_Name": "Keberangkatan Nama Tempat", "Msg_query_Descending": "", "Msg_query_Descending_Order": "Descending Orde", "Msg_query_Documentation_Name": "", "Msg_query_Download_Send_Pas": "", "Msg_query_Download_successful": "", "Msg_query_downLoadError": "", "Msg_query_Engine_Capacity": "<PERSON><PERSON><PERSON> mesin", "Msg_query_FIN": "SIRIP", "Msg_query_Generated_Date": "", "Msg_query_generatedDate": "Generated Date", "Msg_query_Gts": "GST", "Msg_query_Home_Appliance_Information": "Home Informasi Appliance", "Msg_query_Image_info": "Info gambar", "Msg_query_Insured_Object_Basic_Information_Change": "Tertanggung Obyek Dasar Perubahan Informasi", "Msg_query_Insured_Object_Information": "Tertanggung Informasi Obyek", "Msg_query_Insured_Type": "<PERSON><PERSON>", "Msg_query_insuredStatusText": "", "Msg_query_Is_Main": "<PERSON><PERSON><PERSON><PERSON>", "Msg_query_Liability_Category": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Msg_query_Liability_Name": "<PERSON><PERSON>", "Msg_query_Make": "Membuat", "Msg_query_MCC_code": "", "Msg_query_MCC_name": "", "Msg_query_Meal_Type": "<PERSON><PERSON>", "Msg_query_merchant_name": "", "Msg_query_Model": "Model", "Msg_query_more10000": "", "Msg_query_no": "", "Msg_query_Number_of_Item_type": "<PERSON><PERSON><PERSON>", "Msg_query_Number_of_Item_Type": "", "Msg_query_Object_Category": "<PERSON><PERSON><PERSON>", "Msg_query_occupation_class": "", "Msg_query_Order_ID": "<PERSON>d pem<PERSON>an", "Msg_query_Order_Price": "<PERSON><PERSON><PERSON>", "Msg_query_Order_Status": "Status pemesanan", "Msg_query_Order_Time": "Order Time", "Msg_query_Original_Start_Date": "<PERSON><PERSON>", "Msg_query_Payable": "Payable", "Msg_query_Payment_Info": "Payment Info", "Msg_query_Policy_Documentations": "", "Msg_query_Policy_Issue_Date": "", "Msg_query_POS_Documentations": "", "Msg_query_POS_Workflow": "POS Workflow", "Msg_query_productName_version": "Barang Nama _Version", "Msg_query_Rate_Type": "ting<PERSON>", "Msg_query_Receipts": "Receipts", "Msg_query_Receivable": "Receivable", "Msg_query_Reconciliation_Status": "", "Msg_query_records": "ars<PERSON>", "Msg_query_refund": "Refund", "Msg_query_Refund": "", "Msg_query_Registration_Date": "tanggal registrasi", "Msg_query_Scheduled_Arrival_Time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Msg_query_Scheduled_Departure_Time": "Di<PERSON><PERSON><PERSON>an Waktu Keberangkatan", "Msg_query_Seating_Capity": "Tempat duduk Capity", "Msg_query_sendEmail": "", "Msg_query_service_type": "", "Msg_query_snack_modifier": "", "Msg_query_Sort_by_Claim_No": "Urutkan berdasarkan Klaim No.", "Msg_query_Sort_by_Relation_Policy_No": "", "Msg_query_Sort_by_Relation_Pos_No": "", "Msg_query_Sort_by_report_date": "", "Msg_query_Source": "Sumber", "Msg_query_Status": "Status", "Msg_query_Sum_Assured": "<PERSON><PERSON>", "Msg_query_Tax_Detail": "<PERSON><PERSON> pajak", "Msg_query_Tax_Rate_Value": "<PERSON><PERSON><PERSON> / <PERSON><PERSON>", "Msg_query_Tax_Type": "<PERSON><PERSON>", "Msg_query_The_policy_holder_is_in_blacklist": "The policy holder is in blacklist", "Msg_query_titleDes": "", "Msg_query_total": "Total", "Msg_query_transactionNo": "", "Msg_query_transactionType": "", "Msg_query_Transport_Information": "Informasi Transportasi", "Msg_query_Transportation_Number": "Jumlah transportasi", "Msg_query_trigger_category": "", "Msg_query_type": "", "Msg_query_unique_ID": "", "Msg_query_Use_of_Vehicle": "<PERSON><PERSON><PERSON><PERSON>", "Msg_query_Vehicl_No": "Vehicl No.", "Msg_query_Vehicle": "Kendar<PERSON>", "Msg_query_Vehicle_Age": "<PERSON><PERSON><PERSON><PERSON>", "Msg_query_Vehicle_Identification_No": "Vehicle Identification No.", "Msg_query_View_all": "", "Msg_query_Year_of_Make": "<PERSON><PERSON>", "Msg_reconciliation_channel": "Sales Channel", "Msg_Relation_Pos_Number": "", "Msg_Total": "Total", "Msg_transaction_type": "tipe transaksi", "Msg_version": "Versi: kapan", "Msg_Virtural_Insured": "Tertanggung Virtural", "Msg_week": "<PERSON><PERSON>", "Msg_weeks": "Weeks", "Msg_Years": "Years", "MULTI_BENEFICIARY": "", "MULTI_INSURED": "", "MULTI_PAYER": "", "MULTIPLE_OBJECT_INFO": "", "My Task": "", "N Year Risk Amount": "", "name": "<PERSON><PERSON>", "Name": "", "nameCombine": "", "Named Insured": "", "Nationality": "", "NCD": "", "NCD %": "", "NCD Amount": "", "Need Advanced Payment": "", "Need DCA arrangement?": "", "Need Vehicle Examination": "", "Net Prem": "", "Net Premium": "", "New": "", "New Business": "", "New Business & Renewal Configuration": "", "New Business Configuration": "", "New Business Info": "", "New Document": "", "New Master Agreement": "", "New SA": "", "New Vehicle": "", "NEW_BUSINESS_INFO": "", "Next": "", "Next Due Date": "Next Due Date", "Next Due Date: {{date}}": "", "Next Rebalancing Due Date": "", "NextPremiumIncludingTax": "", "NextPremiumWithoutTax": "", "NLG Benefit:": "", "no": "", "NO": "", "No attachment has been uploaded under the selected product.": "", "No case number found": "", "No Claim Discount": "", "No discount configured for current period": "", "No record": "--", "No Results": "", "No valid application elements: INSURED": "", "No valid application elements: PAYER": "", "No valid application elements: POLICY_HOLDER": "", "No Valid Data": "", "No valid master policy is available for this normal policy renewal, please check.": "", "no_data": "Tidak ada data", "No_Data": "", "No.": "", "No. {{appNo}}": "", "No. {{policyNo}}": "", "No. of Accident Free Years": "", "noData": "", "Nominee": "", "NOMINEE": "", "Non Standard Tariff": "", "Non-Location": "", "Non-location Based Object": "", "None": "None", "Normal Policy List": "", "NORMAL_INSURED": "", "NORMAL_PAYMENT": "", "NORMAL_POLICY_HOLDER": "", "normalPolicyList": "", "Not Within Premium Holiday": "", "Note": "", "Notice Reminder": "", "Notification History": "", "Notification Type": "", "Now You Can Create the Team": "", "Number of Accident": "", "Number of Active Policies": "", "Number of Employees": "", "Number of Installment": "", "Number of Luggage": "", "Number of Objects": "", "Number of Order": "", "Number of Pending Policies": "", "Number of Records": "", "Number of Renewal Time": "", "Number of Seat": "", "Number of task assign when user ask": "", "Number of Vehicle Owned": "", "Object": "", "Object ID": "", "OBJECT LIST": "", "OBJECT_INFO": "", "Occupation": "", "Off-Peak Car": "", "OIB": "", "Only Failed": "", "Only insured": "", "Only policyholder": "", "Only view image files?": "", "Open Menu": "", "Open the link in a new window": "", "Operation": "", "Operation History": "", "operation time": "", "Operation_Approve": "", "Operation_Capture": "", "Operation_Current": "", "Operation_Decide": "", "Operation_Operate": "", "Operation_Register": "", "OPERATIONS_COMMENTS": "", "Operator": "", "OPT_IN Check Text": "", "Opt-In Check": "", "Opt-In Rules": "", "Opt-In-Rules": "", "Optional Covers": "", "Optional Text": "", "Order Currency": "", "Order Date": "", "Order ID": "Order ID", "Order Info": "", "Order No.": "", "Order Number": "", "Order Price": "", "Order the Teams while the assignment rule are same": "", "Order Type": "", "Order Value": "Order Value", "Ordinary condition": "", "Organization": "", "Organization ID No.": "", "Organization ID Type": "", "Organization ID Type/No.": "", "Organization Name": "", "Origin SA": "", "Original Master Agreement No.": "", "Original Pol No": "", "Original SA": "", "Original Start Date": "", "Original Sum Assured": "", "Original_policy_no": "", "Other": "", "Other Information": "", "Other Policy Info": "", "Other Product": "", "Other Properties": "", "OTHER_PARTY_ROLES": "", "OTHER_PARTY_ROLES_CN": "", "Others": "", "Over All": "", "Overriding Commission": "", "Package": "", "Package Code": "", "Package Level": "", "Package Name": "", "PackageName": "", "Packages": "", "Packing Information": "", "Pad with zeros": "", "Parcel": "<PERSON><PERSON><PERSON>", "Parcel Number": "Pa<PERSON>el <PERSON>", "Parcel Tier": "<PERSON><PERSON><PERSON>", "Parcel Value": "Parcel Value", "Parcel Volume": "Parcel Volume", "Parcel Weight": "<PERSON><PERSON><PERSON>", "Part Questionnaire Purpose": "", "Partner": "Partner", "partnerCode": "", "partnerType": "", "Pass": "Pass", "Passed": "", "Pay Account": "", "pay_frequency": "Frekuensi Pembayaran Premi", "PAYEE": "", "Payee Info": "", "Payee Name": "", "Payee Type": "", "Payer": "", "payer of Liability": "", "Payer Role": "", "Payer Type": "", "Payer/Payee Type": "", "Payment Amount": "", "Payment Amount By Assignee": "", "Payment By Assignee": "", "Payment Currency By Assignee": "", "Payment Date": "", "Payment Frequency": "", "Payment History": "", "Payment information could not be copied due to differences in some configurations.": "", "Payment information has been successfully copied automatically.": "", "Payment Mehtod": "", "Payment Method": "", "Payment Method / Account Type": "", "Payment Method/Account Type": "", "Payment Method/Account Type Details": "", "Payment Option": "", "Payment Period": "", "Payment Periods": "", "Payment Plan": "", "Payment Status": "", "PAYMENT_ACCOUNT_INFO": "", "PAYMENT_INFO": "", "PAYMENT_INFO_VIEW": "", "PAYMENT_PLAN": "", "PAYMENT_PLAN_PAYER": "", "PAYMENT_PLAN_PREMIUM_PAYMENT": "", "PayMethod": "", "PayMethod / Account Type": "{{-payMethod}}/{{-accountType}}", "PAYOR": "", "payText": "Pembayaran", "Pending Proposal Check": "", "Pending Transaction Amount（Fund Currency）": "", "Pending Transaction Unit": "", "Period": "", "Period Type": "Period Type", "Period Value": "Period Value", "PeriodAge": "", "PeriodYears": "", "Permanent Address": "", "Person List": "", "Personal Record": "", "Pet": "", "Pet ID": "", "Pet Info": "", "Pet Type": "", "Pet Varieties": "", "Phone": "", "Phone No": "", "Phone Number": "", "Phone Type": "", "Pin": "", "Place of Incorporation": "", "Place of Interest": "", "Plan": "", "Plan Code": "", "Plan Goods Version": "", "Plan Level": "", "Plan Name": "", "Plan Premium Model": "", "planName: {{goodsPlanName}}": "", "Planned Premium": "", "Planned Premium Amount": "", "Planned Premium Collected": "", "Planned Premium Layer Details": "", "Planned Premium: {{amount}}": "", "Plate No.": "", "Plate Type": "", "Please": "", "Please add at least one piece of data": "", "Please add at least one product": "", "Please check schema correction. Some schemas have category without name.": "", "Please click 'Apply Master Plan' button to match coverage plan information.": "", "Please confirm the following information is correct . Once confirmed, it cannot be modified.": "", "Please confirm the following information is correct.Once confirmed,it cannot be modified": "", "Please confirm whether the entered Master Agreement information has been saved before uploading the file.": "", "Please Download Template first.": "", "Please enter {{fieldName}} before submitting.": "", "Please enter {{productName}} product decision before submitting the task.": "", "Please enter a number greater than 0": "", "Please enter a number greater than 0 but less than 100": "", "Please enter a number greater than 0 but less than 200": "", "Please enter the decision!": "", "Please fill in the Setting Table": "", "Please generate the offer first.": "", "Please generate the premium first.": "", "please input": "", "Please input": "", "Please input a number": "", "Please input number": "", "Please input positive integer": "", "Please Input Range": "", "Please input valid party ID!": "", "Please input your Product Type!": "", "Please input your Team Name!": "", "Please note when choosing DCA arrangement,  the amount for each period will invest in the fund based on fund appointment rate of planned premium.": "", "Please note when choosing the investment strategy, the fund appointment for premium & portfolio rebalancing will follow the defination on the strategy.": "", "Please notice that the entered coverage along with its related limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage and limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage information will be cleared. Do you want to continue?": "", "Please return to the task pool, search for the proposal number {{proposalNo}} and try again later.": "", "Please save or delete current attachmentType": "", "Please save the policy information before adding comments": "", "Please search or input": "", "Please search transaction first": "", "please select": "", "Please select a higher level underwriter to escalate the case.": "", "Please select a higher level underwriter to referral the case.": "", "Please select an underwriter to escalate the case.": "", "Please select an underwriter to reassign the case.": "", "Please select an underwriter to referral the case.": "", "Please Select At Least One Condition!": "", "Please Select At Least One Liability": "", "Please select at least one record": "", "Please select decision": "", "Please select effective date": "", "Please select Exclusion": "", "Please select factor first": "", "Please select first": "", "Please select language": "", "Please select Liability": "", "please select one": "", "Please select one Goods before submitting!": "Please select one Goods before submitting!", "Please select one Sub-category before submitting!": "", "Please select policy effective date": "", "Please select policy expiry date": "", "Please select policy type submitting!": "", "Please select Product": "", "Please select the Goods Name first.": "", "Please select the insurance applications that need manual underwriting": "", "Please select whole days": "", "Please select your Bind Task Assignment Rule!": "", "Please select your Team Members!": "", "Please select your Team Type!": "", "Please set policyholder": "", "Please Upload": "", "Please Upload Document": "", "Please Upload File": "", "Please Upload invoice": "", "Please upload one file": "", "Please_enter_at_least3characters": "", "please_select": "", "POLICY": "", "Policy Assignment": "", "Policy Basic Infomation": "", "Policy Change": "", "Policy Charge": "", "Policy Configuration": "", "Policy Currency": "", "Policy Delivery Method": "", "Policy E-Document Type": "", "Policy Effective": "", "Policy Effective Check": "", "Policy Effective Date": "", "Policy Effective Date ": "", "Policy Effective Without Collection (NB)": "", "Policy Expiry Date": "", "Policy History": "", "Policy Holder": "", "Policy Info": "", "Policy Information": "", "Policy Issuance Compliance Check （After Premium Payment)": "", "Policy Issuance Rules": "", "Policy Issuance UW Check (After Premium Payment)": "", "Policy Issue Date": "", "Policy List": "", "Policy Loan": "", "Policy Loan Detail": "", "Policy Maturity Termination Method": "", "Policy No": "", "Policy No.": "", "Policy No. {{policyNo}}": "Policy No. {{policyNo}}", "Policy Number Generation Rule": "", "Policy Period": "", "Policy Regeneration": "", "Policy Serial Number": "", "Policy Sign Off Date": "", "Policy Sign Off Rule": "", "Policy Status": "", "Policy Tag": "", "Policy Tag History": "", "Policy Tag List": "", "Policy Tagging": "", "Policy Toolip": "", "Policy Type": "", "Policy UW Decision": "", "Policy UW Decision Details": "", "Policy Year": "", "Policy years": "", "POLICY_CHANGE": "", "POLICY_CHANGE_DETAILS": "", "POLICY_CHANGE_OVERVIEW": "", "POLICY_DETAIL_INFO": "", "POLICY_DETAILS": "", "POLICY_HISTORY": "", "POLICY_HOLDER": "", "POLICY_HOLDER_CN": "", "POLICY_OVERVIEW": "", "Policy_Query": "", "PolicyEffectiveDate": "", "policyEffectiveRuleTooltip": "", "Policyhoder Info": "", "PolicyHolder": "", "Policyholder and insured is the same. The change is applied to": "", "Policyholder Certificate Type": "", "Policyholder Details": "", "Policyholder Email": "", "Policyholder ID No": "", "Policyholder Id No.": "", "Policyholder ID No.": "", "Policyholder ID Type": "", "Policyholder Info": "", "Policyholder Mobile Number": "", "Policyholder Name": "", "PolicyHolder Name": "", "Policyholder Name2": "", "Policyholder Type": "", "Policyholder_Email": "", "Policyholder_ID_No": "", "Policyholder_ID_Type": "", "Policyholder_Mobile_Number": "", "Policyholder_Name": "", "PolicyholderIDNo": "", "PolicyStatus": "", "Portfolio Rebalancing": "", "Portfolio Rebalancing Detail": "", "POS Application Date": "", "POS Effective Date": "", "POS Item": "", "POS No.": "", "POS_Archives_Room": "", "POS_DETAILS": "", "POS_Effective_Date": "", "posDecisionEnum.APPROVE": "", "posDecisionEnum.BACK_DATA_ENTRY": "", "posDecisionEnum.REJECT": "", "posStatusEnum.APPROVAL_IN_PROGRESS": "", "posStatusEnum.CANCELLED": "", "posStatusEnum.DATA_ENTRY_IN_PROGRESS": "", "posStatusEnum.EFFECTIVE": "", "posStatusEnum.INVALID": "", "posStatusEnum.REJECTED": "", "posStatusEnum.WAITING_FOR_APPROVAL": "", "posStatusEnum.WAITING_FOR_COLLECTION": "", "posStatusEnum.WAITING_FOR_DATA_ENTRY": "", "posStatusEnum.WAITING_FOR_EFFECTIVE": "", "posStatusEnum.WITHDRAW": "", "posStatusStepEnum.APPROVAL_PROCESSING": "", "posStatusStepEnum.APPROVAL_WAITING": "", "posStatusStepEnum.CANCELLED": "", "posStatusStepEnum.COLLECTION_PAYMENT": "", "posStatusStepEnum.DATA_ENTRY_CALCULATION": "", "posStatusStepEnum.DATA_ENTRY_CONFIRMATION": "", "posStatusStepEnum.DATA_ENTRY_CS_ITEM": "", "posStatusStepEnum.DATA_ENTRY_PAYMENT_COLLECTION": "", "posStatusStepEnum.EFFECTIVE": "", "posStatusStepEnum.INVALID": "", "posStatusStepEnum.REGISTER": "", "posStatusStepEnum.REJECTED": "", "posStatusStepEnum.SELECT_CS_ITEM": "", "posStatusStepEnum.WITHDRAW": "", "Post Code": "", "Postal Name": "", "Power Type": "", "PRECIOUS_ITEM": "", "Premium": "", "PREMIUM": "", "Premium (Before Campaign)": "", "Premium (Before Tax & Service Fee)": "", "Premium (Total)": "", "Premium & SA Calculation Method": "", "Premium Aggregation Detail": "", "Premium allocation": "", "Premium Calculation Method": "", "Premium Calulation Method": "", "Premium Collected & Allocation": "", "Premium Collection": "", "Premium Collection Time": "", "Premium Detail Download": "", "Premium Details": "", "Premium Discount": "", "Premium Discount on Net Premium": "", "Premium Discount On Tax": "", "Premium Due Date": "", "Premium DueDate": "", "Premium Frequency": "", "Premium Funder": "", "Premium Handling Method": "", "Premium Info": "", "Premium Info Detail": "", "Premium Notice Date": "", "Premium Notice Date Compare with Due Date": "", "Premium or SA Info": "", "Premium Payer": "", "Premium Per Unit": "", "Premium Period": "", "Premium Period Type": "", "Premium Period Value": "", "Premium Status": "", "Premium Type": "", "PREMIUM_AGGREGATION": "", "PREMIUM_AMOUNT": "", "Premium_Discount": "", "Premium_discount_type": "", "Premium_Due_Date": "", "PREMIUM_FUNDER": "", "Premium_including_tax_discount": "", "premium_pay_account": "<PERSON><PERSON><PERSON>", "PREMIUM_PAYER": "", "PREMIUM_PAYER_CN": "", "Premium_without_tax_discount": "", "PremiumDuration": "", "PremiumEndDate": "", "Press enter to record enums, duplicated keys will be ignored.": "", "Preview Offer": "", "Preview Strategy Details": "", "Previous Policy No.": "", "Price Date": "", "Price Date For Adjustment": "", "Principal Balance": "", "Principal Balance Change": "", "Principal Steam Details": "", "Print History": "", "Print Name": "", "Print Reason": "", "Print Time": "", "Prior condition": "", "Priority": "", "Private Task - Active": "", "Private Task - Inactive": "", "process": "", "Process Configuration": "", "Process Failed": "", "Process Successfully": "", "Process Underwriter Level": "", "Process UWer Level": "", "Processing": "", "Product": "", "Product Category": "", "Product Code": "", "Product Code&Name": "", "Product Decision": "", "Product Details": "", "Product Discount": "", "Product in Goods": "", "Product Info": "", "Product Level": "", "Product List": "", "Product Name": "", "Product Name {{name}}": "", "Product name: {{productName}}": "", "Product Premium: {{sa}}: ": "", "Product SA: {{sa}}: ": "", "Product Status": "", "Product Summary": "", "Product Tax": "", "Product Type": "", "Product_Amount": "", "PRODUCT_BUNDLE_BASIC_INFO": "", "Product_Libility_Info": "", "Product_nostyle": "", "Product: {{productCode}}_{{productName}}": "", "productCode_productName": "", "Promo Code": "", "Promotion Code": "", "Promotion Discount": "", "Promotion Discount On Levy": "", "Promotion Type": "", "PROPERTY": "", "Property Coverage": "", "Property Product": "", "Proposal": "", "Proposal Compliance Check （Before Premium Payment)": "", "Proposal Configuration": "", "Proposal Confirmation Date": "", "Proposal Date": "", "Proposal Effective Date": "", "Proposal Flow Setting": "", "Proposal Info": "", "Proposal No": "", "Proposal No: ": "", "Proposal No: {{applicationNo}}": "", "Proposal No: {{proposalNo}}": "", "Proposal No.": "", "Proposal No. {{proposalNo}}": "Proposal No. {{proposalNo}}", "Proposal reminder days can not duplicate. Please check.": "", "Proposal Reminder Rule": "", "Proposal Request Date": "", "Proposal Rule": "", "Proposal Rules": "", "Proposal Status": "", "Proposal Task Pool": "", "Proposal Task Pool Re-assign": "", "Proposal Withdraw Rule": "", "PROPOSAL_DETAILS": "", "PROPOSAL_INFO": "", "Proposal/Policy": "", "Proposal/Policy No.": "", "Proposal/Policy Status": "", "Proposal/POS No": "", "ProposalDate": "", "proposalWithdrawTooltip": "", "Provide Vehicle Photo Later X Days": "", "Public Liability": "", "Public Task": "", "Public Tender": "", "Public Tender No.": "", "Purchase Date": "", "Purchase Price": "", "Purpose": "", "QUARTER": "", "Quarterly": "", "Query Error": "", "Query Escalate Users Failed": "", "Query Reassign Users Failed": "", "Query Referral Users Failed": "", "QUERY_ATTACHMENTS": "", "QUERY_POLICY_HISTORY": "", "QUERY_RENEWAL_HISTORY": "", "query-Agent": "", "query-Agent Name": "", "query-Allocation Amount": "", "query-Business No": "", "query-Consentee": "", "query-Deductible Amount": "", "query-Effective Date": "", "query-Expiry Date": "", "query-Extra Loading": "", "query-Fund Appointment": "", "query-Fund Currency": "", "query-Fund Name": "", "query-Gender": "", "query-Goods Name": "", "query-Goods Version": "", "query-GoodsName": "", "query-GoodsVersion": "", "query-History Type": "", "query-ID Type": "", "query-Identifier Info": "", "query-Individual": "", "query-Insured Email": "", "query-Insured Name2": "", "query-Investment Strategy": "", "query-Is Renewal Policy": "", "query-Main Benefit": "", "query-Master Policy No.": "", "query-Mobile Phone": "", "query-No.": "", "query-Nominee": "", "query-Organization": "", "query-Other Policy Info": "", "query-Others": "", "query-Payer": "", "query-Payment Frequency": "", "query-Payment Method": "", "query-Payment Period": "Payment Period", "query-PeriodAge": "", "query-Policy Currency": "", "query-Policy Effective Date": "", "query-Policy Issue Date": "", "query-Policy Loan": "", "query-Policy Regeneration": "", "query-Policyholder": "", "query-PolicyHolder": "", "query-Policyholder Email": "", "query-Policyholder ID No.": "", "query-Policyholder Info": "", "query-Policyholder Name": "", "query-PolicyNo": "", "query-Premium Due Date": "", "query-Product": "", "query-Proposal No.": "", "query-Registration Date": "", "query-Relationship With Insured": "", "query-Relationship With Policyholder": "", "query-Renewal": "", "query-Sales Channel": "", "query-Select All": "", "query-Service Fee": "", "query-Settlement Date": "", "query-Social Account": "Social Account", "query-Status": "", "query-Transaction Type": "", "query-Trustee": "", "Questionnaire": "", "QUESTIONNAIRE": "", "Questionnaire Info": "", "Questionnaire Name": "", "QUESTIONNAIRE_INFO": "", "Quick Menu": "", "Quotation": "", "Quotation Configuration": "", "Quotation Info": "", "Quotation Information Pre-check": "", "Quotation No": "", "Quotation No.": "", "Quotation No. {{proposalNo}}": "", "Quotation Period": "", "Quotation Query": "", "Quotation Stage": "", "Quotation Status": "", "Quotation Task Pool": "", "Quote Bound": "", "Random Check": "", "Random Check Configuration": "", "Random Ratio": "", "Rate": "", "Rate Type": "", "Rate-Classes BI": "", "Rate-Classes OD": "", "Rate-Classes PD": "", "Rate-Classes PIC": "", "Rate/Amount": "", "Re-accumulate and affect target": "", "Re-assign": "", "Re-underwriting Reason": "", "Re-underwriting Type": "", "Re-Upload": "", "Re-Upload Successfully": "", "Read More": "", "Reason": "", "Reason Comments": "", "Reassign": "", "Reassign | {{selectLength}} Option(s)": "", "reassign control {{proposalNos}}": "", "reassign control batch": "", "Reassign or Referral": "", "Reassign Successfully!": "", "Rebalance Frequency": "", "Rebalancing Date": "", "Rebalancing Frequency": "", "Rebalancing History": "", "Receivepromotionalemailsornot": "", "Recipient": "", "Reconciliation Status": "", "Records / Number of total records": "", "Recount": "", "Recover": "", "Recurring Single top up": "", "Recurring Single Top Up": "", "Recurring Single Top Up Frequency": "", "Recurring Single Top Up Period": "", "Reduce Coverage": "", "Referral": "", "Referral or Reassign": "", "Referral Reason": "", "Referral Response": "", "Referral Successfully!": "", "Referral Task": "", "Refresh Confirm": "", "Regenerate": "", "Regenerate Error": "", "Regenerate Reason": "", "Regenerate Successfully": "", "Register Date": "", "registration area": "", "registration category": "", "Registration Date": "", "registration hiragana": "", "Registration No.": "", "registration serial no": "", "Regular Bonus Plan": "", "Regular Premium": "", "Regular top up": "", "Regular Top Up": "", "Regular Top Up Collected": "", "Regular Top Up: {{amount}}": "", "Regular Top-up": "", "Regular Withdrawal": "", "Reinstate": "", "Reinsurance Decision": "", "REINSURANCE_INFO": "", "reject": "", "Reject": "Reject", "Rejected": "", "Related Policy": "", "Related Policy Overview": "", "Related to insured": "", "Related to insured object": "", "Related to policy": "", "Related to policyholder": "", "RELATED_POLICY": "", "RelatedPartiesInformation": "", "Relation with primary insured": "", "Relation_Policy_No": "", "Relationship No.": "Relationship No.", "Relationship Type / Relatinship No.": "", "Relationship Type / Relatinship No. should be completed or empty": "", "Relationship With Insured": "", "Relationship with Policyholder": "", "Relationship With Policyholder": "", "Relationship With Policyholder: {{holderRelationRemark}}": "", "Relative": "", "Release": "", "Release Failed": "", "Release Success": "", "Release Task": "", "Release Time": "", "Remaining Amount": "", "RemainingPaymentTime": "", "Remark": "", "remarks": "", "Remarks": "", "REMARKS": "", "Reminder": "", "Reminder Frequency(days)": "", "Remove": "", "Remove a Reminder": "", "Remove Date": "", "Render Error": "", "Renew": "", "Renewal": "", "Renewal Extraction Date": "", "Renewal Failure Reason": "", "Renewal History": "", "Renewal Policy No.": "自動継続契約番号", "Renewal Policy No. Generation Rule": "", "Renewal Quotation Expiry Date": "", "Renewal Quotation Info": "", "Renewal Reminder Date": "", "Renewal Reminder Date Compare to Policy Expiry Date": "", "Renewal Reminder Rule": "", "Renewal Status": "", "Renewal UW Info": "", "RENEWAL_INFO": "", "RENTER": "", "Reopen Comment": "", "Repayment Amount": "Repayment Amount", "represent_no": "Perjanjian No.", "Reprint Successfully": "", "Requirement Category": "", "Requirement Code": "", "Requirement Descriptions": "", "Requote": "", "Reset": "Reset", "Residential City": "", "Residential Status": "", "Retirement Age (Insured)": "", "Retirement Option": "", "Retirement Option Start": "", "Retirement Option Start Date": "", "Retirement option start date must be future date.": "", "retured": "", "Return": "", "Return Current Product to Data Entry": "", "Return Entire Submission to Data Entry": "", "Return Reason": "", "Return to Data Entry": "", "Reupload": "", "Reversed": "", "Reversionary Bonus": "Reversionary Bonus", "Reversionary Bonus Allocation Details": "Reversionary Bonus Allocation Details", "RF Weight": "", "Riders": "", "Risk Aggregation": "", "Risk Aggregation Detail": "", "Risk Category": "", "Risk Classification": "", "Risk Sub-category": "", "Risk Underwriting Decision": "", "RISK_AGGREGATION": "", "RiskStartDate": "", "Role": "", "Role Name": "", "Roles": "", "RR Weight": "", "Rule Code": "", "Rule Code/Rule Set": "", "Rule Condition": "", "Rule Configuration": "", "Rule Details": "", "Rule Name": "", "Rule Result": "", "Rule Type": "", "Rule/Rule Set Category": "", "Rule/Rule Set Code": "", "Rule\\Rule Set": "", "Running": "", "SA After Down Sell": "", "SA Multiplier": "", "Sales Agreement Code": "", "Sales Channel": "", "Sales Channel Code": "", "Sales Channel Name": "", "Sales Channel Type": "", "Sales Time": "", "Sales_Channel": "", "SALES_CHANNEL": "", "SalesChannel": "", "Same as": "", "Same As": "", "Same as Initial Premium Payment": "", "Same As Payer of Liability": "", "Save": "", "Save Failed": "", "Save Successfully": "", "Saving Successfully!": "", "Schedule Period Type": "", "Schedule Period Value": "", "Scheduled Arrival Time": "", "Scheduled Departure Time": "", "Scheduled Rate": "", "Scope of Application": "", "Search by Group Policy No.": "", "Search by Master Agreement No.": "", "Search by Name": "", "Search by Operator": "", "Search Insured by Customer Type？": "", "Search Name": "", "Search Policyholder by Customer Type？": "", "searchResult": "", "Secondary Life Insured": "", "SECONDARY_LIFE_INSURED": "", "SecondaryLifeInsured": "", "Segmentation Factor": "", "SEGMENTATION_FACTOR": "", "Select": "", "Select a manager for the team": "", "Select a Template": "", "Select all": "", "Select All": "", "Select Coverage / Sub Coverage": "", "Select Insured Object": "", "Select Plan Group": "", "Select Questionnaire Language": "", "Select the process flow you want to use": "", "Select Type": "", "selectAtLeastOne": "", "Send": "", "Send Back to Origin UWer": "", "Send Back to Task Pool": "", "Send Back to Task Pool failed": "", "Send Back To Task Pool success": "", "Send back to Task Pool?": "", "Send Back to UW Pool": "", "send back to UW task": "", "Send Time": "", "Sender": "", "Sending Status": "", "Senior Number": "", "Seperate by Proposal Status": "", "Sequence Length": "", "Sequence Value": "", "Sequence_No": "", "Service Company": "", "Service Company Code": "", "Service Fee": "", "Service Fee Amount": "", "Service Fee Generated Date": "", "Service Fee Type": "", "SERVICE_AGENT": "", "Set Deductible": "", "Set Limits": "", "Set Priority": "", "Set task push strategy for each team, such as Round Robin and task push by workload.": "", "Settled": "", "SettleFlag": "", "Settlement Date": "", "Settlement Frequency": "", "Settlement Start Date": "", "Sex": "", "Short Rate Method": "", "Should higher than previous policy year": "", "Show_with_Card": "", "Showing {{current}} to {{pageSize}} of {{total}} results": "", "SINGLE": "", "Single Premium": "", "Single Top Up": "", "Single Top Up Amount": "", "Single Top Up Collected": "", "Single Top Up Type": "", "Single Top Up: {{amount}}": "", "Single Top-up": "", "SMS": "", "Social Account": "", "Sorry, failed to upload documents because Master Policy NO. hasn't been filled. Please check.": "", "Sort": "", "Sort by Application Date. (Ascending)": "", "Sort by Application Date. (Descending)": "", "Sort by Create Time": "", "Sort By Creation Date (from oldest to newest)": "", "Sort by Group Policy No. (Ascending)": "", "Sort by Group Policy No. (Descending)": "", "Sort by Operation Time": "", "Sort by Proposal No. (Ascending)": "", "Sort by Proposal No. (Descending)": "", "Sort by Quotation No. (Ascending)": "", "Sort by Quotation No. (Descending)": "", "Sort by Quote Need Date. (Ascending)": "", "Sort by Quote Need Date. (Descending)": "", "Sort by time": "", "Sort Times Ascending": "", "Sort Times Descending": "", "Sort_by_Last_Upload_Time": "", "Sort_by_Policy_No": "", "Special Agreement": "", "Special Agreement Code": "", "Special Agreement Description": "", "Special Agreement Type": "", "Special Code": "", "Special Description": "", "SPECIAL_AGREEMENT": "", "SPECIAL_AGREEMENT_WITH_PLAN": "", "Specific Info": "", "SPECIFIC_INFO": "", "Specified Applicable Goods": "", "Stack Code": "", "Stack Description": "", "Stack Liability Name": "Liability Name: {{liabilityName}}", "Stack Name": "Name Stack", "Stack Type": "Stack Type", "Stack Unit": "Stack Sa<PERSON>an", "Stack Value": "<PERSON><PERSON>", "Stack Value Type": "", "Stamp Duty": "", "Standard": "", "Standard Premium": "", "Standard Tariff": "", "Start": "", "Start | End": "", "Start by Creating a Rule": "", "Start Date": "", "Start Day": "", "Start Time": "", "Status": "", "STOP_PAYMENT": "", "Strategy": "", "Strategy Asset Allocation": "", "Strategy Code": "", "Strategy Detail": "", "Strategy Name": "", "Strategy Name (auto generate)": "", "Strategy Relatives": "", "Street Name": "", "Street No.": "", "Structure": "", "Sub Campaign Category": "", "Sub Coverage": "", "Sub Policy (Main)": "", "Sub Policy (Relative)": "", "Sub Policy Info": "", "Sub Policy No.": "", "Sub Policy No. {{policyNo}}": "Sub Policy No. {{policyNo}}", "Sub Policy Status": "", "Sub Standard": "", "Sub Total": "", "SUB_STANDARD_CODE": "", "SUB_STANDARD_RECORD": "", "Sub-items": "", "Sub-standard Code": "", "Sub-standard Code List": "", "Subject": "", "Submission No": "", "Submission No.": "", "Submit": "", "Submit Failed": "", "Submit Failed: {{message}}": "", "Submit successfully": "", "Submit Successfully": "", "Submit Tips": "", "Substandard Code": "", "success": "", "Successful": "", "Successful Records": "", "successfully": "", "Sum Assured": "", "Sum_Assured_with_free_amount": "", "Sum_Assured_without_free_amount": "", "Summary": "", "Sure": "", "Survival Benefit": "", "Survival Benefit Account Transaction Detail": "", "Survival Benefit Payment Account": "", "Survival Benefit Payment Frequency": "", "Survival Benefit Payment Option": "", "Suspend Reason": "", "Suspension Certificate": "", "Sustav će koristiti Ovdje konfiguriranu Opću odluku pravila i pokrenuti tijek prijedloga na temelju ove odluke. Ako se odbije\"": "", "SWIFT Code": "", "Switch Confirm": "", "Switching customer types will clear existing customer data, please confirm.": "", "Switching the plan group will clear the selected products. Please confirm.": "", "Symbol not matched, please check GeneralSymbols.": "", "System error": "", "System Error": "", "System generates": "", "System logon user is different from the case handler. please check!": "", "System Source": "", "System will trigger automatically confirm the policy sign off X days after policy issue date.": "", "System will trigger reminder notification when the proposal stays in below status after X days.": "", "System will trigger the proposal flow based on the 'Underwriting Decision' of the rule configuration here. If declined, system will reject the proposal. And if manual, system will trigger manual underwriting check for this proposal.": "", "System will trigger the proposal flow based on the \"Compliance Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will trigger manual compliance check for this proposal.": "", "System will trigger the proposal flow based on the \"Verification Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will send the proposal to manual verification.": "", "Tag": "", "Tag Name": "", "Target Return(%)": "", "Target Rule No": "", "Target Rule No.": "", "Target Volatility(%)": "", "Task Assignment Rule": "", "Task Assignment Strategy": "", "Task Create Date": "", "Task No.": "", "Task Pick Up": "", "Task Push Strategy": "", "Task Push Strategy has been configured": "", "Task Push Supplementary Strategy": "", "Task Status": "", "Task successfully assigned to the user.": "", "Task Successfully Withdrawn": "", "Task Type": "", "Tax": "", "Tax Amount": "", "Tax Info": "", "Tax Rate/Value": "", "Tax Setting": "", "Tax Type": "", "TB Type": "", "Team Maintenance Type": "", "Team Management": "", "Team Members": "", "Team Name": "", "Team Name: {{name}}": "", "Team(s) for the stragegy": "", "Terminal Bonus": "", "Terminated": "", "Terminated Reason": "", "Termination": "", "Termination Date": "", "Termination Date(Lapsed Date)": "", "Termination Reason": "", "TerminationDate": "", "TerminationReason": "", "text": "", "text_select": "", "The Amount input should less than original sum assured of the liability.": "", "The Amount input should less than original sum assured of the product.": "", "The amount is consists of 3 parts： Planed Premium,Single Top-Up and Regular Top-Up.": "", "The customer should pay the premium before issuing the policy .": "", "The date & benefit amount list below is calculated based on current policy info. If there is any further policy change occurred, the real benefit date & amount may change.": "", "The export may take a long time, you can download the Excel Fails when the status is completed.": "", "The file is still being generated. Please wait a moment.": "", "The Installment premium is changed from ": "", "The issuance of proposal {{relatedPolices} depends on proposal {{issuanceNo}}. If the decline of this underwriting task results in the withdrawal of proposal {{issuanceNo}}, proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "", "The issuance of the proposal {{relatedPolices}} depends on this proposal. If this proposal is withdrawn, the proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "", "The location cannot be the same, please check.": "", "The modification of the policy information did not pass the compliance verification. The proposal is canceled, and the underwriting task is closed. Please confirm.": "", "The modification of the policy information has triggered a manual compliance task. Please wait for the submission of the compliance task before continuing with the underwriting task.": "", "The net premium has changed": "", "The object names cannot be the same, please check.": "", "The other process is explained in Withdraw": "", "The payment information for this product has not been entered. Please confirm.": "", "The policy data loaded from master policy will be deleted. Do you want to continue?": "", "The policy data loaded from master policy will be refreshed according to new master policy number. Do you want to continue?": "", "The policy does not exist.": "", "The policy has been issued successfully.": "", "The policy is not within renewal extraction period, please confirm whether to raise renewal.": "", "The policyholder you selected has not been created yet. Please confirm.": "", "The POS application is already withdrawn. You don't need to underwrite it anymore.": "", "The Premium Has Changed": "", "The proposal has been sent to New Quote.": "", "The proposal has been sent to Quote Bound.": "", "The proposal has been submitted to manual underwriting.": "", "The proposal has been successfully withdrawn.": "", "The proposal has failed automated underwriting and was declined by the system.": "", "The proposal has failed automated underwriting due to the changes.": "", "The proposal has failed automated underwriting.": "", "The proposal has Lapsed.": "", "The proposal has passed automated underwriting.": "", "The proposal is already declined or postponed by underwriter. You don't need to perform verification anymore.": "", "The proposal is already reject by manual compliance user. You don't need to perform verification anymore.": "", "The proposal is already withdrawn (by client, channel or auto withdrawn by the company). You don't need to perform verification anymore.": "", "The proposal is already withdrawn. You don't need to underwrite it anymore.": "", "The proposal will lapse, are you sure to continue？": "", "The renewal proposal is generated successfully. Proposal No.{{No}}": "", "The renewal quotation is generated successfully. Quotation No.{{No}}.": "", "The renewal validation failed, please check whether the current policy meets the renewal conditions.": "", "The required Master Agreement information is insufficient to upload the file.": "", "The same person as Policyholder": "", "The same Plan already exists": "", "The same Plan name already exists under the current policy, please modify and submit! ": "", "The selected coverage/sub coverage and insured object have been configured with the corresponding deductible. Please confirm.": "", "The selected coverage/sub coverage and insured object have been configured with the corresponding limit. Please confirm.": "", "The team selected above will be brought in here": "", "The updated information will not be saved, are you sure to back?": "", "The user who appointed as team manager is deleted, please reset team manager if needs.": "", "The verification task has been submitted.": "", "There is duplicate exclusion record exist. Please check": "", "There is un-complete medical examination request or pending issue request, confirm to submit manual UW?": "", "Third Party Collection": "", "Third Party Transaction No": "", "This case is escalated from {{user}}.": "", "This document contains vehicle information, premium details and riders information.": "", "This factor is an enumeration type, but enumKey is missing.": "", "This liability is mutually-exclusive with {{tipsText}}": "", "This rule has been binded by team {{teamNames}}, please unbind from team first.": "", "This team has been binded by strategy {{StrategyNames}}, please unbind from strategy first.": "", "This underwriting case is currently under {{user}}. Reassigning it may affect the progress of tasks being processed. Please confirm.": "", "Threshold for Rebalancing": "", "Ticket": "", "Ticket Number": "", "Ticket Price": "", "Ticket Type": "", "Times": "", "Times Types": "", "Tips": "", "Title": "", "to": "", "To be expired": "", "Tonnage": "", "Top Up Due Date": "", "Top Up Period:": "", "Total": "", "Total {{count}} {{objectName}}s": "", "Total Allocated Bonus": "", "Total Amount": "", "Total Beneficiary ratio should be equal to 100%": "", "Total Campaign Discount": "", "Total CB Allocation": "", "Total Claim Amount": "", "Total Commission Amount": "", "Total Extra Loading": "", "Total Fund Value": "", "Total Installments": "", "Total Insured No": "", "Total Insured No.": "", "Total Investment Amount": "", "Total items": "", "Total Loan Balance": "", "Total Outstanding Premium": "", "Total Paid Premium": "Total Paid Premium", "Total Premium": "", "Total Premium Amount": "", "Total Premium Amount Detail": "", "Total Premium Amount Details": "", "Total Premium Collected": "", "Total Premium Name": "", "Total Price": "", "Total Primary Insured No.": "", "Total Principal Amount": "", "Total Product Discount": "", "Total Refund Premium": "Total Refund Premium", "Total Risk Amount": "", "Total Risk Amount Details": "", "Total Sub Policy": "", "Total Sum Assured": "", "Total Tax": "", "Total TIV": "", "Total Unpaid Premium (due & undue)": "", "total_amount": "", "TOTAL_PREMIUM": "", "Total: {{amount}} insured": "", "Total: {{total}}": "", "Total: {{total}} Strategies": "", "Total: Records": "", "Transaction": "", "Transaction Amount": "", "Transaction Date": "", "Transaction Effective Date": "", "Transaction Efffective Date": "", "Transaction Name": "", "Transaction No.": "", "Transaction Status": "", "Transaction Time": "", "Transaction Type": "", "Transaction Unit": "", "TransactionDate": "", "Transation Effective Date": "", "Transit ID": "Transit ID", "Transport Information": "", "Transportation Number": "Jumlah transportasi", "Transportation Type": "", "Transportion_No": "", "Travel Agency": "", "Travel End Date": "", "Travel Expense": "", "Travel Info": "", "Travel Order Number": "", "Travel Order Type": "", "Travel Start Date": "", "Travel Type": "", "TRAVEL_OBJECT_INFO": "", "Trip Info": "", "Trip Type": "", "Trustee": "", "TRUSTEE": "", "Turn back to Manual UW Task Pool": "", "Type": "", "Type a Comment...": "", "Type of Business": "", "Unanswered question exists, please confirm.": "", "Underwriter": "", "Underwriter Name": "", "underwriting": "", "Underwriting": "", "Underwriting Authority": "", "Underwriting case is under review.": "", "Underwriting Check": "", "Underwriting Check Text": "", "Underwriting Configuration": "", "Underwriting Criteria": "", "Underwriting History": "", "Underwriting Level": "", "Underwriting Strategy": "", "Underwriting Task": "", "UNDERWRITING_DECISION": "", "UNDERWRITING_TAG": "", "Unit": "", "Unit Adjustment": "", "Unit No.": "", "Unit No. and Building Name": "", "Unit Premium": "", "Units To Be Adjusted": "", "Universal Saving Account": "", "Unnamed Insured": "", "Unpaid Amount": "", "Update Date": "", "Update OCR Result": "", "Updated at": "", "updateTime": "", "updateUser": "", "Upload": "", "Upload Application Form": "", "Upload Attachment": "", "Upload Date": "", "Upload Document": "", "Upload Failed": "", "Upload Invoice": "", "Upload New Document": "", "Upload Result": "", "Upload Successfully": "", "Upload Time": "", "Uploading": "", "Usage": "", "Usage Based Premium Detail": "", "Usage Code": "", "Usage Upload": "", "Usage Upload History": "", "Use Sub-item": "", "User": "", "User List": "", "User Name": "", "UW Case Operation": "", "UW Case Required Level": "", "UW Comments": "", "UW Criteria": "", "UW Critieria Standard": "", "UW Decision": "", "UW Decision Detail": "", "UW Decision Details": "", "UW Decision History": "", "UW Entry Date": "", "Uw History": "", "UW History": "", "UW in Process": "", "UW Message": "", "UW Owner": "", "UW Query": "", "UW Stage": "", "UW Task": "", "UW Task No": "", "UW Task will be submitted.": "", "UW_Info": "", "UW_INFO": "", "UW_OPERATION": "", "V": "", "Valid": "", "Valid input: {{minValue}} to {{maxValue}}": "", "Value": "", "Value Type": "", "Vehicle": "", "Vehicle Additional Equipment": "", "Vehicle Age": "", "Vehicle Capacity": "", "Vehicle Color": "", "Vehicle Damaged": "", "Vehicle Examination Area": "", "Vehicle Examination Way": "", "Vehicle Info": "", "Vehicle Inspection Information": "", "Vehicle Loan": "", "Vehicle Make": "", "Vehicle Model": "", "Vehicle Plate No.": "", "Vehicle Premium Detail Download": "", "Vehicle Structure": "", "Vehicle Type": "", "Vehicle Usage": "", "VEHICLE_INFO": "", "VEHICLE_LIST_UPLOAD": "", "Verification Check": "", "Verification Comment": "", "Verification Decision": "", "Verification Detail": "", "Verification Fail": "", "Verification failed,please upload again.Upload vehicle statistics:": "", "Verification failed.": "", "Verification History": "", "Verification Pass": "", "Verification Reason": "", "Verification Task Pool": "", "Verification Task Pool Re-assign": "", "Verification task will be submitted.": "", "Verification/Compliance/UW Process Flow Configuration": "", "Verify success.Upload vehicle statistics:": "", "Version": "", "Vesting age is invalid if it is earlier or equal to insured entry age.": "", "Vesting: {{vestingAge}}": "Vesting: {{vestingAge}}", "view": "", "View": "", "View All": "", "View Attachment": "", "View Deductible": "", "View Detail": "", "View History": "", "View Liability": "", "View Limits": "", "View More": "", "view my task": "", "View Policy Detail": "", "view public task": "", "View Tax Details": "", "View the photocopy in a new browser page": "", "View Withdrawal Schedule": "", "ViewAll": "", "VIN No": "", "Vin No.": "", "Waiting Days to Withdraw": "", "Waiting Effective": "", "Waiting For Compliance": "", "Waiting for process": "", "Waiting For Underwriting": "", "Waiting For Verification": "", "Waiting_for_Acceptance": "", "Waiting_for_Approval": "", "Waiting_for_Evaluation": "", "Waived": "", "waiver": "", "WAIVER_PAYMENT": "", "Weekly": "", "Weight": "", "When the policy comes to the end of the policy period, the system will run a regular batch to set the policy status to Terminated": "", "When you change it, the current data content will be cleared. Are you sure to change it?": "", "Whether need to double check for rejected case": "", "WHOLELIFE": "", "Width of Vehicle": "", "With Open Issue": "", "With Open Pending Case": "", "With Open Pending Issue": "", "With Pending Case": "", "withDraw": "", "Withdraw Proposal": "", "Withdraw Reason": "", "Withdraw Reason Content": "", "Withdraw Task": "", "Withdraw the Task": "", "Withdrawal Amount": "", "Withdrawal By Amount": "", "Withdrawal Due Date": "", "Withdrawal Period": "", "Withdrawal Reason": "", "Withdrawal Reason Content": "", "Withdrawal Schedule": "", "Withdrawal Successful": "", "Withdrawed successfully": "", "withdrawn": "", "Within {{Product}}, some responsibilities have mutually exclusive relationships, please check.": "", "Within Premium Holiday": "", "Witness": "", "Witness Info": "", "Work Injury Compensation": "", "Workflow": "", "Write Off Amount": "", "Year": "", "YEAR": "", "Year of Manufacturing": "", "year(s)": "", "Year(s)": "", "Yearly": "", "yes": "", "You can click to amend the proposal information. The amendment will be synchronised to the proposal and may trigger other checks.": "", "You can download the file and check detail reason.": "", "You can only upload PDF/DOC/XLS/PNG/JPG": "", "You can only upload PDF/EXCL/DOC/XLS/PNG/JPG": "", "You can only upload PDF/PNG/JPG/TIFF/ZIP": "", "You can only upload XLS": "", "You can only upload xlsx": "", "You can only upload XLSX": "", "You can query out the task in UW query after being withdrawn . But no further action allowed. Confirm to withdraw it?": "", "You can query out the task in UW query after being withdrawn . But no furthur action allowed. Confirm to withdrawn it?": "", "You have been assigned {X} tasks": "", "You have changed master policy information and will impact uploaded vehicle data. Do you want to re-upload the vehicle list?": "", "You have some proposals under the same relation number. You can select the corresponding proposals and issue them together.": "", "Zip Code": ""}