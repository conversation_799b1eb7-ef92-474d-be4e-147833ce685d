{"": "", " has entered manual underwriting, with the underwriting case number": "査定番号付きでマニュアル査定が入力されました。", " has not successfully passed the automatic {{rule}} check and has been declined.": "", " has not successfully passed the automatic underwriting check and has been declined.": "", ".pdf, .xls, .xlsx, .png, .jpg, .jpeg, .doc, .docx": "", "(Group Policy No. {{groupPolicyNo}})": "", "(wording waiting to be provided)": "データ入力待ち", "{{ objectType }} Info": "", "{{ stage }} Stage": "{{ stage }} ステージ", "{{action}} UW Authority Configuration": "{{action}} 査定権限設定", "{{currentConditionEnumKey}} is not existed in factorEnums.": "{currentConditionEnumKey}}はファクター列挙に存在しません。", "{{docTypes}} is a multi-version file": "{{docTypes}}は複数バージョンのファイルで、デフォルトでは最新バージョンをダウンロードします。確認してください。", "{{elapsedDay}} Days Elapsed": "{{elapsedDay}}日経過", "{{fileName}} Log": "{{fileName}} ログ", "{{first}}_{{second}}": "{{-first}}_{{-second}}", "{{frequency}} {{type}}": "{{frequency}} {{type}}", "{{index}} Order No. {{orderNumber}}": "{{index}} 取引番号 {{orderNumber}}", "{{index}}. Location": "", "{{index}}. Location: {{title}}": "", "{{index}}. New Object": "", "{{index}}. Object: {{title}}": "", "{{levelname}} already exists": "{{levelname}} はすでに存在します", "{{mode}} Claim Stack Definition": "", "{{mode}} Details": "詳細{{mode}}", "{{mode}} Invoice Details": "{{mode}} 帳票の詳細", "{{name}} company": "", "{{name}} Company": "{{name}} 会社", "{{name}}_v{{version}}": "{{name}}_v{{version}}", "{{objectKeyName}}: {{objectNumber}}": "", "{{objectName}} {{index}}": "", "{{objectTitle}} Info": "", "{{premiumType}} (including tax and discount)": "{{premiumType}}（税・割引込み）", "{{premiumType}} (without tax and discount)": "{{premiumType}}", "{{prev}}-{{next}}": "{{prev}} - {{next}}", "{{rate}}{{separator}}{{date}}": "", "{{role}} Info": "{{role}} 情報", "{{ruleTitle}} Generation Rule": "", "{{startDate}} ~ {{endDate}}": "{{startDate}} ~ {{endDate}}", "{{text}} %": "", "{{type}}: {{No}}": "{{type}}: {{No}}", "{Team Name} is not assigned any strategy, Confirm to submit?": "{{ teamNames }}にストラテジーが割り当てられていません。提出しますか？", "%": "", "+ Add": "+追加", "+ Add loading/Discount": "", "+ Add New": "+新規追加", "+ Add New Master Agreement": "+包括契約新規追加", "+ Add New Rule": "", "+ Add New Team": "", "+ Add Rule Condition": "", "+ Upload": "", "< Back to Search": "", "< Back To task pool": "", "1st Screening Result": "第1次審査結果", "2nd Screening Result": "第2次審査結果", "A general rule without a specified Goods already exists. Please select a Goods.": "", "Abbreviation Name": "略称名", "Accept": "", "Acceptance_in_Progess": "受付中", "Acceptedby": "受付承認者", "Accident Degree": "事故のレベル", "Accident Number in Last 3 Year": "過去3年間の事故件数", "Accident Number Last Year": "前年度事故件数", "Accident Summary": "", "Account": "口座情報", "Account Balance": "口座残高", "Account Holder Name": "アカウントホルダーネーム", "Account Info": "口座情報", "Account Name": "口座名義", "Account No.": "口座番号", "Account Number": "口座番号", "Account Transaction Details": "アカウントトランザクション詳細", "Account Transaction Type": "アカウントトランザクションタイプ", "Account Type": "預金種類", "Accumulated days from all pending proposal status": "保留中の申込という状況からの累積日数", "Accumulated RB Allocation Amount": "積立増額配当金", "Accumulated Value": "合計で計算", "Action(s)": "", "Activate": "アクティブ", "Activate Case": "", "Activate Case On": "ケースをアクティブにする", "Activate\\Inactivate Flag": "", "Activate\\Inactivate History": "アクティブ／非アクティブ履歴", "Actual Arrival Time": "実際の到着時間", "Actual Delivery Time": "配送時間", "Actual Departure Time": "実際の出発時間", "Actual Payable Amount": "実際の支払可能額", "Actual Premium": "実際の保険料", "Actual SA": "", "Actual Total No. of Vehicles {{number}}": "実際の総車両数{{number}}", "Actual Total Premium {{totalPremium}}": "実際の保険料総額 {{totalPremium}}", "Ad hoc Single top up": "臨時一括払特約", "Ad-hoc Notification": "臨時通知", "Ad-hoc Single Top Up": "臨時一括払特約", "Add": "新規", "Add a Condition": "条件を追加", "Add a Reminder": "リマインダー日数の増加", "Add Account Info": "口座情報の追加", "Add Attachment Type": "添付ファイルタイプの追加", "Add Comments": "新規コメント追加", "Add Employee Category": "", "Add Extra Loading": "特別保険料を追加", "Add Factors": "因子を追加します", "Add Insured Object": "", "Add Location Based Object": "", "Add Location-based Object": "", "Add New": "追加", "Add New Comments": "コメントを追加する", "Add New Level": "新しいレベルを追加", "Add New Master Agreement": "", "Add New Members": "新しいメンバーの追加", "Add New Organization": "新しい法人を追加する", "Add New Proposal": "", "Add New Quotation": "新しい見積の追加", "Add New SME Application": "新しいSME申込の追加", "Add New Strategy": "新しい方策の追加", "Add New Team": "新しいチームの追加", "Add New Transaction": "新しい取付の追加", "Add Product": "商品を追加", "Add/Update Time": "", "Additional Equipment": "追加装備", "Additional Limit & Deductible Info": "", "ADDITIONAL_LIMIT_DEDUCTIBLE": "", "Address": "住所情報", "Address Info": "住所情報", "Address Type": "住所タイプ", "Adj Annual Prem": "調整後の年間保険料", "Adj Annual Prem (Rate)": "調整後の年間保険料（率）", "Adj Annual Prem（Rate）": "", "Adj Net Prem": "", "Adj Net Premium": "調整後の純保険料", "Adjusted Net Premium": "調整後の純保険料", "Adjustment Comments": "調整コメント", "Adjustment Date": "調整日", "Adjustment Information": "調整情報", "Adult Number": "大人数", "Advanced Payment Amount": "前払い保険料", "After": "後", "After add new authority level, you need to define the grade of the level. From top to bottom, the level grade is from lowest to highest.": "新しい権限レベルを追加した後、レベルの等級を定義する必要があります。上から下に向かって、レベルの等級は最下位から最上位になります。", "After modifying the information, it needs to be recalculated. Please confirm.": "", "Age": "年齢", "Age: {{age}}": "年齢: {{age}}", "Agency": "代理店", "Agency Name": "代理店名", "Agency: {{agencyName}}": "", "Agent": "代理店", "Agent Code": "代理店コード", "Agent Name": "代理店名", "Aggregate Amount": "", "Aggregated Amount (Applying)": "通算額（申込中）", "Aggregated Amount (Inforce)": "通算額（有効済）", "Aggregated Amount (Total)": "通算額（合計）", "Agreement Settlement Rule": "包括契約精算ルール", "Airline Company": "航空会社", "Alert": "アラート", "All": "全て", "All designated beneficiary information will be deleted. Please Confirm.": "", "All goods": "全ての商品", "All goods categories": "全ての商品カテゴリ", "All Information in current table will be deleted after switch, Individual & Organization could not co-exist. Are you sure to delete?": "顧客タイプを切り替えると、既存の顧客データがクリアされますので、確認してください。", "All questions have been answered.": "すべての質問は解決しました。", "All upload data you currently submit will be recorded. If the file verification failed, it will not be submitted.": "", "All upload data you currently submit will be recorded. If the file verified failed, it will not be saved.": "", "All upload data you currently submit will be recorded.If the file verification failed,it will not be submitted.": "", "Allocation": "配分率", "Allocation Amount": "配分率金額", "Allocation Date": "配分率日", "Allocation Frequency": "配分頻度", "Almost!": "ほとんど！", "Amount": "金額", "Amount Name": "", "Ancestor Policy No.": "初回契約番号", "Annual Prem": "年間保険料", "Annual Prem. Methods": "", "Annual Premium": "", "Annual Standard Planned Premium": "年間標準予定保険料", "Annual Standard Premium": "年間標準保険料", "Annualized Premium / Single Premium": "年換算保険料／単一保険料", "Annualized Regular Premium": "年換算定期保険料", "Annuity": "年金", "Annuity Allocation History": "", "Annuity Amount": "年金額", "Annuity Defer Period Type": "年金繰下げ期間タイプ", "Annuity guarantee Period": "年金支払保証期間", "Annuity Info": "年金情報", "Annuity liability": "", "Annuity Payment Account": "年金支払口座", "Annuity Payment Frequency": "年金の支払い頻度", "Annuity Payment Option": "年金の支払い方法", "Annuity Payment Period Type": "年金支払期間の種類", "ANNUITY_INFO": "", "Applicable": "有効", "Application": "", "Application Date": "申込日", "Application Item": "", "Application No": "アプリケーション番号", "Application No {{ appNo }}": "アプリケーション番号", "Application No.": "申出番号", "Application Type": "申請タイプ", "Applied to waive premium liability by POS": "POSによる保険料免除に申請しました", "Applied Withdrawal Amount": "申請引き出し金額", "Appliedby": "申請者", "Apply Master Plan": "", "Apply to all products": "全ての製品に適用します", "Apply to partial paid proposal?": "一部有料申込に申請しますか", "Appointment Rate": "ファンド配分", "Approval": "承認", "Approval Comment": "承認コメント", "Approval Date": "承認日", "Approval History": "承認履歴", "Approval Result": "承認結果", "Approval Time": "承認時間", "Approval_in_Progess": "承認中", "Approve": "承認", "Approvedby": "承認者", "Are you sure to activate this case?": "本当にこのケースをアクティブにしますか？", "Are you sure to cancel?": "キャンセルしてよろしいですか？", "Are you sure to change plan?": "", "Are you sure to clear all the upload records?": "", "Are you sure to delete current level?  If delete, the all level order will change.": "", "Are you sure to delete selected type? It will remove all files under this type.": "選択したファイルタイプを削除しますか？このタイプのファイルをすべて削除されます。", "Are you sure to delete the level?": "このレベルを削除してよろしいですか？", "Are you sure to delete the level?  Current authority level is in force.": "", "Are you sure to delete the level? Current authority level is in force.": "", "Are you sure to delete this comment?": "", "Are you sure to delete this document?": "このファイルを削除してもよろしいですか?", "Are you sure to delete this product?": "", "Are you sure to delete this task?": "このタスクを削除してもよろしいですか？", "Are you sure to delete?": "削除してもよろしいですか？", "Are you sure to remove it?": "", "Are you sure to submit current authority configuration? After submitting, the status of level will become effective.": "", "Are you sure you want to approve the insurance application?": "", "Are you sure you want to decline the insurance application?": "", "Are you sure you want to release this UW task?": "", "Are you sure you want to return the application for the current product?": "", "Are you sure you want to return the entire submission?": "", "Are you sure you want to save the current sort?": "現在の並び順を保存してよろしいですか？", "Are you sure you want to submit this insurance application?": "", "Arrival Airport": "到着空港", "Arrival Delay Time": "到着遅延時間", "Assign": "任務の割り当て", "Assignee": "譲受人", "ASSIGNEE": "譲受人", "Assignee Type": "譲渡タイプ", "Associated goods": "関連販売商品", "Associated Policy": "関連保険証券", "Associated Proposal": "関連のある保険申込書", "Associated UW Task": "", "Associated UW Task Application No.": "譲渡査定タスク申請番号", "At least one item must be created under the address, please check.": "住所内に少なくとも1つの標的を作成する必要があります。ご確認ください。", "At least one should be selected": "", "at_least_one_input": "少なくとも一つの検索項目を入力ください", "Attached To": "付加した対象商品: {{ productName }}", "Attached To Product": "", "Attachment": "添付ファイル", "ATTACHMENT": "添付ファイル", "Attachment Configuration": "添付資料設定", "Attachment Quotation Stage": "見積添付段階", "Attachment Type": "添付ファイルタイプ", "ATTACHMENT_BUNDLE": "添付ファイル", "Attachment/Mandatory": "添付ファイル/必須", "Attachments": "添付", "Authority Configuration": "権限設定", "Authorization Agreement": "権限承諾書", "Authorization Agreement No.": "権限承諾書番号", "Authorization Agreement Signing Date": "権限承諾書署名日", "Auto": "自動", "Auto compliance rule result": "自動コンプライアンスルールのキャリブレーション結果", "Auto Compliance Rule Result": "", "Auto Rebalancing": "自動リバランス", "Auto Rebalancing Close": "自動リバランス終了", "Auto Rebalancing Opened": "自動リバランス開始", "Auto Renewal": "自動継続", "Auto UW Rule Result": "自動査定結果", "Auto Verification Rule Result": "自動申込査定結果", "AUTO_UW_RULE_RESULT_V2": "自動査定結果", "Automated Fill Condition": "", "Automatic Compliance Result": "", "Automatic Premium Loan": "APL履歴", "Automatic Premium Loan Detail": "APL履歴詳細", "Automatic Underwriting Result": "", "Autopay Flag": "自動支払フラグ", "Average Driving Distance": "平均走行距離", "Back": "戻る", "Back to Edit": "編集に戻る", "Back to main page": "メインページへ戻る", "Back to Modify": "", "Back to Search": "検索に戻る", "Back To task pool": "申込書検索画面に戻る", "Back To Worksheet": "ワークシートに戻る", "Bank Account No.": "銀行口座番号", "Bank Address": "銀行所在地", "Bank Branch Address": "銀行支店所在地", "Bank Branch Code": "銀行支店コード", "Bank Branch Name": "支店", "Bank Code": "銀行コード", "Bank Code/Bank Name": "銀行コード/銀行名", "Bank Name": "金融機関名", "Bank Transfer / Account Type": "銀行振替/口座タイプ", "Based on Calendar Days": "", "basic info": "基本情報", "Basic Info": "基本情報", "Basic Info Info": "基本情報", "BASIC_INFO": "基本情報", "BASIC_INFO_VIEW": "基本情報", "BASIC_INFO_WITH_STAND_ALONE": "", "BASIC_INFO_WITHOUT_GOODS": "基本情報", "Batch list Goods": "保険販売商品", "Batch List Upload": "バッチリストのアップロード", "Batch List Upload History": "バッチリストアップロード履歴", "Batch Number": "バッチ番号", "Batch Reassign": "", "Batch Uploading": "バッチアップロードディング", "BCP": "", "Be Attached By": "付加された対象商品: {{productNameList}}", "Before (Rate/Amount)": "前（レート／金額）", "Before Tax & Service Fee": "税およびポイントを含まない", "Beneficial Owner": "実益所有者", "BENEFICIAL_OWNER": "実益所有者", "Beneficiary": "保険金受取人", "BENEFICIARY": "保険金受取人", "Beneficiary Ratio": "受取割合", "Beneficiary ratio is": "保険金受取比率は", "Beneficiary Type": "", "BENEFICIARY_CN": "保険金受取人", "BeneficiaryRatio": "受取人割合(%)", "beneficiaryRatioTooltip": "すべての受取人の受益割合の合計は100％である必要があります。ご確認ください。", "Benefit": "保険給付金", "Benefit Account Balance": "保険給付金口座残高", "Benefit Allocation History": "保険給付金配分率履歴", "Benefit Amount": "保険給付金額", "Benefit info": "契約利益情報", "Benefit Info": "保険給付詳細", "Benefit Next Due Date": "保険給付金次回支払日", "Benefit Option": "", "Benefit Payment Account Info": "保険給付金支払口座情報", "Benefit Schedule": "保険給付金予定日", "BENEFITS_INFO": "", "Bill Amount": "請求額", "Bill Information": "請求書情報", "bill_amount_details": "請求額詳細", "Bill_No": "請求書番号", "Bind Task Assignment Rule": "タスク割当ルールのバインド", "Birthday": "生年月日", "blacklist_search_result_unit": "件", "Body Type": "車型", "BONUS": "", "Bonus Allocation History": "配当履歴", "Bonus Amount": "配当金額", "Bonus Code": "配当金コード", "Bonus Info": "ボーナス情報", "Bonus Name": "配当金名", "Bonus Next Due Data": "", "Bonus Next Due Date": "配当次回支払日", "Bonus Payment Account Info": "配当支払口座情報", "Bonus Total Balance": "配当残高", "Bonus Type": "配当種類", "Bonus/malus": "ボーナス/マルス", "Booking Number": "予約番号", "Booking Time": "予約日", "Both": "両方", "BOTH_GOODS_CANNOT_BE_ISSUED_SIMULTANEOUSLY {{primaryGoods}}{{secondaryGoods}}": "{{secondaryGoods}} は {{primaryGoods}} に依存しています。{{secondaryGoods}} を削除するには、まず {{primaryGoods}} を削除してください。", "Branch Name: {{name}}": "支店名: {{name}}", "Building Info": "建物詳細情報", "Building Size": "建物の面積", "Building Type": "建物タイプ", "Building up the File, please wait patiently.": "ファイルを出力中です。少々お待ちください。", "Built Year": "構造年", "Burglar Alarm": "盗難警報器有無", "Business Activity/Sector": "ビジネス活動/セクター", "Business License No.": "ビジネスライセンス番号", "Business Month": "ビジネス月", "Business No": "ビジネス番号", "Business No. Generation Rule": "ビジネス番号採番ルール", "Business No. n Generation Rule": "", "Business Scenario": "", "Business Transaction No": "", "Business Transaction No.": "ビジネストランザクション番号", "Business Type": "ビジネスタイプ", "Buy/Sell": "買売方式", "By Commission Category": "手数料カテゴリ別", "By Product": "商品別", "BY_EVENT_INFO": "", "by:": "", "By: userName": "By: {{userName}}", "By:{{creator}}": "", "ByEvent_Policy_Data": "イベント別ポリシーデータ", "Calculate": "計算", "Calculate successfully": "", "Calculate Tax when Clear Account": "アカウントをクリアする時、税を計算する", "Calculation Basis": "計算ベース", "Calculation Date": "", "Calculation Direction": "計算説明", "Calculation Frequency": "計算頻度", "Calculation Level": "計算レベル", "Calculation Order": "計算順序", "Calculation/Capitalization Period": "計算期間", "Campaign": "キャンペーン", "Campaign Discount": "キャンペーン割引", "Campaign Discount Type": "キャンセル割引タイプ", "Campaign info": "キャンペーン詳細", "Campaign Info": "キャンペーン詳細", "Campaign Type": "キャンペーンタイプ", "Campaign_code": "キャンペーンコード", "Campaign_name": "キャンペーン名", "Cancel": "キャンセル", "Cancel pin": "", "Canceled": "", "Cancelled": "取消", "Car Owner": "車の所有者", "Car Owner & Driver & Renter": "車の所有者＆運転者&レンター", "Car Owner Birthday": "車の所有者の生年月日", "Car Owner Gender": "車の所有者の性別", "Car Owner ID Number": "車の所有者のID", "Car Owner ID Type": "車の所有者のID　タイプ", "Car Owner Name": "車の所有者の氏名", "Car Owner Name2": "", "Car Owner Name3": "", "Carring Goods Type": "キャリンググッズタイプ", "Case Operation": "ケース操作", "Case Owner": "", "Cash Before Cover": "", "Cash Bonus": "現金ボーナス", "Cash Bonus Account Transaction Details": "現金ボーナス口座トランザクション詳細", "Cash Bonus Allocation Details": "現金ボーナス配分詳細", "Cash Value Saving Account": "現金ボーナス保存口座", "Change Payment Info": "支払情報の変更", "Change Principal": "", "Change the current process flow from the process flow template": "", "Change Underwriter": "", "Changes made after submission will not be revoked.": "", "Channel": "チャネル", "Channel Name": "チャネル名", "Channel User No.": "チャネルのユーザー番号 {{channelUserNo}}", "channelCode": "チャネルコード", "channelCode{{channelCode}}": "チャネルコード{{channelCode}}", "channelUserNo": "チャネルのユーザー番号", "channelUserNo{{channelUserNo}}": "チャネルのユーザー番号 {{channelUserNo}}", "Charge Amount": "", "Charge Code": "", "Charge Due Date": "", "Charge Period": "", "Charge Type": "", "Chassis No.": "シャーシ番号", "Check relatives or not": "親族の確認", "Check Segment": "", "Check staff is on duty or not": "スタッフ勤務状況の確認", "Children Number": "子ども人数", "City": "都市", "Claim": "保険金請求", "CLAIM": "保険金請求", "Claim Amount": "支払金額", "Claim Compensation": "支払金額", "Claim Experience": "保険金請求経験", "Claim History": "保険金請求履歴", "Claim Name": "", "Claim No": "事故番号", "Claim No.": "事故番号", "Claim Number in Last 3 Year": "過去3年間の保険金請求件数", "Claim Number Last Year": "前年度保険金請求件数", "Claim Ratio": "保険金請求比率", "Claim Stack": "クレームスタック", "Claim Status": "保険金請求状態", "Claim Summary": "", "Claim Type": "保険金請求タイプ", "Claim Waive": "保険金請求免除", "Claim_Archives_Room": "保険金請求履歴", "CLAIM_DETAILS": "保険金請求詳細", "CLAIM_HISTORY": "保険金請求履歴", "CLAIM_INFO": "保険金請求", "Claimable Period": "保険期間", "Clause Information": "特約情報", "Clear all": "", "Clear All": "", "Clear successed!": "", "Clear Successfully": "", "Click Add another type": "", "Click on the policy version number below to switch policy version information.": "", "Click or drag file here area to upload": "ファイルはこのエリアにクリックまたはドラッグしてアップロードします", "Click or drag file here to upload": "こちらにクリックまたはドラッグしてファイルをアップロード", "Click or drag the file here to upload": "こちらでファイルをクリック、またはドラッグしてアップロードしてください。", "close": "閉じる", "Close": "閉じる", "Closed": "", "Code": "コード", "Code No. {{code}}": "コード番号{{code}}", "Collapse": "折りたたむ", "Collapse All": "すべて折りたたむ", "Collected Amount(Original Currency)": "入金金額（原通貨）", "Collected Amount(Policy Currency)": "入金金額（契約通貨）", "Collection & Refund": "入出金情報", "Collection Amount Detail": "入金金額詳細", "Collection Method": "入金方法", "COLLECTION_AND_REFUND": "", "collection_and_refund_amount": "入出金額", "collection_or_refund_amount": "入出金額", "collection_refund_amount_details": "入出金額の詳細", "collection_refund_item": "入出金項目", "Color of Plate No": "ナンバープレートの色", "Combination Sequence": "組み合わせ順序", "Comment": "コメント", "COMMENTS": "コメント", "Comments: {{remark}}": "", "Commision": "", "Commission": "手数料", "Commission (On Top of Premium)": "", "Commission & Service Fee": "手数料・サービス料", "Commission Details": "手数料詳細", "Commission Generated Date": "手数料生成日", "COMMISSION_AGENT": "手数料担当者", "COMMISSION_AND_SERVICE_FEE": "", "Commission（On Top of Premium)": "手数料（グロス）", "CommissionAmount": "手数料金額", "CommissionType": "手数料種類", "Commodity Information": "", "Common Info": "共通情報", "Compaign Discount": "", "Company Name": "会社名", "Compiance History": "", "Complete": "受付完了", "Complete Date": "完了日付", "Complete Time": "", "Completed Date": "完了日", "compliance": "compliance", "Compliance": "コンプライアンス", "Compliance Check": "コンプライアンスチェック", "Compliance Decision": "応諾決定", "Compliance Decision Details": "Compliance Decision Details", "Compliance History": "", "Compliance Info": "準拠情報", "Compliance Result": "", "Compliance Status": "コンプライアンス状況", "Compliance Task No.": "準拠タスク番号", "Compliance Type": "", "COMPLIANCE_INFO": "", "COMPLIANCE_RESULT": "コンプライアンス結果", "Concurrent Case": "", "CONCURRENT_CASE": "コンプライアンスケース", "Conditional Accepted": "条件付き受諾", "Configure the Task Assignment Rule first": "まずタスク割当ルールを設定してください。", "Confim to delete this plan?": "", "Confirm": "確認", "Confirm Application": "アプリケーションの確認", "Confirm Date": "日付を確認する", "Confirm to clear all data?": "", "Confirm to delete": "", "Confirm to delete current document?": "現在のファイルを削除してもよろしいですか?", "Confirm to delete?": "削除を確定しますか？", "Confirmation Date": "確認日", "Confirmation Required": "", "Confirmed Fund Price": "ファンド価格の確認", "Consentee": "同意者", "CONSENTEE": "同意者", "Contact Address": "住所", "Contact Person": "連絡人", "Contact Person Info": "連絡者情報", "Contact Phone Info": "連絡電話情報", "ContactPerson": "", "content": "", "Content": "内容", "Continue to Submit": "引き続け提出します", "Contract Date": "貸付契約日", "Contract Number": "契約番号", "Copy": "コピー", "copy failed": "", "Copy from": "", "Copy from {{tabName}} Product": "", "Copy from Motor Product": "", "Copy Master Agreement": "マスター約定のコピー", "Copy master agreement  to Relational Policy": "", "Copy successful!": "", "Copy Successfully": "", "Copy To": "", "Copy to New Product": "商品のコピー", "Copy to New Version": "新バージョンへコピー", "Country": "国", "Country Code": "国コード", "Country of Residence": "", "Coverage": "補償", "Coverage / Sub Coverage": "", "Coverage Detail": "補償詳細", "Coverage Details": "", "Coverage Info": "", "Coverage Level": "", "Coverage Period": "保険期間", "Coverage Period Type": "補償期間の種類", "Coverage Period Value": "保証期間の値", "Coverage Plan": "補償プラン", "Coverage plan cannot be matched due to incompleted information, please complete the object information first.": "", "Coverage Plan Details": "", "Coverage plan will be refreshed, please confirm.": "", "Coverage Total Prem": "補償の総保険料", "COVERAGE_GOODS_DETAIL_BUNDLE": "", "COVERAGE_INFO": "補償情報", "COVERAGE_INFO_DRAWER_CLAIM_STACK": "クレームスタック", "COVERAGE_INFO_DRAWER_COVERAGE_DETAILS": "補償詳細", "COVERAGE_INFO_DRAWER_DCA_ARRANGEMENT": "ドルコスト平均法プラン", "COVERAGE_INFO_DRAWER_FUND_APPOINTMENT": "ファンド配分比率", "COVERAGE_INFO_DRAWER_LIABILITY": "補償内容", "COVERAGE_INFO_DRAWER_PORTFOLIO_REBALANCING": "リバランス", "COVERAGE_INFO_DRAWER_PREMIUM_INFO": "保険料情報", "COVERAGE_INFO_DRAWER_RETIREMENT_OPTION": "定年計画", "COVERAGE_INFO_DRAWER_TOP_UP": "増額", "COVERAGE_PLAN": "補償プラン", "COVERAGE_PLANS": "補償プラン", "COVERAGE_PREMIUM": "", "COVERAGE_PREMIUM_BUNDLE": "", "Create Date": "作成日", "Create Policy under Master Agreement": "包括契約に基づき子契約を作成", "Create task assignment rule to match {{module}}.": "{{module}}にマッチするタスク割当ルールを作成します。", "Create team and bind task assignment rule. The {{module}} hit binded rule will be pushed into this team.": "チームとタスク割当ルールを作成します。バインドされた{{module}}ルールはこのチームにプッシュされることとなります。", "create time": "作成日時", "Creation Date": "作成日時", "Creator": "作成担当者", "Currency": "通貨", "currency_amount_Ap": "{{currency}} - {{amount}}", "currency_combine_amount": "{{currency}} {{amount}}", "Current Handler": "現在担当者", "Current Policy Change Overview": "", "Current Policy Overview": "", "Current Premium Amount": "現在の保険料額", "Current process flow": "", "Current system date": "現在のシステム日付", "Current Underwriting Level": "現行引受レベル", "Current Version": "", "Currently policyholder & insured are different person. But after the change, we find their info is the same and system will treat them as one person. Confirm to merge them?": "変更前は契被別人、変更後は契被同人になります、顧客を合併しますか？", "currentOperator": "現在のオペレーター", "Customer": "顧客", "Customer Grade": "顧客グレード", "Customer ID Number": "", "Customer List": "顧客リスト", "Customer Screening Result": "顧客審査結果", "Customer Type": "顧客カテゴリー", "Customer type not selected yet. Please confirm.": "まだ顧客タイプが選択されていません。ご確認ください。", "CUSTOMER_INFO": "顧客情報", "CUSTOMER_PROFILE": "", "Daily": "毎日", "Data Entry in Progress, Pending Proposal Check, Waiting for Issuance": "入力中、査定待ち、有効待ち", "Data is modified but not saved. Do you want to save the modified content?": "データは変更されましたが、保存されていません。修正した内容を保存しますか?", "Date of birth": "生年月日", "Date Of Birth": "生年月日", "Date Of Witness": "証人の日付", "Date Quote Needed": "見積もり必要日", "DAY": "日", "Day(s)": "日", "Days": "日数", "DayS": "日数", "Days Types": "", "DCA": "", "DCA Amount (for each period)": "ドルコスト平均法金額（分割払いごとに）", "DCA Arrangement": "ドルコスト平均法プラン", "DCA Frequency": "ドルコスト平均法頻度", "Death Date: {{dateOfDeath}}": "死亡日付: {{date<PERSON>fDeat<PERSON>}}", "Debit Note Amount": "請求書金額", "Debit Note Information": "請求書情報", "Debit Note Information ": "", "Debit Note No.": "請求書番号", "Debit Note No. No. {{debitNoteNo}}": "請求書番号 {{debitNoteNo}}", "Deceased": "死者", "Decision": "決定", "Decision Details": "決定詳細", "Decision Fail Decline": "最新の保険申込情報に基づくと、この保証ケースの提出は保険の拒否を招くことになりますので、確認してください。", "Decision Failed": "", "Decision Reason": "決定理由", "Decision Successfully": "", "Decision Time": "", "Declaration Date": "告知確認日", "Declaration Stage": "告知段階", "Declaration_Information": "健康告知情報", "Decline": "", "Deducted From Investment": "投資口座からの引き落とします", "Deductible": "免責金額", "Deductible Amount": "控除額", "Deductible Info": "", "Deduction Source": "控除ソース", "Deduction Type": "控除タイプ", "Defer Period": "繰下げ期間", "Delete": "削除:", "Delete success": "削除は成功しました。", "Delete Successfully": "", "Deleting a goods will also delete all corresponding data, please confirm.": "商品を削除すると、すべての対応するデータも同時に削除されます。確認してください。", "Delivery Status": "配送状況", "Department Code": "", "Departure": "出発地", "Departure Airport": "出発空港", "Departure City": "出発地（市）", "Departure Country": "出発地（国）", "Departure Delay Time": "出発遅延時間", "Departure Point": "Departure Point", "Departure Time Zone": "出発地タイムゾーン", "Deposit Account Balance": "預金口座残高", "Description": "説明", "Designated Beneficiary": "", "Designated Beneficiary :": "", "Designated beneficiary ratio must equal to 100%": "", "Destination": "旅行先", "Destination City": "旅行先（市）", "Destination Country": "旅行先", "Destination Region": "旅行先（エリア）", "Destination Time Zone": "旅行先タイムゾーン", "detail": "詳細", "Details": "詳細", "Device Abs": "デバイスABS", "Device AEB": "デバイスAEB", "Device Airbag": "デバイスエアバッグ", "Device Alarm": "デバイスアラーム", "Device Brand": "デバイスブランド", "Device Buyer ID": "デバイス購入者ID", "Device Buyer Review Score": "デバイス購入者レビュースコア", "Device Category": "デバイスカテゴリ", "Device Description": "デバイス説明", "Device Gear or steering lock": "デバイスギアまたはステアリングロック", "Device GPS": "デバイスGPS", "Device ID": "デバイスID", "Device Immobiliser": "デバイスイモビライザー", "Device Info": "デバイス詳細", "Device Installed": "デバイス実装済み", "Device Manufacturer": "デバイスメーカー", "Device Market Value": "デバイスの市場価格", "Device Model": "デバイスモデル", "Device Name": "デバイスID名", "Device Number": "デバイス数", "Device Perchuase Time": "デバイスの購入時間", "Device Price": "デバイス価格", "Device Seller ID": "デバイス販売者ID", "Device Seller Review Score": "デバイス販売者レビュースコア", "Device Status": "デバイスステータス", "Device Tracking": "デバイス追跡装置", "Device User": "デバイスユーザー", "Digit Length": "桁数の長さ", "Digit Position": "", "Direct sales": "ダイレクトセールス", "Disbursement Method Information": "支払方法情報", "Discount": "割引", "Discount Amount": "割引金額", "Discount Period (Premium Due No)": "割引期間", "discount_type": "割引タイプ", "Distance Confirmation Date": "距離確認日", "Distribution Method": "配当方法", "Ditrh of birth": "せいねんがっぴ", "Dividend Payment Method": "配当金の支払方法", "DOB": "生年月日", "Document": "", "Document Generation": "ドキュメント生成", "Document Generation Management": "書類生成管理", "Document Name": "ドキュメント名", "Document Name {{currentImgTitle}}": "ドキュメント名: {{currentImgTitle}}", "Document Type": "書類タイプ", "Document({{total}})": "ドキュメント({{total}})", "Documents": "ファイル", "Dollar Cost Averaging": "ドルコスト平均法", "Don't Need Reminder": "リマインダーのクローズ", "Don't Use Sub-item": "サブアイテムを使用しない", "Down Sell SA": "", "Download ({{size}})": "ダウンロード ({{size}})", "Download {{size}}": "ダウンロード {{size}}", "Download & Send Password": "ダウンロード＆パスワード送信", "Download All": "すべてダウンロード", "Download All Event Policies": "すべてのイベント保険契約をダウンロード", "Download E-Policy": "", "Download failed": "ダウンロードに失敗しました", "Download successfully": "ダウンロードに成功しました", "Download Template": "テンプレートダウンロード", "DRAFT": "下書き", "Drive ID Type": "運転者IDタイプ", "Driver": "運転者", "Driver Birthday": "運転者生年月日", "Driver Experience": "運転歴", "Driver Gender": "運転者性別", "Driver ID Number": "運転者ID番号", "Driver IdNumber": "運転者ID番号", "Driver IdType": "運転者ID種類", "Driver Industry": "運転者の業界", "Driver Information": "運転手情報", "Driver License Number": "運転免許証番号", "Driver License Registration Date": "運転免許登録日", "Driver Marital Status": "運転者結婚状態", "Driver MaritalStatus": "運転者結婚状態", "Driver Name": "運転者氏名", "Driver Name2": "", "Driver Name3": "", "Driver Occupation": "運転者の職業", "Driver Tier": "運転者レベル", "Driver Type": "運転者種類", "Driving Distance": "走行距離", "Driving Experience": "運転経験", "Driving License No.": "運転免許証番号", "Driving License Registration Date": "", "Driving Licensen No.": "", "Due Date": "応当日", "due to the change of insured information": "に変更する", "Due Unpaid Premium": "未払保険料", "Duplicate configuration, please check.": "設定が重複しています。確認してください。", "Duplicate Master Agreement No.": "重複したマスター契約番号", "Duplicated Tag": "重複タグ", "Duration": "", "E-mail": "", "E-mail Info": "メール情報", "E-policy": "電子契約", "Each product chooses at least one liability": "各製品は少なくとも一つの責任を選択します。", "EB_COVERAGE_GOODS_DETAIL": "", "EB_COVERAGE_PREMIUM": "", "EB_INSURED": "", "Edit": "編集", "Edit by": "編集者", "Edit By": "", "Edit Document": "", "Edit Extra Loading": "", "Edit Name": "", "Edit Now": "編集する", "Edit Process Flow": "", "Edit Strategy": "方策編集", "Edit Team": "チーム編集", "Editing": "編集", "effective": "有効", "Effective": "有効", "Effective Date": "有効日", "Effective Date Time Zone": "運転免許証番号", "Effective Period": "有効期間", "Effective Sub Policy": "", "Effective Time": "有効日時", "EffectiveDate": "有効日", "ELECTRONIC_EQUIPMENT": "", "Email": "メール情報", "Employee Category": "", "Employee Category {{No}}": "", "EMPLOYEE_GROUP": "", "End": "", "End Day": "終了日", "End Time": "通知終了時間", "Engine No.": "エンジン番号", "Enrollment Transaction": "", "Entry Time": "アクセス時間", "Error": "", "Error File": "エラーファイル", "Error happened during upload.": "ファイルアップロードエラー", "Error Notification": "", "Escalate": "エスカレート", "Escalate or Reassign": "エスカレートまたは再割当", "Escalate Stage": "エスカレート段階", "Escalate Successfully!": "エスカレーションに成功しました！", "Escalation Task": "エスカレーションタスク", "esclated/esclate": "エスカレート", "Estimate Lapse Date": "予定失効日", "Estimated Total  Premium": "", "Estimated Total No. of Vehicles": "推定総車両数", "Evaluatedby": "査定者", "Evaluation Decision": "評価決定", "Evaluation_in_Progess": "査定中", "Event Policy Issue Switch": "イベントポリシーの発行スイッチ", "Event Policy No.": "イベント加入者番号", "Event Policy Upload": "子契約アップロード", "Event Policy Upload History": "子契約アップロード履歴", "Every {{frequency}}": "各{{value}} {{unit}}", "Examination Date": "診察日", "Examination Description": "", "Examination Result": "", "Exchange Rate": "為替レート", "Exchange Rate (PremCur/BaseCur)": "", "Exchange Rate (SaCur/BaseCur)": "", "Exchange Rate (SaCur/PremCur)": "", "Exchange Rate Date (PremCur/BaseCur)": "", "Exchange Rate Date (SaCur/BaseCur)": "", "Exchange Rate Date (SaCur/PremCur)": "", "Excluding Promotion Discount": "プロモーション割引を除く", "Exclusion": "免責", "EXCLUSION": "免責", "Exclusion Category": "免責カテゴリ", "Exclusion Clause": "免責条項", "Exclusion Code": "除外コード", "Exclusion Content": "除外内容", "Exclusion List": "免責リスト", "Exclusion Lists": "", "Exclusion Reason": "免責理由", "Expand": "もっと見る", "Expand All": "", "Expired": "", "Expired Date": "", "Expiry": "満期", "Expiry Date": "満期日", "Expiry Date should be later than effective date": "満期日は発行日より後でなければなりません", "Expiry Date Time Zone": "満期日タイムゾーン", "ExpiryDate": "満期日", "Export": "", "Export All": "すべてをエクスポート", "Export Log": "エクスポートログ", "Export Time": "エクスポート時間", "External Name": "", "External_Doc_No": "外部文書番号", "Extra Loading": "保険料割増", "Extra Loading amount": "割増保険料額", "Extra Loading Type": "特別保険料種類", "Extra Premium": "特別保険料", "Extra Premium Due Day": "特別保険料払込天数", "Extra Setting": "追加設定", "Extract Period": "抽出期間", "Extract period was entered error, please check!": "抽出期間の入力に誤りがあります。ご確認ください。", "Extract Policy Condition": "契約条件抽出", "Fail Reason": "失敗原因", "Failed Reason": "失敗理由", "Failed Record List": "失敗データリスト", "Failed Records": "", "Falculative": "任意再保険", "fee_status": "費用ステータス", "Feedback": "", "FeeType": "費用種類", "FF Weight": "FF重量", "Fidelity Guarantee": "忠実度保証", "Field can only be digital or letter": "フィールドはデジタルまたは文字のみにすることができます", "Field Value": "", "Fields Name": "項目名", "File Creator": "作成者", "File Name": "ファイル名", "File Name: {{fileName}}": "ファイル名: {{fileName}}", "File size cannot exceed": "ファイルサイズは{{size}}Mを超えることはできません。", "File Upload Time": "アップロード時間", "Fill In": "記入", "Fill in from Vehicle List": "", "Fill in the Exisiting Customer": "既存の顧客情報を選択する", "Fill in the Exisiting Customer >>": "既存の顧客情報を選択する>>", "Fill in the existing account": "", "Fill in the Existing Account": "既存のアカウントに入力する", "Filter": "フィルター", "Final Decision": "最終決定", "Final Underwriting Level": "最終引受レベル", "FINAL_DECISION": "最終決定", "Finance": "", "FINISH_PAYMENT": "全額支払", "Fire Alarm": "火災警報有無", "First Name": "名", "first.": "", "Fold Menu": "メニューを非表示する", "Follow the Investment Strategy": "投資戦略に従います", "Follow the same appointment rate as planned premium and top ups.": "予定保険料およびトップアップと同じ配分率に従います。", "For Liability": "特約について", "For List Data": "リストデータについて", "For prior condition, it will be matched in advance whether matches any ordinary condition or not, otherwise it will be ignored. For ordinary condition, it will be matched together otherwise the task will be pushed public pool.": "事前条件の場合、通常条件に一致するかどうかにかかわらず事前に一致させます。そうでない場合は無視されます。通常条件の場合、何れに一致させます。そうでない場合、タスクはパブリックプールに移されます。", "For Product": "", "For Regular Data": "一般データについて", "For single cash value type, only one formula could be defined. No duplicate allowed.": "シングルキャッシュ タイプの場合、定義できる式は1つだけであります。 重複定義はできません。", "For Team": "チームについて", "For the same rule or rule set, only one record is allowed.": "同じルールまたはルールセットについて、一つのレコードしか許可されません。", "Formula Category": "公式カテゴリー", "Formula Code": "公式コード", "FR Weight": "FR重量", "Free amount of Liability SA": "", "Free Investment Amount": "Free Investment Amount", "Free_amount_of_liability_sa": "無料保険責任のSA", "Free_policy_info": "無料契約の情報", "Free_policy_no": "無料契約の契約番号", "Frequency": "減額頻度", "Frequency of Payment": "支払頻度", "Fronting": "業務種類", "Full Name": "フルネーム", "Full Records": "", "Fund": "ファンド", "Fund Allocation": "ファンド割り当て", "Fund Application Date": "ファンド申込日", "Fund Appointment": "ファンド配分比率", "Fund Appointment After Rebalancing": "リバランス後のファンド配分比率", "Fund Appointment Before Rebalancing": "リバランス前のファンド配分比率", "Fund Appointment For Rebalancing": "投資リバランスのファンド配分比率", "Fund Balance": "ファンド残高", "Fund Code": "ファンドコード", "Fund Currency": "ファンド通貨", "Fund Name": "ファンド名", "Fund Price": "ファンド価格", "Fund Transaction Date": "ファンド取引日", "Fund transaction details": "ファンドトランザクション明細", "Fund Transaction Details": "ファンド取引の詳細", "Fund Value": "ファンドの価値", "Fund Value After Rebalancing": "リバランス後のファンド評価額", "Fund Value Before Rebalancing": "リバランス前のファンド評価額", "G Weight": "Gウェート", "Gender": "性别", "Generate": "作成", "Generate Offer": "オファー生成", "Generate Policy Schedule": "", "Generate Premium": "保険料計算", "Generate Quotation Form": "見積書フォーム作成", "Generate/Regenerate Reason": "生成/再生成の理由", "Generated Date": "作成日", "Generation Time": "生成時間", "Get Result": "", "Gift Code": "ギフトコード", "Gift Delivery Dimension": "ギフト配送のディメンション", "Gift Delivery Method": "ギフト配送方法", "Gift Delivery Time": "ギフト配送時間", "Go to New Quote": "", "Go to Quote Bound": "", "Go to Quote Sent": "", "Goods": "商品名", "GOODS": "販売商品", "Goods cannot be empty": "商品は空であってはいけません", "Goods Category": "グッズカテゴリ", "Goods Code/GoodsName": "", "Goods in Transit": "輸送中の商品", "Goods is not on sale, please check.": "", "Goods Name": "販売商品名", "Goods Name and Model": "販売商品名とモデル", "Goods Summary": "商品概要", "Goods Version": "商品バージョン", "GoodsName": "販売商品名", "GoodsVersion": "商品バージョン", "Got it": "", "Green Card Fee": "", "Green Card No": "グリーンカード番号", "Group Level": "", "Group No": "", "Group Personal Accident": "団体個人傷害", "Group Policy": "団体契約", "Group Policy No.": "", "Group Policy No. {{groupPolicyNo}}": "", "Group Policy Query": "", "Guarantee Period": "保証期間", "HALFYEAR": "半年ごと", "has not successfully passed the automatic underwriting check and has been declined.": "", "has Original Pol": "契約有無", "has successfully passed the automatic check.": "", "Haulage Permit No": "運搬許可番号", "HEADER_MORE_INFO": "", "High Level": "ハイレベル", "Hight of Vehicle": "車両の高さ", "Historical Permium Holiday": "過去の保険料未払日", "Historical Premium Holiday": "", "History": "", "History Type": "履歴タイプ", "HOLDER": "契約者", "Home Protection Schema (HPS) Exemption: {{hpsExemption}}": "住宅保護計画 (HPS)免除： {{hpsExemption}}", "Hospital": "", "Hospital Name": "病院名", "Hour(s)": "", "How to Deal with Balance Amount": "口座残高はどのように処理するか", "How to Use": "使用方法", "IBAN": "国際銀行口座", "ID Card": "", "ID No.": "ID番号", "ID NO.": "ID番号", "ID No. ": "", "ID Type": "身分証明書種類", "Identifier Info": "ID情報", "If": "もし", "If satisfy all of the following conditions": "以下の条件をすべて満たす場合", "If satisfy any of the following conditions": "次のいずれかの条件を満たす場合", "If the file you want to upload does not belong to the above file type, click Add another type": "アップロードしたいファイルが上記のファイルタイプに該当しない場合、「新規追加」をクリックしてください", "If the option is selected, the system will generate reminder based on days only. Otherwise, specific times will be considered.": "", "If the option is selected, the system will withdraw the proposal based on days only. Otherwise, specific times will be considered.": "", "Ignore the rule and proceed": "", "ILP Bonus": "", "Image": "イメージ", "Immediate Effect": "", "Import Type": "インポートタイプ", "Inactivate Case": "", "Inactivate Case On": "無効ケースについて", "Incident Date": "事故発生日", "Incident Reason": "事故原因", "Including Promotion Discount": "プロモーション割引を含む", "Individual": "個人的", "Individual Policy Upload": "個別ポリシーのアップロード", "Individual Policy Upload Under Master Agreement": "団体契約に基づく個別ポリシーのアップロード", "Industrial Classification": "Industrial Classification", "Info not Completed": "", "Initial Number": "", "Initial Premium Amount": "初回保険料額", "Initial Premium Collected": "初期保険料領収", "Initial Premium Due": "初期保険料支払期日", "Initial Premium Payment": "初期保険料支払", "Initial Principal": "初始本金金额", "Initial_Premium_Payment_Method": "初回保険料支払方法", "Initiallment_Premium_Payment_Method": "分割払い保険料支払方法", "Initiate Manual Underwriting": "", "Input": "入力", "Inspection Expiry Date": "検査有効期限", "Installment": "分割払い", "Installment / Renewal Premium Payment": "分割払い／保険料支払自動継続", "Installment No.": "払込番号", "Installment Number": "払込回数", "Installment Premium": "保険料", "Installment Premium (Before Campaign)": "保険料（キャンペーン前）", "Installment Premium (Before Tax & Service Fee)": "分割払保険料（税抜け）", "Installment Premium (Total)": "保険料合計", "Installment Premium period": "分割払い期間", "Installment_Premium_Bill": "分割払保険料情報", "Installment_Premium_including_tax_discount": "割賦保険料（税・割引込み）", "Installment_Premium_without_tax_discount": "保険料", "Instruction": "", "Insurable Interest": "被保険利息", "Insurance": "保険", "Insured": "被保険者", "INSURED": "被保険者", "Insured (Main Product)": "被保険者(主契約)", "Insured Certificate Type": "被保険証明書タイプ", "Insured Email": "被保険者メールアドレス", "Insured ID No": "被保険者身分証明書番号", "Insured Id No.": "被保険者本人確認書類番号", "Insured Info": "", "Insured Info will be cleared and replaced by policyholder Info. Are you sure to proceed?": "被保険者情報はクリアされ、保険契約者情報に置き換えられることとなりますが、実行するかどうかを確認してください。", "Insured Location": "", "Insured Name": "被保険者氏名（漢字）", "Insured Name: {{insuredName}}": "", "Insured Name2": "被保険者氏名（カナ）", "Insured Object": "", "Insured Object Category": "", "Insured Object Information": "", "Insured Object Name": "", "Insured Object Name: {{name}}": "", "Insured Object of Building": "補償対象物（建物）", "Insured Object of Device": "補償対象物（デバイス）", "Insured Object of Loan Guarantee": "", "Insured Object of Order": "補償対象物（オーダー）", "Insured Object of Pet": "補償対象物（ペット）", "Insured Object of Product": "", "Insured Object of Vehicle": "補償対象物（車）", "Insured Object Type": "", "Insured Query": "", "Insured Type": "被保険者タイプ", "INSURED_CN": "被保険者", "Insured_Object_Info": "補償対象詳細", "INSURED_OBJECT_INFO": "", "INSURED_OBJECT_PROFILE": "", "insured_period": "保険期間", "InsuredIDNo": "被保険者身分証明書番号", "Insuresd ID No.": "被保険者ID番号", "Insuresd Name": "被保険者名", "Interest": "", "Interest Balance": "利息残高", "Interest Balance Change": "利息変更", "Interest Rate": "利息利率", "Internal Offset Amount": "内部差額決済額", "Invalid": "", "Investment & Loan Info": "投資と貸付情報", "Investment Horizon": "投資期限", "Investment Info": "投資情報", "Investment Strategy": "投資戦略", "INVESTMENT_INFO": "", "Invoice No.": "帳票番号", "Invoice Number": "請求書番号", "Invoice Received Date": "帳票受取日", "Invoice Status": "", "Invoice Task Pool": "帳票タスクプール", "Invoice/Credit Note Number": "帳票／クレジットノート番号", "is Loan Vehicle": "車ローン有無", "is New Vehicle": "新車フラグ", "is Not Registed": "登録番号有無", "Is Renewal Policy": "自動継続契約", "Is renewal quote process required?": "保険契約更新の保険料試算を続行しますか？", "is Special Shape Vehicle": "特殊形状の車両フラグ", "Issue": "", "Issue Date": "振出日", "Issue Policy": "契約発行", "Issue policy under master agreement": "", "Issue successfully": "", "Issue Tips": "", "Issue without payment": "後払い契約フラグ", "Issue Without Payment": "", "Issue without payment for individual policy": "", "ISSUE_AGENT": "募集担当者", "Issued": "", "Issued successfully": "", "Issued successfully!": "", "Issured Name": "", "It is not supported to save exactly the same type information": "全く同じタイプの情報の保存はできません。", "item": "項目", "Item Code": "", "Item Name": "項目名", "Key Node": "", "Kindly search the Policy No. first.": "先にポリシー番号を検索してください。", "Label": "", "Label: Value": "", "Laibility Detail": "補償詳細", "Landline": "固定電話", "Language": "言語", "Lapsed": "失効した", "Lapsed Reason": "失効理由", "LapsedDate": "失効日", "LapsedReason": "失効理由", "Last Decision": "最終決定", "Last Name": "氏（漢字）", "Last Operation Time": "", "Last Price Date": "前回価格日", "Last Update User": "最終更新ユーザー", "Last Year Driving Distance": "前年度走行距離", "Latest Interest Amount": "最新利息金額", "Latest Interest Calculation Date": "最新利息計算日", "Latest Interest Capitalization Date": "最新利息繰入日", "Latest Status": "", "latitude": "緯度", "Legal {{type}}": "法定{{type}}", "Legal Beneficiary": "法定保険金受取人", "Legal Representative Info": "法定代表情報", "Legal Trustee": "法定被信託人", "Length of Vehicle": "車両の長さ", "Length: {{length}}": "", "Level": "", "Level Name": "レベル名", "Level Order": "レベル順", "Levy": "賦課金", "Liabilities": "補償", "Liability": "補償内容", "Liability Category": "", "Liability Coverage Period": "", "Liability ID": "", "Liability Name": "補償名称", "Liability Premium": "", "Liability SA": "", "Lien": "留置権", "Lien Exclusion Clause": "留置権除外条項", "Limit": "", "Limit Info": "", "Link Product": "", "Linked Investment Product": "", "List Data": "リストデータ", "Loading": "ローディング", "Loading & Down Sell": "", "Loading List": "", "Loading Method": "", "Loading Period": "", "Loading Period Type": "", "Loading Type": "", "Loading Value": "", "Loan Balance": "契約貸付残高", "Loan Balance Details": "契約貸付残高詳細", "Loan Company": "貸金会社", "Loan Contract No.": "貸付契約番号", "Loan Effective Date": "契約貸付効力日", "Loan Info": "貸付情報", "Loan Provider": "車ローン提供者", "Loan Years": "車ローン年数", "Location {{index}}": "", "Location Based Object": "", "Location Details": "", "Location-based Object": "", "longitude": "経度", "Machinery Breaakdown": "機械故障", "MACHINERY_EQUIPMENT": "", "Main": "", "Main / Rider": "主契約/特約", "Main Benefit": "主契約", "Main Condition Type": "主条件タイプ", "Main Driving Area": "主な運転エリア", "Main Insured": "主被保険者", "Main Insured ID No.": "", "Main Insured ID Type": "", "Main Insured Name": "", "Main Insured Sub Policy No.": "", "Main Product": "主契約", "Main_Rider": "主契約/副契約", "Manager": "マネージャー", "Manager has been changed. Please check.": "マネージャーが変更されました。ご確認ください。", "Mandatory": "", "Manual Compliance Decision": "", "Manual Input": "手入力", "Manual UW Query": "", "Manual UW Task Pool": "マニュアル査定管理", "Marital Status": "", "Market Price": "市場価格", "MARKET_SEGMENTATION": "市場セグメンテーションとカバレッジ計画", "Marketing Goods Selection": "マーケティンググッズの選択", "Marketing Goods Settings": "販売商品の設定", "marriageStatus": "結婚ステータス", "Master Agreement Change": "包括契約変更", "Master Agreement Effective Date": "マスター契約の開始日", "Master Agreement Effective Time": "包括契約有効日時", "Master Agreement Expiry Date": "マスター契約の満期日", "Master Agreement No {{busiNo}}": "包括契約番号 {{busiNo}}", "Master Agreement No.": "包括契約番号", "Master Agreement No. {{masterPolicylNo}}": "包括契約番号{{masterPolicylNo}}", "Master Agreement No. {{masterPolicyNo}}": "包括契約番号{{masterPolicylNo}}", "Master Agreement No.: {{masterPolicyNo}}": "包括契約番号： {{masterPolicyNo}}", "Master Agreement Status": "包括契約ステータス", "Master Agreement Sub-category": "マスター契約サブ種類", "Master Agreement Task Pool": "包括契約タスクプール", "Master Insured Name": "主被保険者名前", "Master Policy": "マスター契約", "Master Policy No.": "包括加入者番号", "Master Policyholder": "包括契約者", "Master Policyholder Name": "包括契約者名", "MASTER_AGREEMENT_BASIC_INFO": "基本情報", "Master_policy_Status": "包括契約ステータス", "masterPolicyChangeProcessed": "処理済み {{number}}", "masterPolicyChangeProcessing": "処理中 {{number}}", "Matched Tag": "マッチしたタグ", "Mater Policy No": "", "Mater Policy No.": "", "Maturity Agreement": "満期設定", "Maturity Benefit": "満期金", "Maturity Benefit Account Transaction Detail": "満期金口座トランザクション詳細", "Maturity Date": "満期日", "Maturity Reminder Date Compare to Policy Expiry Date": "満期通知時間（契約満期日との比較）", "MaximumPaymenttime": "最大支払時間", "Medical Examination": "健康診察", "Medical examination is not allowed to edit after issue, confirm to issue": "発行後は健康診察を編集することができません。発行してもよろしいでしょうか。", "Medical examination is not allowed to edit after issue, confirm to issue?": "", "Medical Examination Item": "健康診察項目", "Medical Examination Item ": "", "Medical Examination Plan": "", "Medical Expense Invoice": "健康診察帳票", "Medical Plan": "健康診察プラン", "Medical Plan Code": "", "Medical Plan Name": "健康診察プラン名", "Medical Plan Value": "健康診察プランの価値", "Medical Requirement Status": "医療要件ステータス", "Method of adding account": "口座追加の方法", "Min Premium": "最小保険料", "Min-Premium Type / Min-Premium": "最低保険料", "Min-Premium Type / Min-Premium should be completed or empty": "最低保険料は必要の場合、関係タイプと関係コードを事前に設定してください。", "Minimum Investment Period": "最低投資期間", "Minimum Investment Period Type": "最小投資期間タイプ", "Minimum Investment Period Value": "最小投資期間値", "Minimum Length": "", "Minimum Protection Value": "最低保証額", "MIP End Date: {{endDate}}": "終了日: {{endDate}}", "MIP Start Date: {{stateDate}}": "開始日: {{stateDate}}", "Mobile": "電話番号（携帯）", "Mobile Phone": "携帯電話", "Model Portfolio": "モデルポートフォリオ", "Model Portfolio Code": "モデルポートフォリオコード", "Model Portfolio Details": "", "Model Portfolio Name": "モデルポートフォリオ名", "Modifying the selection of sub coverage will clear the configured limit and deductible information. Please confirm.": "サブカバレッジの選択を変更すると、設定済みの限度額と免責金額情報がクリアされますので、ご確認ください。", "MONTH": "毎月", "Month(s)": "", "Monthly": "毎月", "More Action": "", "More Info": "", "Motor NCD": "モーターNCD", "MOTOR_FLEET_POLICY_PREMIUM": "", "MOTOR_FLEET_VEHICLE_UPLOAD": "", "MPV": "MPV", "Msg_back_to_policy_info": "契約情報画面に戻る", "Msg_claim_claim_applied_by": "事故受付担当者", "Msg_claim_claim_evaluated_by": "事故受付日", "Msg_claim_claim_evaluation_date": "保険金査定日", "Msg_claim_claimant_id_no": "事故報告者ID", "Msg_claim_claimant_id_type": "事故報告者ID種類", "Msg_claim_claimant_name": "事故報告者", "Msg_claim_insured_email": "被保険者メールアドレス", "Msg_claim_insured_id_no": "被保険者本人確認書類番号", "Msg_claim_Insured_ID_Type": "身分証明書タイプ", "Msg_claim_insured_mobile_number": "被保険者電話番号", "Msg_claim_insured_name": "被保険者名", "Msg_claim_last_document_received_date": "最終書類完備日", "Msg_claim_last_update_date": "最終更新日", "Msg_claim_newly_received": "新しく受け取りました", "Msg_claim_over_30day": "30D以上", "Msg_claim_Payment_Method": "支払方法", "Msg_claim_pend_reason": "保留理由", "Msg_claim_pending_case_status": "保留中のケースステータス", "Msg_claim_product_name": "商品名", "Msg_claim_query_Claim_Query": "案件検索", "Msg_claim_registered_by": "請求受付担当者", "Msg_claim_registration_date": "請求受付日", "Msg_claim_report_date": "報告日", "Msg_common_query_POS_Capture_Date": "異動引受日", "Msg_common_query_POS_Captured_By": "異動引受担当者", "Msg_common_query_POS_Item": "異動項目", "Msg_common_query_POS_No": "異動番号", "Msg_common_query_POS_Registered_By": "異動登録担当者", "Msg_common_query_POS_Registration_Date": "異動登録日", "Msg_common_query_POS_Status": "異動ステータス", "Msg_common_query_Sort_by_POS_No": "異動番号で並べ替え", "Msg_common_relationship_name": "関係者名", "Msg_customer_service_item": "変更処理項目", "Msg_day": "日数", "Msg_Days": "日間", "Msg_detail_Contract_Information": "契約情報", "Msg_error_passwordToE": "パスワードの送信に失敗しました。", "Msg_Market_Master_Policy_No": "包括契約番号", "Msg_Month": "カ月", "Msg_Months": "カ月", "Msg_moreinfo": "詳しい情報", "Msg_paymentperiod_single": "一括払", "Msg_paymentperiod_wholelife": "終身払", "Msg_paymentperiod_years_old": "歳", "Msg_please_input_right_format": "正しいフォーマットを入力してください", "Msg_Pos_basic_info": "異動基本情報", "Msg_Pos_change_reason": "異動理由", "Msg_Pos_Other_Change_Reason": "他異動理由", "Msg_pos_query_posQuery": "異動検索", "Msg_query_Acount_Info": "金額情報", "Msg_query_Actual_Amount": "実際金額", "Msg_query_Additional_Excess": "余分な過剰", "Msg_query_answer": "回答", "Msg_query_Arrival_Place": "到着地", "Msg_query_Arrival_Place_ID": "到着地ID", "Msg_query_Arrival_Place_Name": "到着地名", "Msg_query_Ascending": "昇順", "Msg_query_Ascending_Order": "昇順", "Msg_query_Auction_Item": "オークションアイテム", "Msg_query_Body_Type": "ボディータイプ", "Msg_query_brand_premium_partner": "ブランドプレミアムパートナー（サービス会社パートナー）", "Msg_query_Claim_Documentations": "保険金請求ドキュメント", "Msg_query_Claim_Number": "請求受付番号", "Msg_query_Claim_Workflow": "保険金請求ワークフロー", "Msg_query_collection": "入金", "Msg_query_Contract_Effective_Date": "契約開始日", "Msg_query_Contract_No": "契約番号", "Msg_query_Contract_Termination_Date": "契約終了日", "Msg_query_Contract_Type": "契約タイプ", "Msg_query_Contract_Value": "契約金額", "Msg_query_Create_Time": "作成日時", "Msg_query_data_source": "データソース", "Msg_query_Delay": "遅延", "Msg_query_Delivery_Information": "配信情報", "Msg_query_Departure_Place": "出発地", "Msg_query_Departure_Place_ID": "出発地ID", "Msg_query_Departure_Place_Name": "出発地名", "Msg_query_Descending": "降順", "Msg_query_Descending_Order": "降順", "Msg_query_Documentation_Name": "ドキュメント名", "Msg_query_Download_Send_Pas": "ダウンロード＆パスワードを送付する", "Msg_query_Download_successful": "ダウンロードに成功しました。", "Msg_query_downLoadError": "ダウンロードに失敗しました。", "Msg_query_Engine_Capacity": "エンジン排気量", "Msg_query_FIN": "FIN", "Msg_query_Generated_Date": "生成日", "Msg_query_generatedDate": "作成日", "Msg_query_Gts": "消費税", "Msg_query_Home_Appliance_Information": "家庭用電気製品の情報", "Msg_query_Image_info": "イメージ情報", "Msg_query_Insured_Object_Basic_Information_Change": "目的物基本情報の変更", "Msg_query_Insured_Object_Information": "目的物基本情報", "Msg_query_Insured_Type": "被保険者タイプ", "Msg_query_insuredStatusText": "被保険者がブラックリストに登録されています。", "Msg_query_Is_Main": "メインか", "Msg_query_Liability_Category": "担保種目", "Msg_query_Liability_Name": "担保名", "Msg_query_Make": "作成", "Msg_query_MCC_code": "MCCコード", "Msg_query_MCC_name": "MCC名", "Msg_query_Meal_Type": "宅配種類", "Msg_query_merchant_name": "マーチャント名", "Msg_query_Model": "モデル", "Msg_query_more10000": "検索結果が10000を超えています。検索条件を調整してください", "Msg_query_no": "番号", "Msg_query_Number_of_Item_type": "アイテムタイプ番号", "Msg_query_Number_of_Item_Type": "アイテムタイプ番号", "Msg_query_Object_Category": "目的物分類", "Msg_query_occupation_class": "職業階級", "Msg_query_Order_ID": "注文番号", "Msg_query_Order_Price": "注文価格", "Msg_query_Order_Status": "注文状態", "Msg_query_Order_Time": "注文時間", "Msg_query_Original_Start_Date": "最初の開始日", "Msg_query_Payable": "支払可能", "Msg_query_Payment_Info": "支払情報", "Msg_query_Policy_Documentations": "保険契約ドキュメント", "Msg_query_Policy_Issue_Date": "保険証券発行日", "Msg_query_POS_Documentations": "契約変更管理ファイル", "Msg_query_POS_Workflow": "変更ワークフロー", "Msg_query_productName_version": "販売商品名_バージョン", "Msg_query_Rate_Type": "税率の種類", "Msg_query_Receipts": "レシート", "Msg_query_Receivable": "受領し得る", "Msg_query_Reconciliation_Status": "調整ステータス", "Msg_query_records": "レコード", "Msg_query_refund": "返戻金", "Msg_query_Refund": "返戻金", "Msg_query_Registration_Date": "登録日付", "Msg_query_Scheduled_Arrival_Time": "到着予定時刻", "Msg_query_Scheduled_Departure_Time": "出発予定時刻", "Msg_query_Seating_Capity": "座席定員数", "Msg_query_sendEmail": "パスワードはメールで送信されます。", "Msg_query_service_type": "サービスタイプ", "Msg_query_snack_modifier": "スナック+編集(メンバーシップ編集)", "Msg_query_Sort_by_Claim_No": "案件番号で並べ替え", "Msg_query_Sort_by_Relation_Policy_No": "被保険者証番号の順で並べ替え", "Msg_query_Sort_by_Relation_Pos_No": "包括子契約異動番号の順で並べ替え", "Msg_query_Sort_by_report_date": "レポートの日付で並べ替え", "Msg_query_Source": "ソース", "Msg_query_Status": "状態", "Msg_query_Sum_Assured": "保険金額", "Msg_query_Tax_Detail": "税の詳細", "Msg_query_Tax_Rate_Value": "税率/値", "Msg_query_Tax_Type": "税金の種類", "Msg_query_The_policy_holder_is_in_blacklist": "この加入者はブラックリストに存在する", "Msg_query_titleDes": "これは暗号化されたファイルです。パスワードはメールで送信されます。", "Msg_query_total": "総計", "Msg_query_transactionNo": "トランザクション番号", "Msg_query_transactionType": "トランザクションタイプ", "Msg_query_Transport_Information": "交通情報", "Msg_query_Transportation_Number": "交通手段番号", "Msg_query_trigger_category": "トリガーカテゴリ", "Msg_query_type": "タイプ", "Msg_query_unique_ID": "一意のID", "Msg_query_Use_of_Vehicle": "輸送手段", "Msg_query_Vehicl_No": "車両識別番号", "Msg_query_Vehicle": "車", "Msg_query_Vehicle_Age": "車の使用年数", "Msg_query_Vehicle_Identification_No": "車両識別番号", "Msg_query_View_all": "すべてを表示する", "Msg_query_Year_of_Make": "作成年", "Msg_reconciliation_channel": "販売チャネル", "Msg_Relation_Pos_Number": "包括子契約異動番号", "Msg_Total": "トータル", "Msg_transaction_type": "取引タイプ", "Msg_version": "商品バーション", "Msg_Virtural_Insured": "バーチャル被保険者", "Msg_week": "今週", "Msg_weeks": "数週", "Msg_Years": "年", "MULTI_BENEFICIARY": "複数受取人", "MULTI_INSURED": "被保険者", "MULTI_PAYER": "保険料の支払者", "MULTIPLE_OBJECT_INFO": "", "My Task": "", "N Year Risk Amount": "{{ N }} 年度リスク額", "name": "お名前", "Name": "担当者", "nameCombine": "{{code}}_{{name}}", "Named Insured": "記名被保険者", "Nationality": "国籍", "NCD": "NCD", "NCD %": "NCD％", "NCD Amount": "NCD金額", "Need Advanced Payment": "前払い必要", "Need DCA arrangement?": "ドルコスト平均法プランンは必要ですか？", "Need Vehicle Examination": "", "Net Prem": "純保険料", "Net Premium": "純保険料", "New": "新規", "New Business": "新規加入", "New Business & Renewal Configuration": "新規契約と継続", "New Business Configuration": "新規契約設定", "New Business Info": "", "New Document": "新しいファイル", "New Master Agreement": "包括契約新規追加", "New SA": "新しい保険金額（追加保険金）", "New Vehicle": "", "NEW_BUSINESS_INFO": "", "Next": "次へ", "Next Due Date": "次回応当日", "Next Due Date: {{date}}": "次回払込日: {{date}}", "Next Rebalancing Due Date": "次回リバランス払込日", "NextPremiumIncludingTax": "次期保険料（税込）", "NextPremiumWithoutTax": "次期保険料（税抜き）", "NLG Benefit:": "失効せず保証", "no": "いいえ", "NO": "No.", "No attachment has been uploaded under the selected product.": "選択された製品には添付ファイルがアップロードされていません。", "No case number found": "", "No Claim Discount": "無請求割引", "No discount configured for current period": "本期間内割引未設定", "No record": "--", "No Results": "結果なし", "No valid application elements: INSURED": "", "No valid application elements: PAYER": "", "No valid application elements: POLICY_HOLDER": "", "No Valid Data": "データが見つかりません", "No valid master policy is available for this normal policy renewal, please check.": "", "no_data": "該当データはありません", "No_Data": "", "No.": "番号", "No. {{appNo}}": "", "No. {{policyNo}}": "", "No. of Accident Free Years": "無事故年数", "noData": "データなし", "Nominee": "受取名義人", "NOMINEE": "受取名義人", "Non Standard Tariff": "非標準的な料金", "Non-Location": "", "Non-location Based Object": "", "None": "None", "Normal Policy List": "", "NORMAL_INSURED": "被保険者", "NORMAL_PAYMENT": "レギュラー", "NORMAL_POLICY_HOLDER": "包括契約者", "normalPolicyList": "個別契約リスト", "Not Within Premium Holiday": "保険料払込猶予期間外", "Note": "注記", "Notice Reminder": "保険料徴収通知リマインダー", "Notification History": "通知履歴", "Notification Type": "通知種類", "Now You Can Create the Team": "今、チーム作りができます", "Number of Accident": "", "Number of Active Policies": "有効保険契約数", "Number of Employees": "従業員数", "Number of Installment": "分割払い回数", "Number of Luggage": "携行品数", "Number of Objects": "団体契約車両数", "Number of Order": "チケット数", "Number of Pending Policies": "未処理の保険契約数", "Number of Records": "レコード数", "Number of Renewal Time": "更新回数", "Number of Seat": "座席数", "Number of task assign when user ask": "ユーザーの問い合わせ時のタスク割当数", "Number of Vehicle Owned": "所有車台数", "Object": "補償対象", "Object ID": "オブジェクトID", "OBJECT LIST": "", "OBJECT_INFO": "補償対象物情報", "Occupation": "職業", "Off-Peak Car": "オフピークカー", "OIB": "", "Only Failed": "失敗データのみ", "Only insured": "被保険者のみ", "Only policyholder": "加入者のみ", "Only view image files?": "", "Open Menu": "メニュー一覧", "Open the link in a new window": "", "Operation": "操作", "Operation History": "操作履歴", "operation time": "処理時間", "Operation_Approve": "承認者", "Operation_Capture": "キャプチャ担当者", "Operation_Current": "現在の操作者", "Operation_Decide": "査定担当者", "Operation_Operate": "操作者", "Operation_Register": "登録者", "OPERATIONS_COMMENTS": "", "Operator": "担当者", "OPT_IN Check Text": "マニュアルはないため、引受または拒否のみを選択してください", "Opt-In Check": "Opt-In Check", "Opt-In Rules": "Opt-In Rules", "Opt-In-Rules": "", "Optional Covers": "オプションカバー", "Optional Text": "オプションテキスト", "Order Currency": "注文通貨", "Order Date": "注文時間", "Order ID": "注文 ID", "Order Info": "注文情報", "Order No.": "チャネルトランザクション番号 {{no}}", "Order Number": "予約種類", "Order Price": "注文金額", "Order the Teams while the assignment rule are same": "割当ルールが同じ場合、チームを並べ替えます。", "Order Type": "予約種類", "Order Value": "注文内容", "Ordinary condition": "通常状態", "Organization": "組織", "Organization ID No.": "企業証明書番号", "Organization ID Type": "企業証明書タイプ", "Organization ID Type/No.": "", "Organization Name": "法人名義", "Origin SA": "", "Original Master Agreement No.": "前回の包括契約番号 ", "Original Pol No": "契約番号", "Original SA": "", "Original Start Date": "", "Original Sum Assured": "初期保険金額", "Original_policy_no": "元の契約番号", "Other": "", "Other Information": "", "Other Policy Info": "その他契約情報", "Other Product": "", "Other Properties": "", "OTHER_PARTY_ROLES": "その他の関係者役割", "OTHER_PARTY_ROLES_CN": "その他の関係者役割", "Others": "その他", "Over All": "全体", "Overriding Commission": "上乗せ手数料", "Package": "商品組合", "Package Code": "", "Package Level": "", "Package Name": "保険商品組合せ名", "PackageName": "保険商品組合せ名: {{packageName}}", "Packages": "保険商品組合せ", "Packing Information": "", "Pad with zeros": "", "Parcel": "パーセル", "Parcel Number": "パーセル番号", "Parcel Tier": "パーセルティアー", "Parcel Value": "パーセル内容", "Parcel Volume": "パーセル量", "Parcel Weight": "パーセル重さ", "Part Questionnaire Purpose": "部分アンケートの目的", "Partner": "パートナー", "partnerCode": "", "partnerType": "", "Pass": "Pass", "Passed": "パス", "Pay Account": "", "pay_frequency": "払込回数", "PAYEE": "保険金受取人", "Payee Info": "保険金受取人情報", "Payee Name": "保険金受取人氏名", "Payee Type": "送金先種類", "Payer": "保険料の支払者", "payer of Liability": "payer of {{Liability}}", "Payer Role": "支払者の役割", "Payer Type": "支払者タイプ", "Payer/Payee Type": "支払人/受取人タイプ", "Payment Amount": "支払金額", "Payment Amount By Assignee": "譲渡先別の支払金額", "Payment By Assignee": "譲渡先別の支払", "Payment Currency By Assignee": "譲渡先別の送金通貨", "Payment Date": "", "Payment Frequency": "保険料支払頻度", "Payment History": "支払履歴", "Payment information could not be copied due to differences in some configurations.": "一部の設定が異なるため、支払情報のコピーに失敗しました。", "Payment information has been successfully copied automatically.": "支払情報は正常に自動コピーされました。", "Payment Mehtod": "", "Payment Method": "支払方式", "Payment Method / Account Type": "支払方法 / 口座種類", "Payment Method/Account Type": "支払方法/口座タイプ", "Payment Method/Account Type Details": "支払方法/口座種類の詳細", "Payment Option": "支払オプション", "Payment Period": "支払期間", "Payment Periods": "支払期間", "Payment Plan": "支払プラン", "Payment Status": "支払状態", "PAYMENT_ACCOUNT_INFO": "支払口座情報", "PAYMENT_INFO": "", "PAYMENT_INFO_VIEW": "", "PAYMENT_PLAN": "支払プラン", "PAYMENT_PLAN_PAYER": "保険料の支払者", "PAYMENT_PLAN_PREMIUM_PAYMENT": "保険料支払", "PayMethod": "支払方法", "PayMethod / Account Type": "{{-payMethod}}/{{-accountType}}", "PAYOR": "支払者", "payText": "支払", "Pending Proposal Check": "", "Pending Transaction Amount（Fund Currency）": "トランザクション待ち金額（ファンド通貨）", "Pending Transaction Unit": "トランザクション待ち口数", "Period": "", "Period Type": "期間タイプ", "Period Value": "期間内容", "PeriodAge": "{{value}} 歳", "PeriodYears": "{{value}} 年", "Permanent Address": "本籍地", "Person List": "", "Personal Record": "個人記録", "Pet": "ペット", "Pet ID": "ペットID", "Pet Info": "ペット情報", "Pet Type": "ペットタイプ", "Pet Varieties": "ペットの品種", "Phone": "連絡電話情報", "Phone No": "", "Phone Number": "携帯番号", "Phone Type": "電話タイプ", "Pin": "", "Place of Incorporation": "設立場所", "Place of Interest": "興味のある場所", "Plan": "販売商品プランバージョン", "Plan Code": "", "Plan Goods Version": "販売商品プランバージョン", "Plan Level": "", "Plan Name": "プラン名", "Plan Premium Model": "", "planName: {{goodsPlanName}}": "プラン名: {{goodsPlanName}}", "Planned Premium": "予定保険料", "Planned Premium Amount": "", "Planned Premium Collected": "予定保険料領収", "Planned Premium Layer Details": "計画保険料の詳細", "Planned Premium: {{amount}}": "計画保険料: {{amount}}", "Plate No.": "プレート番号", "Plate Type": "プレートタイプ", "Please": "", "Please add at least one piece of data": "少なくとも一つのデータを追加してください", "Please add at least one product": "", "Please check schema correction. Some schemas have category without name.": "スキーマの修正を確認してください。スキーマに名前のないカテゴリがあります。", "Please click 'Apply Master Plan' button to match coverage plan information.": "", "Please confirm the following information is correct . Once confirmed, it cannot be modified.": "以下の情報が正しいかを確認してください。 確認してから変更できません。", "Please confirm the following information is correct.Once confirmed,it cannot be modified": "以下の情報が問題ないかを確認してください。確定押下した後、二度と修正できません。", "Please confirm whether the entered Master Agreement information has been saved before uploading the file.": "ファイルをアップロードする前に、入力したマスター契約情報が保存しますか。", "Please Download Template first.": "先に<2>テンプレートをダウンロードして</2>ください", "Please enter {{fieldName}} before submitting.": "", "Please enter {{productName}} product decision before submitting the task.": "", "Please enter a number greater than 0": "0以上の数字を入力してください。", "Please enter a number greater than 0 but less than 100": "0以上100未満の数字を入力してください。", "Please enter a number greater than 0 but less than 200": "0以上200未満の数字を入力してください。", "Please enter the decision!": "", "Please fill in the Setting Table": "設定表にご記入ください", "Please generate the offer first.": "まずオファーを生成してください.", "Please generate the premium first.": "まず保険料を計算してください。", "please input": "", "Please input": "入力してください", "Please input a number": "Please input a number", "Please input number": "", "Please input positive integer": "正の整数を入力してください", "Please Input Range": "{{minValue}}-{{maxValue}}を入力してください。", "Please input valid party ID!": "有効なパーティーIDを入力してください！", "Please input your Product Type!": "", "Please input your Team Name!": "チーム名を入力してください。", "Please note when choosing DCA arrangement,  the amount for each period will invest in the fund based on fund appointment rate of planned premium.": "", "Please note when choosing the investment strategy, the fund appointment for premium & portfolio rebalancing will follow the defination on the strategy.": "投資戦略を選択した場合、保険料の基金割り当て比率と投資リバランスの基金割り当て比率は、投資戦略で設定された比率が使用されます。", "Please notice that the entered coverage along with its related limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage and limit/deductible information will be cleared. Do you want to continue?": "", "Please notice that the entered insured object along with its related coverage information will be cleared. Do you want to continue?": "", "Please return to the task pool, search for the proposal number {{proposalNo}} and try again later.": "", "Please save or delete current attachmentType": "添付ファイルの保存または削除をお願いします。", "Please save the policy information before adding comments": "", "Please search or input": "検索または入力してください", "Please search transaction first": "まず取引を検索してください。", "please select": "", "Please select a higher level underwriter to escalate the case.": "より上位のアンダーライターを選択し、ケースをエスカレーションしてください。", "Please select a higher level underwriter to referral the case.": "", "Please select an underwriter to escalate the case.": "", "Please select an underwriter to reassign the case.": "ケースを再割り当てするアンダーライターを選択してください。", "Please select an underwriter to referral the case.": "", "Please Select At Least One Condition!": "少なくとも1つの条件を選択してください！", "Please Select At Least One Liability": "", "Please select at least one record": "", "Please select decision": "", "Please select effective date": "有効日を選択してください。", "Please select Exclusion": "免責を選択してください。", "Please select factor first": "", "Please select first": "まず {{text}}を選択してください。", "Please select language": "言語を選択してください。", "Please select Liability": "補償内容を選択してください。", "please select one": "一つ選択してください。", "Please select one Goods before submitting!": "商品を選択してから提出してください！", "Please select one Sub-category before submitting!": "", "Please select policy effective date": "契約始期日を選択してください", "Please select policy expiry date": "契約満期日を選択してください。", "Please select policy type submitting!": "", "Please select Product": "商品を選択してください。", "Please select the Goods Name first.": "", "Please select the insurance applications that need manual underwriting": "", "Please select whole days": "全日を選択してください。", "Please select your Bind Task Assignment Rule!": "バインドタスクの割当ルールを選択してください。", "Please select your Team Members!": "チームメンバーを選択してください。", "Please select your Team Type!": "", "Please set policyholder": "保険加入者を設定してください", "Please Upload": "", "Please Upload Document": "", "Please Upload File": "ファイルをアップロードしてください", "Please Upload invoice": "請求書をアップロードしてください。", "Please upload one file": "", "Please_enter_at_least3characters": "3文字以上入力してください", "please_select": "", "POLICY": "契約", "Policy Assignment": "契約割当", "Policy Basic Infomation": "", "Policy Change": "契約変更", "Policy Charge": "", "Policy Configuration": "保険契約設定", "Policy Currency": "契約通貨", "Policy Delivery Method": "保険証券の配送方法: {{method}}", "Policy E-Document Type": "契約E-Document Type", "Policy Effective": "", "Policy Effective Check": "契約有効確認", "Policy Effective Date": "契約始期日", "Policy Effective Date ": "", "Policy Effective Without Collection (NB)": "", "Policy Expiry Date": "満期日", "Policy History": "契約履歴", "Policy Holder": "", "Policy Info": "契約情報", "Policy Information": "契約情報", "Policy Issuance Compliance Check （After Premium Payment)": "Policy Issuance Compliance Check （After Premium Payment)", "Policy Issuance Rules": "契約成立ルール", "Policy Issuance UW Check (After Premium Payment)": "", "Policy Issue Date": "保険証券発行日", "Policy List": "", "Policy Loan": "契約ローン", "Policy Loan Detail": "契約貸付詳細", "Policy Maturity Termination Method": "保険契約満期消滅方法", "Policy No": "加入者番号", "Policy No.": "加入者番号", "Policy No. {{policyNo}}": "加入者番号 {{policyNo}}", "Policy Number Generation Rule": "", "Policy Period": "", "Policy Regeneration": "ポリシー再生成", "Policy Serial Number": "ポリシーシリアルナンバー", "Policy Sign Off Date": "保険証券受領日", "Policy Sign Off Rule": "契約承認ルール", "Policy Status": "契約ステータス", "Policy Tag": "", "Policy Tag History": "", "Policy Tag List": "", "Policy Tagging": "", "Policy Toolip": "{{key}}: {{value}}", "Policy Type": "契約種類", "Policy UW Decision": "保険契約の引受決定", "Policy UW Decision Details": "", "Policy Year": "契約年度", "Policy years": "契約年度", "POLICY_CHANGE": "", "POLICY_CHANGE_DETAILS": "", "POLICY_CHANGE_OVERVIEW": "", "POLICY_DETAIL_INFO": "", "POLICY_DETAILS": "契約詳細", "POLICY_HISTORY": "契約履歴", "POLICY_HOLDER": "契約者情報", "POLICY_HOLDER_CN": "契約者情報", "POLICY_OVERVIEW": "", "Policy_Query": "契約検索", "PolicyEffectiveDate": "契約始期日", "policyEffectiveRuleTooltip": "システムは、ここで設定された「一般決定」ルールより申込フローをトリガーする。結論は拒否の場合、申込書を拒否する；結論はマニュアルの場合、新契約作成を拒否する。  \\n マニュアルはないため、引受または拒否のみを選択してください", "Policyhoder Info": "", "PolicyHolder": "加入者", "Policyholder and insured is the same. The change is applied to": "契被同人、変更対象は", "Policyholder Certificate Type": "契約者証明書種類", "Policyholder Details": "", "Policyholder Email": "加入者メールアドレス", "Policyholder ID No": "加入者本人確認書類番号", "Policyholder Id No.": "加入者本人確認書類番号", "Policyholder ID No.": "加入者本人確認書類番号", "Policyholder ID Type": "", "Policyholder Info": "契約者情報", "Policyholder Mobile Number": "加入者携帯番号", "Policyholder Name": "加入者氏名（漢字）", "PolicyHolder Name": "", "Policyholder Name2": "加入者氏名（カナ）", "Policyholder Type": "加入者タイプ", "Policyholder_Email": "加入者メールアドレス", "Policyholder_ID_No": "加入者本人確認書類番号", "Policyholder_ID_Type": "加入者本人確認書類タイプ", "Policyholder_Mobile_Number": "加入者携帯番号", "Policyholder_Name": "加入者氏名（漢字）", "PolicyholderIDNo": "加入者本人確認書類番号", "PolicyStatus": "契約ステータス", "Portfolio Rebalancing": "リバランス", "Portfolio Rebalancing Detail": "リバランス詳細", "POS Application Date": "", "POS Effective Date": "異動有効日", "POS Item": "異動項目", "POS No.": "異動番号", "POS_Archives_Room": "異動履歴", "POS_DETAILS": "異動詳細", "POS_Effective_Date": "異動有効日", "posDecisionEnum.APPROVE": "承認", "posDecisionEnum.BACK_DATA_ENTRY": "レスポンスデータを入力する", "posDecisionEnum.REJECT": "拒否", "posStatusEnum.APPROVAL_IN_PROGRESS": "承認中", "posStatusEnum.CANCELLED": "キャンセル", "posStatusEnum.DATA_ENTRY_IN_PROGRESS": "異動登録中", "posStatusEnum.EFFECTIVE": "有効", "posStatusEnum.INVALID": "無効", "posStatusEnum.REJECTED": "拒否", "posStatusEnum.WAITING_FOR_APPROVAL": "承認待ち", "posStatusEnum.WAITING_FOR_COLLECTION": "入金待ち", "posStatusEnum.WAITING_FOR_DATA_ENTRY": "登録待ち", "posStatusEnum.WAITING_FOR_EFFECTIVE": "有効待ち", "posStatusEnum.WITHDRAW": "削除", "posStatusStepEnum.APPROVAL_PROCESSING": "承認", "posStatusStepEnum.APPROVAL_WAITING": "承認待ち", "posStatusStepEnum.CANCELLED": "キャンセル", "posStatusStepEnum.COLLECTION_PAYMENT": "入金", "posStatusStepEnum.DATA_ENTRY_CALCULATION": "計算", "posStatusStepEnum.DATA_ENTRY_CONFIRMATION": "確認", "posStatusStepEnum.DATA_ENTRY_CS_ITEM": "異動登録", "posStatusStepEnum.DATA_ENTRY_PAYMENT_COLLECTION": "入出金", "posStatusStepEnum.EFFECTIVE": "有効", "posStatusStepEnum.INVALID": "失効", "posStatusStepEnum.REGISTER": "登録", "posStatusStepEnum.REJECTED": "拒否", "posStatusStepEnum.SELECT_CS_ITEM": "異動項目選択", "posStatusStepEnum.WITHDRAW": "削除", "Post Code": "郵便番号", "Postal Name": "郵便名", "Power Type": "パワータイプ", "PRECIOUS_ITEM": "", "Premium": "保険料", "PREMIUM": "保険料", "Premium (Before Campaign)": "保険料（キャンペーン前）", "Premium (Before Tax & Service Fee)": "分割払保険料（税抜け）", "Premium (Total)": "保険料合計", "Premium & SA Calculation Method": "", "Premium Aggregation Detail": "保険料集計", "Premium allocation": "保険料投資の配分率", "Premium Calculation Method": "", "Premium Calulation Method": "", "Premium Collected & Allocation": "保険料領収＆割り当て", "Premium Collection": "保険料徴収", "Premium Collection Time": "保険料コレクションの時間", "Premium Detail Download": "", "Premium Details": "", "Premium Discount": "保険料割引", "Premium Discount on Net Premium": "純保険料の割引き", "Premium Discount On Tax": "保険料割引の税金", "Premium Due Date": "保険料応当日", "Premium DueDate": "", "Premium Frequency": "保険料払込頻度", "Premium Funder": "保険料提供者", "Premium Handling Method": "", "Premium Info": "", "Premium Info Detail": "", "Premium Notice Date": "保険料徴収通知日", "Premium Notice Date Compare with Due Date": "", "Premium or SA Info": "保険料または保険金情報", "Premium Payer": "保険料支払人", "Premium Per Unit": "保険料（単位ごと）", "Premium Period": "払込時間", "Premium Period Type": "保険料払込期間タイプ", "Premium Period Value": "保険料払込期間", "Premium Status": "保険料払込ステータス", "Premium Type": "保険料タイプ", "PREMIUM_AGGREGATION": "プレミアム・アグリゲーション", "PREMIUM_AMOUNT": "保険料金額", "Premium_Discount": "", "Premium_discount_type": "保険料割引タイプ", "Premium_Due_Date": "保険料支払期日", "PREMIUM_FUNDER": "保険料提供者", "Premium_including_tax_discount": "保険料（税?割引込み）", "premium_pay_account": "保険料支払いアカウント", "PREMIUM_PAYER": "保険料支払人", "PREMIUM_PAYER_CN": "保険料支払人", "Premium_without_tax_discount": "保険料（税抜き）", "PremiumDuration": "保険料支払期間", "PremiumEndDate": "保険料支払期限", "Press enter to record enums, duplicated keys will be ignored.": "", "Preview Offer": "", "Preview Strategy Details": "方策詳細照会", "Previous Policy No.": "前回の加入者番号", "Price Date": "ファンド価格適用日", "Price Date For Adjustment": "価格調整日", "Principal Balance": "元金残高", "Principal Balance Change": "元金残高変更", "Principal Steam Details": "元金スチーム詳細", "Print History": "出力履歴", "Print Name": "作成者", "Print Reason": "作成事由", "Print Time": "作成時間", "Prior condition": "優先条件", "Priority": "優先度", "Private Task - Active": "プライベートタスク－有効", "Private Task - Inactive": "プライベートタスク－無効", "process": "処理", "Process Configuration": "プロセス設定", "Process Failed": "", "Process Successfully": "処理完了", "Process Underwriter Level": "現在のアンダーライター格付け", "Process UWer Level": "プロセスアンダーライターのレベル", "Processing": "処理中", "Product": "商品", "Product Category": "商品分類", "Product Code": "商品コード", "Product Code&Name": "商品コード&商品名", "Product Decision": "商品決定", "Product Details": "", "Product Discount": "商品割引", "Product in Goods": "販売商品特約", "Product Info": "商品情報", "Product Level": "", "Product List": "", "Product Name": "保険商品名", "Product Name {{name}}": "", "Product name: {{productName}}": "商品名: {{productName}}", "Product Premium: {{sa}}: ": "", "Product SA: {{sa}}: ": "", "Product Status": "商品状態", "Product Summary": "", "Product Tax": "商品税", "Product Type": "", "Product_Amount": "商品金額", "PRODUCT_BUNDLE_BASIC_INFO": "", "Product_Libility_Info": "商品と補償の情報", "Product_nostyle": "商品", "Product: {{productCode}}_{{productName}}": "", "productCode_productName": "{{productCode}}_{{productName}}", "Promo Code": "", "Promotion Code": "プロモーションコード", "Promotion Discount": "プロモーション割引", "Promotion Discount On Levy": "プロモーション割引（税金）", "Promotion Type": "プロモーションタイプ", "PROPERTY": "", "Property Coverage": "", "Property Product": "", "Proposal": "提案", "Proposal Compliance Check （Before Premium Payment)": "Proposal Compliance Check （Before Premium Payment)", "Proposal Configuration": "保険申込書設定", "Proposal Confirmation Date": "見積もり契約確認日", "Proposal Date": "申込日", "Proposal Effective Date": "保険開始日", "Proposal Flow Setting": "申込のフロー設定", "Proposal Info": "申込情報", "Proposal No": "保険申込番号", "Proposal No: ": "保険申込番号", "Proposal No: {{applicationNo}}": "申込受付番号: {{applicationNo}}", "Proposal No: {{proposalNo}}": "保険申込番号: {{proposalNo}}", "Proposal No.": "申込受付番号", "Proposal No. {{proposalNo}}": "保険申込番号 {{proposalNo}}", "Proposal reminder days can not duplicate. Please check.": "リマインダー日数の設定は既に存在しました、確認してください。", "Proposal Reminder Rule": "見積もり契約リマインダールール", "Proposal Request Date": "申込日", "Proposal Rule": "申込ルール", "Proposal Rules": "申込ルール", "Proposal Status": "申込ステータス", "Proposal Task Pool": "申込書検索", "Proposal Task Pool Re-assign": "担当者を再割り当て", "Proposal Withdraw Rule": "申込取下ルール", "PROPOSAL_DETAILS": "申込詳細", "PROPOSAL_INFO": "申込情報", "Proposal/Policy": "申込書／契約", "Proposal/Policy No.": "申込書／加入者番号", "Proposal/Policy Status": "申込書／契約ステータス", "Proposal/POS No": "申込番号/異動番号", "ProposalDate": "申込日", "proposalWithdrawTooltip": "これは、申込ステータス及び申込取消期間を自動的に設定するためのものです。", "Provide Vehicle Photo Later X Days": "", "Public Liability": "公共責任", "Public Task": "パブリックタスク", "Public Tender": "公共調達", "Public Tender No.": "公共調達番号", "Purchase Date": "購入日", "Purchase Price": "購入金額", "Purpose": "目的", "QUARTER": "四半期", "Quarterly": "四半期ごとに", "Query Error": "検索エラー", "Query Escalate Users Failed": "エスカレートユーザーの検索に失敗しました。", "Query Reassign Users Failed": "再割当ユーザーの検索に失敗しました。", "Query Referral Users Failed": "", "QUERY_ATTACHMENTS": "", "QUERY_POLICY_HISTORY": "", "QUERY_RENEWAL_HISTORY": "", "query-Agent": "募集人", "query-Agent Name": "募集人名前", "query-Allocation Amount": "分配額", "query-Business No": "ビジネス番号", "query-Consentee": "保護者", "query-Deductible Amount": "免責金額", "query-Effective Date": "始期日", "query-Expiry Date": "満期日", "query-Extra Loading": "保険料割増", "query-Fund Appointment": "ファンド配分", "query-Fund Currency": "ファンドカレンシー", "query-Fund Name": "ファンド名", "query-Gender": "性別", "query-Goods Name": "販売商品名", "query-Goods Version": "販売商品バージョン", "query-GoodsName": "販売商品名", "query-GoodsVersion": "商品バージョン", "query-History Type": "履歴タイプ", "query-ID Type": "身分証明書タイプ", "query-Identifier Info": "認識情報", "query-Individual": "個人", "query-Insured Email": "被保険者メールアドレス", "query-Insured Name2": "被保険者氏名（カナ）", "query-Investment Strategy": "投資戦略", "query-Is Renewal Policy": "自動継続契約", "query-Main Benefit": "主契約", "query-Master Policy No.": "包括契約番号", "query-Mobile Phone": "携帯電話", "query-No.": "No.", "query-Nominee": "ノミニー", "query-Organization": "法人", "query-Other Policy Info": "その他契約情報", "query-Others": "その他", "query-Payer": "支払者", "query-Payment Frequency": "支払頻度", "query-Payment Method": "支払方法", "query-Payment Period": "支払期間", "query-PeriodAge": "{{value}} 歳", "query-Policy Currency": "契約通貨", "query-Policy Effective Date": "契約始期日", "query-Policy Issue Date": "保険証券発行日", "query-Policy Loan": "契約貸付", "query-Policy Regeneration": "ポリシー再生成", "query-Policyholder": "", "query-PolicyHolder": "加入者", "query-Policyholder Email": "加入者メールアドレス", "query-Policyholder ID No.": "加入者本人確認書類番号", "query-Policyholder Info": "加入者の情報", "query-Policyholder Name": "加入者氏名（漢字）", "query-PolicyNo": "加入者番号", "query-Premium Due Date": "請求データ抽出応当日", "query-Product": "商品", "query-Proposal No.": "保険申込番号", "query-Registration Date": "登録日", "query-Relationship With Insured": "", "query-Relationship With Policyholder": "加入者との関係", "query-Renewal": "保険継続", "query-Sales Channel": "販売チャンネル", "query-Select All": "すべて選択", "query-Service Fee": "サービス料・ポイント", "query-Settlement Date": "承認日", "query-Social Account": "SNSアカウント", "query-Status": "ステータス", "query-Transaction Type": "トランザクションタイプ", "query-Trustee": "被信託人", "Questionnaire": "アンケート", "QUESTIONNAIRE": "アンケート＆告知", "Questionnaire Info": "アンケート情報", "Questionnaire Name": "アンケート名", "QUESTIONNAIRE_INFO": "アンケート情報", "Quick Menu": "クリックメニュー", "Quotation": "", "Quotation Configuration": "保険見積書設定", "Quotation Info": "", "Quotation Information Pre-check": "見積情報の事前検証", "Quotation No": "", "Quotation No.": "見積番号", "Quotation No. {{proposalNo}}": "", "Quotation Period": "見積期間", "Quotation Query": "", "Quotation Stage": "見積段階", "Quotation Status": "見積状態", "Quotation Task Pool": "見積タスクプール", "Quote Bound": "", "Random Check": "ランダムチェック", "Random Check Configuration": "ランダムチェック設定", "Random Ratio": "ランダムチェックの割合", "Rate": "レート", "Rate Type": "レートタイプ", "Rate-Classes BI": "レートクラスBI", "Rate-Classes OD": "レート-クラスOD", "Rate-Classes PD": "レートクラスPD", "Rate-Classes PIC": "レートクラスPIC", "Rate/Amount": "", "Re-accumulate and affect target": "再累積してターゲットに影響を与える", "Re-assign": "", "Re-underwriting Reason": "再査定理由", "Re-underwriting Type": "再査定タイプ", "Re-Upload": "再度アップロード", "Re-Upload Successfully": "", "Read More": "", "Reason": "理由", "Reason Comments": "理由のコメント", "Reassign": "再割当", "Reassign | {{selectLength}} Option(s)": "再割り当て | {{selectLength}} 件", "reassign control {{proposalNos}}": "現在の保険証券と他の保険証券（{{proposalNos}}）は関連付けられています。現在の保険証券の入力タスクを再割り当てする場合、関連する保険証券の入力タスクも再割り当てされます，ご確認ください。", "reassign control batch": "現在の一括再割り当てのタスクには、商品パッケージまたは連携発行シナリオに基づく保険申込書が含まれています。これらの保険申込書のタスクを再割り当てすると、他の関連する保険申込書のタスクも再割り当てされます，確認してください。", "Reassign or Referral": "", "Reassign Successfully!": "再割当成功", "Rebalance Frequency": "", "Rebalancing Date": "リバランス日", "Rebalancing Frequency": "リバランス頻度", "Rebalancing History": "リバランス履歴", "Receivepromotionalemailsornot": "プロモーションメールを受信する", "Recipient": "受信者", "Reconciliation Status": "リコンサイルステータス", "Records / Number of total records": "記録 / 総記録数", "Recount": "再計算", "Recover": "", "Recurring Single top up": "逓増定期", "Recurring Single Top Up": "逓増定期", "Recurring Single Top Up Frequency": "逓増定期頻度", "Recurring Single Top Up Period": "逓増定期期間", "Reduce Coverage": "保険金額の減額", "Referral": "", "Referral or Reassign": "", "Referral Reason": "", "Referral Response": "", "Referral Successfully!": "", "Referral Task": "", "Refresh Confirm": "", "Regenerate": "再作成", "Regenerate Error": "再生成エラー", "Regenerate Reason": "再生成理由", "Regenerate Successfully": "再生成が成功しました", "Register Date": "", "registration area": "登録エリア", "registration category": "登録カテゴリー", "Registration Date": "登録日", "registration hiragana": "登録ひらがな", "Registration No.": "登録番号", "registration serial no": "登録シリアル番号", "Regular Bonus Plan": "定期ボーナス・プラン", "Regular Premium": "定期保険料", "Regular top up": "", "Regular Top Up": "定期増額（年単位）", "Regular Top Up Collected": "定期追加保険料領収", "Regular Top Up: {{amount}}": "定期増額: {{amount}}", "Regular Top-up": "", "Regular Withdrawal": "定期減額", "Reinstate": "復活", "Reinsurance Decision": "", "REINSURANCE_INFO": "再保険情報", "reject": "拒否", "Reject": "拒否", "Rejected": "拒否", "Related Policy": "関連契約", "Related Policy Overview": "", "Related to insured": "被保険者関連", "Related to insured object": "補償対象情報", "Related to policy": "契約関連", "Related to policyholder": "加入者関連", "RELATED_POLICY": "", "RelatedPartiesInformation": "関連当事者情報", "Relation with primary insured": "被保険者との関係", "Relation_Policy_No": "関連契約番号", "Relationship No.": "リレーションシップ番号", "Relationship Type / Relatinship No.": "関係タイプ / 関係コード", "Relationship Type / Relatinship No. should be completed or empty": "関係タイプ / 関係コードは必要の場合、関係タイプと関係コードを事前に設定してください。", "Relationship With Insured": "被保険者との続柄", "Relationship with Policyholder": "契約者との続柄", "Relationship With Policyholder": "契約者との続柄", "Relationship With Policyholder: {{holderRelationRemark}}": "契約者との関係: {{holderRelationRemark}}", "Relative": "", "Release": "", "Release Failed": "", "Release Success": "", "Release Task": "", "Release Time": "", "Remaining Amount": "残高", "RemainingPaymentTime": "残り支払時間", "Remark": "メモ", "remarks": "メモ", "Remarks": "メモ", "REMARKS": "メモ", "Reminder": "リマインド", "Reminder Frequency(days)": "リマインダー頻度(日数)", "Remove": "", "Remove a Reminder": "リマインダー日数の減少", "Remove Date": "削除日", "Render Error": "", "Renew": "更新", "Renewal": "自動更新", "Renewal Extraction Date": "自動継続データ抽出日", "Renewal Failure Reason": "自動継続失敗原因", "Renewal History": "更新履歴", "Renewal Policy No.": "自動継続契約番号", "Renewal Policy No. Generation Rule": "自動継続契約番号生成ルール", "Renewal Quotation Expiry Date": "自動継続失効日", "Renewal Quotation Info": "自動継続見積情報", "Renewal Reminder Date": "自動継続通知日", "Renewal Reminder Date Compare to Policy Expiry Date": "", "Renewal Reminder Rule": "", "Renewal Status": "自動継続ステータス", "Renewal UW Info": "更新引受情報", "RENEWAL_INFO": "", "RENTER": "レンター", "Reopen Comment": "Reopen Comment", "Repayment Amount": "返戻額", "represent_no": "包括契約番号", "Reprint Successfully": "再作成成功しました", "Requirement Category": "要件カテゴリ", "Requirement Code": "医療要件コード", "Requirement Descriptions": "医療要件詳細", "Requote": "", "Reset": "リセット", "Residential City": "住宅街", "Residential Status": "", "Retirement Age (Insured)": "定年退職年齢（被保険者）", "Retirement Option": "定年計画", "Retirement Option Start": "", "Retirement Option Start Date": "定年計画開始日", "Retirement option start date must be future date.": "定年計画の開始日は未来の日付である必要があります。", "retured": "差し戻す", "Return": "", "Return Current Product to Data Entry": "", "Return Entire Submission to Data Entry": "", "Return Reason": "", "Return to Data Entry": "", "Reupload": "", "Reversed": "Reversed", "Reversionary Bonus": "増額配当", "Reversionary Bonus Allocation Details": "増額配当金履歴", "RF Weight": "RFウェイト", "Riders": "特約", "Risk Aggregation": "", "Risk Aggregation Detail": "リスクアグリゲーション詳細", "Risk Category": "リスクカテゴリー", "Risk Classification": "リスク分類", "Risk Sub-category": "リスクサブカテゴリ", "Risk Underwriting Decision": "リスク査定決定", "RISK_AGGREGATION": "リスクアグリゲーション", "RiskStartDate": "始期日", "Role": "役割", "Role Name": "役割名", "Roles": "役割", "RR Weight": "RRウェート", "Rule Code": "", "Rule Code/Rule Set": "", "Rule Condition": "ルール条件", "Rule Configuration": "ルール設定", "Rule Details": "", "Rule Name": "ルール名", "Rule Result": "", "Rule Type": "", "Rule/Rule Set Category": "", "Rule/Rule Set Code": "", "Rule\\Rule Set": "ルール/ルールセット", "Running": "ランニング", "SA After Down Sell": "引下げ後の保険金", "SA Multiplier": "保険金乗数", "Sales Agreement Code": "営業契約コード", "Sales Channel": "販売チャネル", "Sales Channel Code": "販売チャネルコード", "Sales Channel Name": "チャネル名", "Sales Channel Type": "販売チャネルタイプ", "Sales Time": "販売期間", "Sales_Channel": "販売チャネル", "SALES_CHANNEL": "チャネル", "SalesChannel": "販売チャネル", "Same as": "", "Same As": "{{relatedFirstSection}}と同じ", "Same as Initial Premium Payment": "初回保険料の支払と同じ", "Same As Payer of Liability": "", "Save": "保存", "Save Failed": "保存失敗", "Save Successfully": "保存成功", "Saving Successfully!": "保存完了", "Schedule Period Type": "スケジュール期间种类", "Schedule Period Value": "スケジュール期间バリュー", "Scheduled Arrival Time": "到着予定時刻", "Scheduled Departure Time": "出発予定時刻", "Scheduled Rate": "", "Scope of Application": "求める範囲", "Search by Group Policy No.": "", "Search by Master Agreement No.": "包括契約番号で検索", "Search by Name": "", "Search by Operator": "担当者で検索", "Search Insured by Customer Type？": "被保険者の顧客カテゴリーを選択してください", "Search Name": "担当者氏名検索", "Search Policyholder by Customer Type？": "加入者の顧客カテゴリーを選択してください", "searchResult": "検索結果", "Secondary Life Insured": "第二被保険者", "SECONDARY_LIFE_INSURED": "第二被保険者", "SecondaryLifeInsured": "第二被保険者", "Segmentation Factor": "分割要素", "SEGMENTATION_FACTOR": "分割要素", "Select": "選択", "Select a manager for the team": "チームのマネージャーを選択します", "Select a Template": "", "Select all": "すべて選択", "Select All": "すべて選択", "Select Coverage / Sub Coverage": "", "Select Insured Object": "", "Select Plan Group": "", "Select Questionnaire Language": "アンケート言語の選択", "Select the process flow you want to use": "", "Select Type": "", "selectAtLeastOne": "少なくとも一つを選択します", "Send": "送信", "Send Back to Origin UWer": "元のアンダーライターに差し戻す", "Send Back to Task Pool": "", "Send Back to Task Pool failed": "", "Send Back To Task Pool success": "タスクプールへの差し戻しに成功しました", "Send back to Task Pool?": "タスクプールに戻っていいですか？", "Send Back to UW Pool": "", "send back to UW task": "査定プールへ差し戻す", "Send Time": "送信時刻", "Sender": "送信者", "Sending Status": "", "Senior Number": "シニア番号", "Seperate by Proposal Status": "申込ステータス分ける計算", "Sequence Length": "", "Sequence Value": "シーケンス値", "Sequence_No": "シーケンス番号", "Service Company": "サービス会社", "Service Company Code": "サービス会社コード", "Service Fee": "サービス料", "Service Fee Amount": "サービス金額", "Service Fee Generated Date": "サービス料金生成日", "Service Fee Type": "サービス料タイプ", "SERVICE_AGENT": "最新担当者", "Set Deductible": "", "Set Limits": "", "Set Priority": "優先度設定", "Set task push strategy for each team, such as Round Robin and task push by workload.": "各チームに総当たり戦や仕事量別のタスクプッシュなどのタスクプッシュ戦略を設定します。", "Settled": "処理済み", "SettleFlag": "フラグを立てる", "Settlement Date": "支払日", "Settlement Frequency": "支払頻度", "Settlement Start Date": "支払開始日", "Sex": "", "Short Rate Method": "", "Should higher than previous policy year": "", "Show_with_Card": "カードで表示", "Showing {{current}} to {{pageSize}} of {{total}} results": "{{total}}件中の{{current}}～ {{pageSize}}件を表示", "SINGLE": "一括払", "Single Premium": "一括払", "Single Top Up": "一括払特約", "Single Top Up Amount": "一時増額金額", "Single Top Up Collected": "一括払特約保険料領収", "Single Top Up Type": "一時増額タイプ", "Single Top Up: {{amount}}": "一時増額: {{amount}}", "Single Top-up": "", "SMS": "", "Social Account": "ソーシャルアカウント", "Sorry, failed to upload documents because Master Policy NO. hasn't been filled. Please check.": "マスターポリシー番号が未入力のため、書類のアップロードに失敗しました。ご確認ください。", "Sort": "ソート", "Sort by Application Date. (Ascending)": "", "Sort by Application Date. (Descending)": "", "Sort by Create Time": "", "Sort By Creation Date (from oldest to newest)": "作成日でソート（古い順）", "Sort by Group Policy No. (Ascending)": "", "Sort by Group Policy No. (Descending)": "", "Sort by Operation Time": "", "Sort by Proposal No. (Ascending)": "申込受付番号の順で並び替え（昇順）", "Sort by Proposal No. (Descending)": "申込受付番号の順で並び替え（降順）", "Sort by Quotation No. (Ascending)": "", "Sort by Quotation No. (Descending)": "", "Sort by Quote Need Date. (Ascending)": "見積必要日でソート (昇順)", "Sort by Quote Need Date. (Descending)": "見積必要日でソート (降順)", "Sort by time": "時間順", "Sort Times Ascending": "時間順で並べ替え", "Sort Times Descending": "時間降順で並び替え", "Sort_by_Last_Upload_Time": "並べ替え：最終更新時刻", "Sort_by_Policy_No": "保険契約で並べ替え", "Special Agreement": "特別約定", "Special Agreement Code": "特別約定コード", "Special Agreement Description": "特別約定の説明", "Special Agreement Type": "特別約定タイプ", "Special Code": "特別約定コード", "Special Description": "特別約定詳細", "SPECIAL_AGREEMENT": "特約", "SPECIAL_AGREEMENT_WITH_PLAN": "", "Specific Info": "詳細情報", "SPECIFIC_INFO": "詳細情報", "Specified Applicable Goods": "", "Stack Code": "", "Stack Description": "", "Stack Liability Name": "補償項目名称: {{liabilityName}}", "Stack Name": "スタック名称", "Stack Type": "スタックタイプ", "Stack Unit": "スタック単位", "Stack Value": "スタックの値", "Stack Value Type": "", "Stamp Duty": "印紙税", "Standard": "標準", "Standard Premium": "標準保険料", "Standard Tariff": "オープン保険契約", "Start": "", "Start | End": "", "Start by Creating a Rule": "ルール作りから始めます", "Start Date": "開始日", "Start Day": "開始日", "Start Time": "タスク開始時間", "Status": "ステータス", "STOP_PAYMENT": "ストップ", "Strategy": "方策 {{ name }}", "Strategy Asset Allocation": "", "Strategy Code": "戦略コード", "Strategy Detail": "戦略詳細", "Strategy Name": "投資戦略名", "Strategy Name (auto generate)": "方策名（自動生成）", "Strategy Relatives": "戦略の関連要因", "Street Name": "道の名前", "Street No.": "通り番号", "Structure": "構造", "Sub Campaign Category": "サブキャンペーンカテゴリ", "Sub Coverage": "", "Sub Policy (Main)": "", "Sub Policy (Relative)": "", "Sub Policy Info": "", "Sub Policy No.": "", "Sub Policy No. {{policyNo}}": "Sub Policy No. {{policyNo}}", "Sub Policy Status": "", "Sub Standard": "サブスタンダード", "Sub Total": "合計", "SUB_STANDARD_CODE": "サブスタンダードコード", "SUB_STANDARD_RECORD": "サブスタンダードレコード", "Sub-items": "サブアイテム", "Sub-standard Code": "", "Sub-standard Code List": "", "Subject": "件名", "Submission No": "", "Submission No.": "", "Submit": "確認", "Submit Failed": "送信に失敗しました", "Submit Failed: {{message}}": "", "Submit successfully": "", "Submit Successfully": "处理成功", "Submit Tips": "トップスの提出", "Substandard Code": "サブスタンダードコード", "success": "", "Successful": "成功", "Successful Records": "", "successfully": "成功", "Sum Assured": "保険金額", "Sum_Assured_with_free_amount": "保険金額（おまけ分込み）", "Sum_Assured_without_free_amount": "保険金額", "Summary": "", "Sure": "確定", "Survival Benefit": "生存給付金", "Survival Benefit Account Transaction Detail": "生存給付金口座トランザクション詳細", "Survival Benefit Payment Account": "生存給付金の支払頻度", "Survival Benefit Payment Frequency": "生存給付金の支払い方法", "Survival Benefit Payment Option": "生存給付金の支払い方法", "Suspend Reason": "一時停止の理由", "Suspension Certificate": "一時停止証明書", "Sustav će koristiti Ovdje konfiguriranu Opću odluku pravila i pokrenuti tijek prijedloga na temelju ove odluke. Ako se odbije\"": "システムはここで設定された一般的な決定ルールを使用し、この決定に基づいて提案プロセスを開始します。もし拒否されたら。", "SWIFT Code": "金融機関SWIFTコード", "Switch Confirm": "", "Switching customer types will clear existing customer data, please confirm.": "顧客カテゴリーを変更すると、既存の顧客情報がクリアされますので、ご確認ください。", "Switching the plan group will clear the selected products. Please confirm.": "プラングループを切り替えると、選択された製品がクリアされますので、ご確認ください。", "Symbol not matched, please check GeneralSymbols.": "シンボルが一致していません。総シンボルを確認してください。", "System error": "", "System Error": "システムエラー", "System generates": "", "System logon user is different from the case handler. please check!": "現在のユーザは案件担当者と不一致です、確認してください。", "System Source": "システム別", "System will trigger automatically confirm the policy sign off X days after policy issue date.": "契約加入後Ｘ日に、自動的に締結されます。", "System will trigger reminder notification when the proposal stays in below status after X days.": "見積もり契約の状態はX日に保留する場合、システムリマインダーを送信する。", "System will trigger the proposal flow based on the 'Underwriting Decision' of the rule configuration here. If declined, system will reject the proposal. And if manual, system will trigger manual underwriting check for this proposal.": "", "System will trigger the proposal flow based on the \"Compliance Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will trigger manual compliance check for this proposal.": "", "System will trigger the proposal flow based on the \"Verification Decision\"  of rule configured here. If declined, system will reject the proposal. And if manual, system will send the proposal to manual verification.": "", "Tag": "タグ", "Tag Name": "", "Target Return(%)": "", "Target Rule No": "ターゲットルール番号", "Target Rule No.": "", "Target Volatility(%)": "", "Task Assignment Rule": "タスク割当ルール", "Task Assignment Strategy": "タスク割当ストラテジー", "Task Create Date": "タスク作成日", "Task No.": "タスクNo.", "Task Pick Up": "", "Task Push Strategy": "タスクプッシュ戦略", "Task Push Strategy has been configured": "タスクプッシュ戦略が設定されました", "Task Push Supplementary Strategy": "タスクプッシュ追加戦略", "Task Status": "タスク状態", "Task successfully assigned to the user.": "タスクがユーザーに正常に割り当てられました", "Task Successfully Withdrawn": "タスク取消完了", "Task Type": "タスクタイプ", "Tax": "税金", "Tax Amount": "税金金額", "Tax Info": "税金情報", "Tax Rate/Value": "税率/税金額", "Tax Setting": "税金設定", "Tax Type": "税金タイプ", "TB Type": "消滅配当種類", "Team Maintenance Type": "チーム管理タイプ", "Team Management": "チーム管理", "Team Members": "チームメンバー", "Team Name": "チーム名", "Team Name: {{name}}": "", "Team(s) for the stragegy": "戦略のチーム", "Terminal Bonus": "消滅配当", "Terminated": "終了しました", "Terminated Reason": "無効理由", "Termination": "", "Termination Date": "終了日", "Termination Date(Lapsed Date)": "保険契約終了日（失効日）", "Termination Reason": "無効理由", "TerminationDate": "終了日", "TerminationReason": "終了理由", "text": "", "text_select": "", "The Amount input should less than original sum assured of the liability.": "", "The Amount input should less than original sum assured of the product.": "", "The amount is consists of 3 parts： Planed Premium,Single Top-Up and Regular Top-Up.": "金額は3つの部分（予定保険料、一括払特約、定期保険料）から構成されています。", "The customer should pay the premium before issuing the policy .": "", "The date & benefit amount list below is calculated based on current policy info. If there is any further policy change occurred, the real benefit date & amount may change.": "下記の日付および生存給付金は現在の契約状況による計算された。もし異動発生する場合、下記情報変更可能性はあります", "The export may take a long time, you can download the Excel Fails when the status is completed.": "エクスポートに少し時間がかかります。ステータスが完了になったら、Excelファイルをダウンロードしてください。", "The file is still being generated. Please wait a moment.": "ファイルはまだ生成中です。しばらくお待ちください。", "The Installment premium is changed from ": "被保険者情報変更で保険料が", "The issuance of proposal {{relatedPolices} depends on proposal {{issuanceNo}}. If the decline of this underwriting task results in the withdrawal of proposal {{issuanceNo}}, proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "保険証券{{relatedPolices}}の発行は保険証券{{issuanceNo}}に依存しています。本引受業務を拒否した場合、保険証券{{issuanceNo}}が取り消されると、保険証券{{relatedPolices}}も同時に取り消されますので、ご確認ください。", "The issuance of the proposal {{relatedPolices}} depends on this proposal. If this proposal is withdrawn, the proposal {{relatedPolices}} will also be withdrawn simultaneously. Please confirm.": "保険証券{{relatedPolices}}の発行はこの保険証券に依存しています。この保険証券が取り消された場合、保険証券{{relatedPolices}}も同時に取り消されますので、ご確認ください。", "The location cannot be the same, please check.": "", "The modification of the policy information did not pass the compliance verification. The proposal is canceled, and the underwriting task is closed. Please confirm.": "保険契約情報の変更がコンプライアンス検証に合格していません。申し込みがキャンセルされ、引受任務が終了しました。確認してください。", "The modification of the policy information has triggered a manual compliance task. Please wait for the submission of the compliance task before continuing with the underwriting task.": "保険契約情報の変更が人的コンプライアンスタスクを触発しました。引受任務の処理を続ける前に、まずコンプライアンスタスクの提出を待ってください。", "The net premium has changed": "", "The object names cannot be the same, please check.": "オブジェクトの名前は同じにできません。ご確認ください。", "The other process is explained in Withdraw": "もう1つのプロセスは取り下げで説明されています。", "The payment information for this product has not been entered. Please confirm.": "この製品の支払い情報が入力されていません。確認してください。", "The policy data loaded from master policy will be deleted. Do you want to continue?": "", "The policy data loaded from master policy will be refreshed according to new master policy number. Do you want to continue?": "", "The policy does not exist.": "", "The policy has been issued successfully.": "", "The policy is not within renewal extraction period, please confirm whether to raise renewal.": "", "The policyholder you selected has not been created yet. Please confirm.": "選択した契約者がまだ作成されていません。ご確認ください。", "The POS application is already withdrawn. You don't need to underwrite it anymore.": "異動申込は取消されました、査定不要です。", "The Premium Has Changed": "被保険者情報変更に伴い", "The proposal has been sent to New Quote.": "", "The proposal has been sent to Quote Bound.": "", "The proposal has been submitted to manual underwriting.": "", "The proposal has been successfully withdrawn.": "この申込書は正常に取り消されました。", "The proposal has failed automated underwriting and was declined by the system.": "", "The proposal has failed automated underwriting due to the changes.": "", "The proposal has failed automated underwriting.": "", "The proposal has Lapsed.": "", "The proposal has passed automated underwriting.": "", "The proposal is already declined or postponed by underwriter. You don't need to perform verification anymore.": "申込契約は拒否または延期されました、2次査定不要です。", "The proposal is already reject by manual compliance user. You don't need to perform verification anymore.": "申込契約はコンプライアンス ユーザーによって拒否されました、2次査定不要です。", "The proposal is already withdrawn (by client, channel or auto withdrawn by the company). You don't need to perform verification anymore.": "申込契約は顧客、アライアンス先または保険会社から取消されました、2次査定不要です。", "The proposal is already withdrawn. You don't need to underwrite it anymore.": "申込契約は取消されました、査定不要です。", "The proposal will lapse, are you sure to continue？": "", "The renewal proposal is generated successfully. Proposal No.{{No}}": "更新後の保険契約生成されました。保険契約番号：{{No}}", "The renewal quotation is generated successfully. Quotation No.{{No}}.": "更新後保険料が生成されました。試算番号：{{No}}", "The renewal validation failed, please check whether the current policy meets the renewal conditions.": "自動更新が失敗した、現在の保険契約が更新条件を満たしているかどうかを確認してください。", "The required Master Agreement information is insufficient to upload the file.": "必要な包括契約情報が不足しているため、ファイルをアップロードできません。", "The same person as Policyholder": "", "The same Plan already exists": "", "The same Plan name already exists under the current policy, please modify and submit! ": "", "The selected coverage/sub coverage and insured object have been configured with the corresponding deductible. Please confirm.": "選択された補償/補償項目と標的には、対応する免責額が設定されています。ご確認ください。", "The selected coverage/sub coverage and insured object have been configured with the corresponding limit. Please confirm.": "選択された補償/補償項目と標的には、対応する限度額が設定されています。ご確認ください。", "The team selected above will be brought in here": "上記で選択されたチームがここに招集されます。", "The updated information will not be saved, are you sure to back?": "", "The user who appointed as team manager is deleted, please reset team manager if needs.": "チームマネージャーに任命されたユーザーは削除されます。必要に応じてチームマネージャーを再設定してください。", "The verification task has been submitted.": "二次査定結果が保存しました。", "There is duplicate exclusion record exist. Please check": "重複した除外記録がありますので、ご確認ください。", "There is un-complete medical examination request or pending issue request, confirm to submit manual UW?": "未完了の健康診断リクエストまたは保留中の問題リクエストがありますが、手動査定を提出してもよろしいですか？", "Third Party Collection": "第三者による入金", "Third Party Transaction No": "第三者トランザクション番号", "This case is escalated from {{user}}.": "このケースは{{user}}からエスカレーションされました。", "This document contains vehicle information, premium details and riders information.": "", "This factor is an enumeration type, but enumKey is missing.": "この因子は列挙型で、そのenumKeyが見つかりません。", "This liability is mutually-exclusive with {{tipsText}}": "この補償内容は{{tipsText}}と相互排他的な関係があります。", "This rule has been binded by team {{teamNames}}, please unbind from team first.": "", "This team has been binded by strategy {{StrategyNames}}, please unbind from strategy first.": "このチームはすでに{{StrategyNames}}とバインドされているため、まずこのストラテジーからのバインドを解除してください。", "This underwriting case is currently under {{user}}. Reassigning it may affect the progress of tasks being processed. Please confirm.": "この引受ケースは現在{{user}}の下にあります。再割り当ては処理中のタスクの進行に影響を与える可能性がありますので、確認してください。", "Threshold for Rebalancing": "", "Ticket": "チケット", "Ticket Number": "チケット番号", "Ticket Price": "チケット代", "Ticket Type": "チケットタイプ", "Times": "回数", "Times Types": "", "Tips": "ヒント", "Title": "タイトル", "to": "から", "To be expired": "期限切れになる", "Tonnage": "トン数", "Top Up Due Date": "定期増額の払込応当日", "Top Up Period:": "トップアップ期間: ", "Total": "合計", "Total {{count}} {{objectName}}s": "", "Total Allocated Bonus": "合計ボーナス配分", "Total Amount": "合計金額", "Total Beneficiary ratio should be equal to 100%": "受取人の合計割合は100％でなければなりません。", "Total Campaign Discount": "キャンセル割引総額", "Total CB Allocation": "現金配当金合計", "Total Claim Amount": "総計支払金額", "Total Commission Amount": "手数料総額", "Total Extra Loading": "追加補償費用の合計額", "Total Fund Value": "総ファンドバリュー", "Total Installments": "", "Total Insured No": "", "Total Insured No.": "", "Total Investment Amount": "合計投資金額", "Total items": "合計 {{total}}", "Total Loan Balance": "貸付残高合計", "Total Outstanding Premium": "", "Total Paid Premium": "合計支払保険料", "Total Premium": "総保険料", "Total Premium Amount": "総保険料", "Total Premium Amount Detail": "総保険料詳細", "Total Premium Amount Details": "総保険料詳細", "Total Premium Collected": "合計保険料を領収", "Total Premium Name": "", "Total Price": "総計金額", "Total Primary Insured No.": "", "Total Principal Amount": "合計元金金額", "Total Product Discount": "商品割引総額", "Total Refund Premium": "合計返還保険料", "Total Risk Amount": "リスク総額", "Total Risk Amount Details": "リスク総額詳細", "Total Sub Policy": "", "Total Sum Assured": "", "Total Tax": "税金総額", "Total TIV": "総保険価値（TIV）", "Total Unpaid Premium (due & undue)": "未払い保険料総額（支払期日到来分および未経過分）", "total_amount": "総額", "TOTAL_PREMIUM": "総保険料", "Total: {{amount}} insured": "", "Total: {{total}}": "合計: {{total}}", "Total: {{total}} Strategies": "合計: {{total}} ストラテジー", "Total: Records": "合計: {{total}} 件", "Transaction": "トランザクションタイプ", "Transaction Amount": "トランザクション金額", "Transaction Date": "取引日", "Transaction Effective Date": "取引有効日", "Transaction Efffective Date": "", "Transaction Name": "取引名", "Transaction No.": "取引番号", "Transaction Status": "トランザクション状態", "Transaction Time": "トランザクション時間", "Transaction Type": "取引タイプ", "Transaction Unit": "トランザクション口数", "TransactionDate": "取引日", "Transation Effective Date": "", "Transit ID": "通行 ID", "Transport Information": "交通情報", "Transportation Number": "取引時間", "Transportation Type": "取引ユニット", "Transportion_No": "取引日", "Travel Agency": "旅行社", "Travel End Date": "旅行終了日", "Travel Expense": "旅行費用", "Travel Info": "旅行情報", "Travel Order Number": "オーダー番号", "Travel Order Type": "予約種類", "Travel Start Date": "旅行開始日", "Travel Type": "旅行種類", "TRAVEL_OBJECT_INFO": "", "Trip Info": "旅行情報", "Trip Type": "片道/往復", "Trustee": "受託者", "TRUSTEE": "受託者", "Turn back to Manual UW Task Pool": "マニュアル査定タスクプールに戻ります", "Type": "タイプ", "Type a Comment...": "", "Type of Business": "", "Unanswered question exists, please confirm.": "未回答の質問があります。ご確認してください。", "Underwriter": "査定担当者", "Underwriter Name": "アンダーライター名", "underwriting": "査定", "Underwriting": "査定", "Underwriting Authority": "査定権限", "Underwriting case is under review.": "", "Underwriting Check": "査定チェック", "Underwriting Check Text": " システムは、ここで設定された「査定決定」ルールより査定結論を送信するまたはマニュアル査定に送信する。査定結論は商品（特約）単位を出す。", "Underwriting Configuration": "査定設定", "Underwriting Criteria": "査定条件", "Underwriting History": "", "Underwriting Level": "引受レベル", "Underwriting Strategy": "査定ストラテジー", "Underwriting Task": "", "UNDERWRITING_DECISION": "査定決定", "UNDERWRITING_TAG": "査定タグ", "Unit": "ユニット", "Unit Adjustment": "", "Unit No.": "ユニット番号", "Unit No. and Building Name": "ユニット番号と建物名", "Unit Premium": "", "Units To Be Adjusted": "調整単位", "Universal Saving Account": "万能預貯金口座", "Unnamed Insured": "", "Unpaid Amount": "未払い保険料", "Update Date": "更新日", "Update OCR Result": "OCR読み取り結果をアップロード", "Updated at": "", "updateTime": "", "updateUser": "", "Upload": "アップロード", "Upload Application Form": "申し込みフォームのアップロード", "Upload Attachment": "添付ファイルアップロード", "Upload Date": "", "Upload Document": "ファイルアップロード", "Upload Failed": "アップロード失敗", "Upload Invoice": "請求書アップロード", "Upload New Document": "新しいファイルアップロード", "Upload Result": "", "Upload Successfully": "アップロード成功", "Upload Time": "アップロードタイム", "Uploading": "", "Usage": "", "Usage Based Premium Detail": "", "Usage Code": "コード", "Usage Upload": "利用状況アップロード", "Usage Upload History": "利用状況のアップロード履歴", "Use Sub-item": "サブアイテムを使用します", "User": "ユーザー", "User List": "ユーザーリスト", "User Name": "ユーザー名", "UW Case Operation": "査定ケース操作", "UW Case Required Level": "保険引受義務レベル", "UW Comments": "査定コメント", "UW Criteria": "", "UW Critieria Standard": "査定標準", "UW Decision": "査定決定", "UW Decision Detail": "査定決定詳細", "UW Decision Details": "引受決定詳細", "UW Decision History": "", "UW Entry Date": "", "Uw History": "アンダーライティング情報", "UW History": "査定履歴", "UW in Process": "査定中", "UW Message": "引受情報", "UW Owner": "", "UW Query": "査定情報検索", "UW Stage": "査定段階", "UW Task": "査定タスク", "UW Task No": "引受タスクナンバー", "UW Task will be submitted.": "査定タスクが提出されます。", "UW_Info": "アンダーライティング情報", "UW_INFO": "査定情報", "UW_OPERATION": "査定操作", "V": "Ｖ", "Valid": "", "Valid input: {{minValue}} to {{maxValue}}": "", "Value": "バリュー", "Value Type": "", "Vehicle": "", "Vehicle Additional Equipment": "", "Vehicle Age": "車両年齢", "Vehicle Capacity": "車両容量", "Vehicle Color": "車両の色", "Vehicle Damaged": "", "Vehicle Examination Area": "", "Vehicle Examination Way": "", "Vehicle Info": "車両情報", "Vehicle Inspection Information": "車検情報", "Vehicle Loan": "", "Vehicle Make": "車両メーカー", "Vehicle Model": "車両モデル", "Vehicle Plate No.": "自動車登録番号", "Vehicle Premium Detail Download": "", "Vehicle Structure": "車両構造", "Vehicle Type": "車両タイプ", "Vehicle Usage": "車両の使用", "VEHICLE_INFO": "車両情報", "VEHICLE_LIST_UPLOAD": "車両リストアップロード", "Verification Check": "二次査定", "Verification Comment": "", "Verification Decision": "", "Verification Detail": "", "Verification Fail": "申込査定失敗", "Verification failed,please upload again.Upload vehicle statistics:": "検証に失敗しました。再度車両統計をアップロードしてください：", "Verification failed.": "", "Verification History": "", "Verification Pass": "申込査定成功", "Verification Reason": "検証理由", "Verification Task Pool": "本人確認タスクプール", "Verification Task Pool Re-assign": "２次査定タスクプール割当", "Verification task will be submitted.": "", "Verification/Compliance/UW Process Flow Configuration": "二次査定/コンプライアンス/マニュアル査定プロセスフロー設定", "Verify success.Upload vehicle statistics:": "検証に成功しました。車両統計をアップロードしてください：", "Version": "バージョン", "Vesting age is invalid if it is earlier or equal to insured entry age.": "ベスティング年齢が被保険者の加入年齢より以降となる場合は無効です。", "Vesting: {{vestingAge}}": "ベスティング: {{vestingAge}}", "view": "", "View": "照会", "View All": "", "View Attachment": "添付ファイルを照会", "View Deductible": "", "View Detail": "詳細の照会", "View History": "", "View Liability": "補償内容照会", "View Limits": "", "View More": "詳細を見る", "view my task": "担当者タスクへ", "View Policy Detail": "保険契約詳細を表示 ", "view public task": "全部タスクへ", "View Tax Details": "", "View the photocopy in a new browser page": "新しいブラウザページで写真コピーを表示", "View Withdrawal Schedule": "減額スケジュールを表示", "ViewAll": "全て確認", "VIN No": "車両識別番号", "Vin No.": "ヴィン番号", "Waiting Days to Withdraw": "取下げの待機期間", "Waiting Effective": "有効待ち", "Waiting For Compliance": "コンプライアンス待ち", "Waiting for process": "査定待ち", "Waiting For Underwriting": "加入審査待ち", "Waiting For Verification": "二次査定待ち", "Waiting_for_Acceptance": "受付待", "Waiting_for_Approval": "承認待", "Waiting_for_Evaluation": "査定待", "Waived": "免除", "waiver": "免除", "WAIVER_PAYMENT": "免除", "Weekly": "毎週", "Weight": "重さ", "When the policy comes to the end of the policy period, the system will run a regular batch to set the policy status to Terminated": "システムは、バッチ処理によって満期の保険契約のスターテスを消滅に設定します", "When you change it, the current data content will be cleared. Are you sure to change it?": "", "Whether need to double check for rejected case": "拒否ケースのダブルチェック要否", "WHOLELIFE": "終身", "Width of Vehicle": "車両の幅さ", "With Open Issue": "処理待ちの問題あり", "With Open Pending Case": "ペンディングケースあり", "With Open Pending Issue": "未決ペンディング事項あり", "With Pending Case": "そこにはペンディングケース", "withDraw": "取下げ", "Withdraw Proposal": "申込を取り消す", "Withdraw Reason": "取消事由", "Withdraw Reason Content": "", "Withdraw Task": "タスク取消", "Withdraw the Task": "タスク取消", "Withdrawal Amount": "減額", "Withdrawal By Amount": "減額金額", "Withdrawal Due Date": "減額期日", "Withdrawal Period": "減額期間", "Withdrawal Reason": "<PERSON><PERSON><PERSON> Reason", "Withdrawal Reason Content": "Withdrawal Reason Content", "Withdrawal Schedule": "減額スケジュール", "Withdrawal Successful": "", "Withdrawed successfully": "取り消しました", "withdrawn": "取下げ", "Within {{Product}}, some responsibilities have mutually exclusive relationships, please check.": " {{Product}} 内には、一部の責任に相互排他的な関係がありますので、確認してください。", "Within Premium Holiday": "保険料支払い一時停止期間内", "Witness": "証人リスト", "Witness Info": "証人情報", "Work Injury Compensation": "労働災害補償", "Workflow": "ワークフロー", "Write Off Amount": "差引額", "Year": "年", "YEAR": "毎年", "Year of Manufacturing": "製造年", "year(s)": "", "Year(s)": "", "Yearly": "毎年", "yes": "はい", "You can click to amend the proposal information. The amendment will be synchronised to the proposal and may trigger other checks.": "クリックすると、申込情報を修正できます。修正内容は申込情報に同期され、他のチェックが発生する可能性があります。", "You can download the file and check detail reason.": "ファイルダをウンロードして、詳細原因を確認してください。", "You can only upload PDF/DOC/XLS/PNG/JPG": "アップロードできるのはPDF / DOC / XLS / PNG / JPGのみです", "You can only upload PDF/EXCL/DOC/XLS/PNG/JPG": "PDF/EXCL/DOC/XLS/PNG/JPGのみアップロード可能です。", "You can only upload PDF/PNG/JPG/TIFF/ZIP": "PDF/PNG/JPG/TIFF/ZIPのみアップロード可能です。", "You can only upload XLS": "XLSをアップロードしてください", "You can only upload xlsx": "xlsxをアップロードしてください", "You can only upload XLSX": "XLSXをアップロードしてください", "You can query out the task in UW query after being withdrawn . But no further action allowed. Confirm to withdraw it?": "", "You can query out the task in UW query after being withdrawn . But no furthur action allowed. Confirm to withdrawn it?": "", "You have been assigned {X} tasks": "{{x}}個のタスクーが割合されました", "You have changed master policy information and will impact uploaded vehicle data. Do you want to re-upload the vehicle list?": "マスター契約が変更されて、アップロード済みの車両ファイルへ影響があるので、車両ファイルを再度アップロードする必要がありますか？", "You have some proposals under the same relation number. You can select the corresponding proposals and issue them together.": "同じ関連番号の下にいくつかの提案があります。該当する提案を選択して一緒に発行することができます。", "Zip Code": "郵便番号"}