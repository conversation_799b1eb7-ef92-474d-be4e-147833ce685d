@import './variables.scss';

.antd-finance-center {

  // 使用nagrand Table时wrapper也有border, 因此隐藏一个，替换完Table组件可以删掉这里和#{$antd-prefix}-table-container的border
  :global {
    .nagrand-table-container .#{$antd-prefix}-table-container {
      border: none;
    }

    .nagrand-table-container .#{$antd-prefix}-table-tbody .#{$antd-prefix}-table-cell .nagrand-ellipsis {
      width: 100% !important;
    }

   // nagrand antd4样式跟当前antd5有差异，需要覆盖  后期可以删除
    .nagrand-actions.#{$ant-prefix}-btn-icon-only .anticon {
      font-size: var(--font-size-lg) !important;
    }

    // 下面.#{$antd-prefix}-btn-link样式覆盖nagrand的link样式
    .nagrand-query-form {
      .#{$antd-prefix}-btn-link {
        color: var(--primary-color);
      }
    }

    // nagrand 中设置了Modal的header 和 content的border, 没有title或者content时希望border为none
    .nagrand-method-modal {

      .#{$antd-prefix}-modal-confirm-content:not(:has(+ .#{$antd-prefix}-modal-confirm-header)),
      .#{$antd-prefix}-modal-confirm-content:not(:has(~ .#{$antd-prefix}-modal-confirm-header)) {
        border-style: none;
        margin-top: 0;
      }

      .#{$antd-prefix}-modal-confirm-header:not(:has(+ .#{$antd-prefix}-modal-confirm-content)),
      .#{$antd-prefix}-modal-confirm-header:not(:has(~ .#{$antd-prefix}-modal-confirm-content)) {
        border-style: none;
      }
    }

    // nagrand 中设置了Modal的header 和 content的border, 没有title或者content时希望border为none
    .nagrand-modal {

      .#{$antd-prefix}-modal-confirm-content:not(:has(+ .#{$antd-prefix}-modal-confirm-header)),
      .#{$antd-prefix}-modal-confirm-content:not(:has(~ .#{$antd-prefix}-modal-confirm-header)) {
        border-style: none;
        margin-top: 0;
      }

      .#{$antd-prefix}-modal-confirm-header:not(:has(+ .#{$antd-prefix}-modal-confirm-content)),
      .#{$antd-prefix}-modal-confirm-header:not(:has(~ .#{$antd-prefix}-modal-confirm-content)) {
        border-style: none;
      }

      .#{$antd-prefix}-modal-content:has(.#{$antd-prefix}-modal-body:empty) .#{$antd-prefix}-modal-header {
        border-style: none;
      }
    }

    .#{$antd-prefix}-btn-text:not(:disabled):not(.#{$antd-prefix}-btn-disabled):hover {
      color: var(--primary-color);
      background: transparent;
      }
      
      .nagrand-table-container {
        // table 中使用了Form.Item的情况，在没有error的时候去除默认的margin-bottom: 24px
        .#{$antd-prefix}-table-cell {
          .#{$antd-prefix}-form-item:not(.#{$antd-prefix}-form-item-has-error) {
            margin-bottom: 0;
          }
      
          &:has(.#{$antd-prefix}-form-item-has-error) {
            height: 76px;
          }
        }
      }
  }

  :global {
    .#{$antd-prefix}-btn-link {
      color: var(--text-color);
    }

    .#{$antd-prefix}-btn-link[disabled] {
      color: var(--disabled-color);
    }

    .#{$antd-prefix}-table-container {
      border: 1px solid var(--border-light);
    }

    .#{$antd-prefix}-form-vertical .#{$antd-prefix}-form-item-label {
      margin-bottom: var(--gap-xss);
      padding-bottom: 0;
    }

    .#{$antd-prefix}-table-thead>tr>th:not(:last-child):not(.#{$antd-prefix}-table-selection-column):not(.#{$antd-prefix}-table-row-expand-icon-cell):not([colspan])::before {
      display: none;
      font-weight: bold;
    }

    .#{$antd-prefix}-table-thead>tr>th,
    .#{$antd-prefix}-table-tbody>tr>td,
    .#{$antd-prefix}-table tfoot>tr>th,
    .#{$antd-prefix}-table tfoot>tr>td {
      padding: var(--gap-md) var(--gap-lg);
      line-height: 16px;
    }

    .#{$antd-prefix}-table-tbody {

      >tr.#{$antd-prefix}-table-row-hover:not(.#{$antd-prefix}-table-expanded-row):not(.#{$antd-prefix}-table-row-selected),
      >tr:hover:not(.a#{$antd-prefix}-table-expanded-row):not(.#{$antd-prefix}-table-row-selected):not(.#{$antd-prefix}-table-placeholder) {
        >td {
          background-color: var(--item-bg-hover);
        }
      }

      .#{$antd-prefix}-table-cell:empty:before {
        content: '--';
      }

      .#{$antd-prefix}-table-cell-ellipsis span:empty:after {
        content: '--';
      }
    }

    // nagrad table sorter-columns flex布局为justify-content: start，设置了column align: right不生效，这里进行覆盖。nagrand样式更新后此处可删除
    .#{$antd-prefix}-table-column-has-sorters[style*='text-align: right']>.#{$antd-prefix}-table-column-sorters {
      justify-content: end;
    }

    .#{$antd-prefix}-descriptions-item {
      padding-bottom: var(--gap-xs);
    }

    .#{$antd-prefix}-card-body {
      padding: var(--gap-md) var(--gap-lg) var(--gap-lg);
    }

    .#{$antd-prefix}-tabs-tab-btn {
      width: 100%;
      text-align: center;
    }

    .#{$antd-prefix}-tabs-nav .#{$antd-prefix}-tabs-tab {
      padding-top: var(--gap-md);
      padding-bottom: var(--gap-xss);
    }

    .#{$antd-prefix}-btn-lg {
      font-size: 14px;
    }

    .#{$antd-prefix}-drawer-footer {
      text-align: right;
      padding: 20px var(--gap-big);

      button {
        min-width: $min-btn-width;
        margin-left: var(--gap-md);
      }
    }

    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-item:hover,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-submenu:hover,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-item-active,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-submenu-active,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-item-open,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-submenu-open,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-item-selected,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)>.#{$antd-prefix}-menu-submenu-selected {
      border-bottom: 2px solid var(--primary-color);
      font-weight: 700;

      &:after {
        display: none;
      }
    }

    .#{$antd-prefix}-menu-item:hover,
    .#{$antd-prefix}-menu-submenu-title:hover {
      color: var(--menu-item-hover-color) !important;
    }

    .#{$antd-prefix}-menu-item-selected {
      background-color: var(--menu-item-selected-bg) !important;
      font-weight: 700;
    }

    .#{$antd-prefix}-pro-table-list-toolbar-container {
      justify-content: start;
      padding: var(--gap-big) 0 var(--gap-xs) 0;
    }

    .#{$antd-prefix}-pagination-total-text {
      color: var(--text-color-tertiary);
      font-size: 14px;
      margin-right: auto;
    }

    .#{$antd-prefix}-drawer-body {
      color: var(--text-color);
    }

    .#{$antd-prefix}-drawer-header-title {
      flex-direction: row-reverse; // close to the right

      &>button {
        margin-right: 0;
      }
    }

    .#{$antd-prefix}-descriptions-item-container:empty:before {
      content: '--';
    }

    .#{$antd-prefix}-modal-title {
      font-weight: 700;
      line-height: 26px;
    }


    .#{$antd-prefix}-form-item .#{$antd-prefix}-form-item-label>label {
      color: var(--text-color);
    }

    // nagrand Modal
    .#{$antd-prefix}-modal.nagrand-modal {
      .#{$antd-prefix}-modal-close {
        top: 0;
        right: 0;
        inset-inline-end: 0;
        width: 64px;
        height: 64px;

        .#{$antd-prefix}-modal-close-x {
          display: block;
        }
      }

      .#{$antd-prefix}-modal-confirm-paragraph {
        row-gap: 0;
      }
    }

    // ghost 按钮disabled的背景色被ghost的transparent覆盖了，需要再覆盖一下
    .#{$antd-prefix}-btn-primary.#{$antd-prefix}-btn-background-ghost:disabled {
      background-color: var(--primary-disabled-color);
    }
  }
}

:global {
  .#{$antd-prefix}-drawer {
    .#{$antd-prefix}-table-tbody {

      >tr.#{$antd-prefix}-table-row-hover:not(.#{$antd-prefix}-table-expanded-row):not(.#{$antd-prefix}-table-row-selected),
      >tr:hover:not(.a#{$antd-prefix}-table-expanded-row):not(.#{$antd-prefix}-table-row-selected):not(.#{$antd-prefix}-table-placeholder) {
        >td {
          background-color: var(--item-bg-hover);
        }
      }

      .#{$antd-prefix}-table-cell:empty:before {
        content: '--';
      }

      .#{$antd-prefix}-table-cell-ellipsis span:empty:after {
        content: '--';
      }
    }

    .#{$antd-prefix}-descriptions-item-container:empty:before {
      content: '--';
    }
  }
}