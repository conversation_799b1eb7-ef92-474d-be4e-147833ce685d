import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Divider, Form, message } from 'antd';

import { CommonForm, Drawer, FieldType, Steps } from '@zhongan/nagrand-ui';

import type { OverduePayment } from 'genesis-web-service';
import {
  AccountInterestService,
  ProductCategoryEnumList,
} from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';

import { Config as InterestConfig } from '../InterestRateContent/Config';
import styles from '../style.scss';

enum Step {
  AccountInfo = 0,
  InterestDetails = 1,
}

interface OverduePaymentDrawerProps {
  editedOverduePayment: OverduePayment;
  visible: boolean;
  closeDrawer: () => void;
  onSubmitCallback: () => void;
  readonly: boolean;
}

export const OverduePaymentDrawer: React.FC<OverduePaymentDrawerProps> = ({
  editedOverduePayment,
  visible,
  closeDrawer,
  onSubmitCallback,
  readonly,
}) => {
  // 控制是添加模式还是编辑模式
  const isAdd = !editedOverduePayment;
  const { t } = useTranslation('finance');
  const [form] = Form.useForm();
  const [submitButtonLoading, setSubmitButtonLoading] =
    useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState(Step.AccountInfo);
  const [interestConfigId, setInterestConfigId] = useState<number>();
  const [informationSaved, setInformationSaved] = useState(false);

  const enums = useTenantBizDict();

  const fields = useMemo(
    () => [
      {
        key: 'overduePaymentType',
        label: t('Overdue Payment Type'),
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('finance.common.required', {
              label: t('Overdue Payment Type'),
            }),
          },
        ],
        extraProps: {
          options: enums?.overduePaymentType,
          disabled: readonly,
        },
      },
      {
        type: FieldType.Customized,
        col: 24,
        render: (
          <>
            <Divider />
            <div className={styles.sectionHeader}>{t('Interest Info')}</div>
          </>
        ),
      },
      {
        key: 'interestType',
        label: t('Interest Type'),
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('finance.common.required', {
              label: t('Interest Type'),
            }),
          },
        ],
        extraProps: {
          options: enums?.investAccountInterestType,
          disabled: readonly,
        },
      },
      {
        key: 'overdueInterestCalculationDateType',
        label: t('Interest Calculation Date'),
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('finance.common.required', {
              label: t('Interest Calculation Date'),
            }),
          },
        ],
        extraProps: {
          options: enums?.overdueInterestCalculationDate,
          disabled: readonly,
        },
      },
    ],
    [t, enums, readonly]
  );

  useEffect(() => {
    if (editedOverduePayment) {
      form.setFieldsValue(editedOverduePayment);
    }
  }, [editedOverduePayment]);

  const handleSubmit = useCallback(() => {
    if (currentStep === Step.AccountInfo) {
      if (readonly) {
        setInterestConfigId(editedOverduePayment?.id);
        setCurrentStep(Step.InterestDetails);
        return;
      }
      form.validateFields().then(values => {
        setSubmitButtonLoading(true);
        let request: Promise<number>;
        if (isAdd) {
          request = AccountInterestService.addOverduePayment(values);
        } else {
          request = AccountInterestService.updateOverduePayment(
            editedOverduePayment?.id,
            values
          );
        }
        request
          .then(id => {
            message.success(t('Save successfully'));
            setInformationSaved(true);
            setCurrentStep(Step.InterestDetails);
            setInterestConfigId(id);
          })
          .catch((error: Error) => message.error(error?.message))
          .finally(() => setSubmitButtonLoading(false));
      });
    }
  }, [currentStep, readonly, form, editedOverduePayment?.id, isAdd, t]);

  const onCloseDrawer = useCallback(() => {
    if (informationSaved) {
      onSubmitCallback();
    }
    setInformationSaved(false);
    setCurrentStep(Step.AccountInfo);
    form.resetFields();
    closeDrawer();
  }, [informationSaved, form, closeDrawer, onSubmitCallback]);

  const stepItems = useMemo(
    () => [
      {
        title: t('Overdue Payment Information'),
      },
      {
        title: t('Interest Details'),
      },
    ],
    [currentStep, t]
  );

  return (
    <Drawer
      title={
        isAdd
          ? t('Add New Overdue Payment Type')
          : t('Overdue Payment Type Configuration')
      }
      closable={false}
      open={visible}
      className={styles.accountInterestDrawer}
      onClose={onCloseDrawer}
      onSubmit={handleSubmit}
      submitBtnShow={currentStep === Step.AccountInfo}
      submitBtnProps={{ loading: submitButtonLoading }}
      cancelText={t('Cancel')}
      sendText={t('Next')}
    >
      <Steps current={currentStep} items={stepItems} className="w-[60%]" />
      <div
        className={styles.accountInterestDrawerForm}
        style={{
          display: currentStep === Step.AccountInfo ? 'block' : 'none',
        }}
      >
        <CommonForm formProps={{ form }} fields={fields} />
      </div>
      <div
        style={{
          display: currentStep === Step.InterestDetails ? 'block' : 'none',
        }}
      >
        <InterestConfig
          interestConfigId={interestConfigId}
          readonly={readonly}
          productCategoryIdList={ProductCategoryEnumList.COMMON_INTEREST}
        />
      </div>
    </Drawer>
  );
};
