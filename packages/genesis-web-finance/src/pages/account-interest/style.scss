@import '@/variables.scss';

.account-interest-container {
  background: var(--white);
  .tabs {
      :global {
        .#{$antd-prefix}-tabs-nav {
          margin: 0;
        }
      }
    }
  :global {
    .#{$antd-prefix}-divider-horizontal {
      margin: $gap-md 0;
    }
    .#{$antd-prefix}-table-thead > tr > th .#{$antd-prefix}-table-filter-icon {
      position: relative;
    }
    .#{$antd-prefix}-btn {
      border-radius: var(--border-radius-base);
      width: fit-content;
    }
                                                                                                                                                                                                                                                                }
    .interest-content-wrapper {
      :global {
        .#{$antd-prefix}-menu {
          margin-top: $gap-lg;
          .#{$antd-prefix}-menu-item {
            padding-right: $gap-big;
            margin-top: 0;
            margin-bottom: 0;
            height: 52px;
            line-height: 52px;
          }
          .#{$antd-prefix}-menu-item-selected {
            background: none !important;
          }
          .#{$antd-prefix}-menu-title-content {
            height: 100%;
            border-bottom: 1px solid var(--border-default);
          }
        }
      }
      .menu-item {
        display: flex;
        align-items: center;
        height: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        .title {
          flex: 1;
        }
        > i {
          margin-right: 0;
          text-align: right;
          font-size: $font-size-big;
          font-weight: 700;
        }
      }
      .add-btn {
        width: 320px;
        margin: $gap-lg;
      }
                }
  }
.account-interest-drawer {
  .process-icon {
    box-shadow: 0 0 0var (--gap-xss) var(--primary-color-20-percent);
    border-radius: 50%;
  }
  .button-container {
    display: flex;
    align-items: center;
    background-color: var(--white);
    width: 1000px;
    height: 70px;
    position: fixed;
    bottom: 0;
    border-top: 1px solid var(--border-default);
    padding: $gap-md 50px $gap-md 0;
    text-align: right;
    :global {
      .#{$antd-prefix}-btn {
        margin-left: $gap-md;
      }
    }
    > span {
      cursor: pointer;
      > i {
        margin-right: 10px;
      }
    }
    .left-button {
      flex: 1;
      text-align: right;
    }
  }
  .policy-account-table {
    margin: 0 !important;
    padding: 0 !important;
  }
}
.account-interest-drawer-form {
  height: 100%;
  padding-bottom: 50px;
  margin-top: $gap-big;
  :global {
    .#{$antd-prefix}-form-item-control {
      width: 240px;
    }
  }
  .section-header {
    font-weight: 700;
    padding-bottom: $gap-md;
  }
  .tooltip-icon {
    padding-left: 5px;
    line-height: 20px;
  }
}

.modal-info-icon {
  color: $warning-color;
}
