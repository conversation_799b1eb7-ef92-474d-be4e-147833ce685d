/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2021-10-26 17:46:55
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2021-11-10 16:27:00
 * @Description: PolicyAccountDrawer
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, message } from 'antd';

import { CommonForm, Drawer, Steps } from '@zhongan/nagrand-ui';

import type { PolicyAccountItemProps } from 'genesis-web-service';
import { AccountInterestService } from 'genesis-web-service';

import { Config as InterestConfig } from '../InterestRateContent/Config';
import { usePolicyAccountDrawerFields } from '../hooks/useFormFields';
import styles from '../style.scss';

enum Step {
  AccountInfo = 0,
  InterestDetails = 1,
}

interface PolicyAccountDrawerProps {
  policyAccountDetail: PolicyAccountItemProps;
  visible: boolean;
  closeDrawer: () => void;
  onSubmitCallback: () => void;
  readonly: boolean;
}

export const PolicyAccountDrawer: React.FC<PolicyAccountDrawerProps> = ({
  policyAccountDetail,
  visible,
  closeDrawer,
  onSubmitCallback,
  readonly,
}) => {
  // 控制是添加模式还是编辑模式
  const isAdd = !policyAccountDetail;
  const { t } = useTranslation('finance');
  const [form] = Form.useForm();
  const [submitButtonLoading, setSubmitButtonLoading] =
    useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState(Step.AccountInfo);
  const [interestConfigId, setInterestConfigId] = useState<number>();
  const [informationSaved, setInformationSaved] = useState(false);

  const fields = usePolicyAccountDrawerFields({
    form,
    disabled: readonly,
    policyAccountDetail,
  });

  useEffect(() => {
    if (policyAccountDetail) {
      form.setFieldsValue(policyAccountDetail);
    }
  }, [policyAccountDetail]);

  const handleSubmit = useCallback(() => {
    if (currentStep === Step.AccountInfo) {
      if (readonly) {
        setInterestConfigId(policyAccountDetail?.interestConfigId);
        setCurrentStep(Step.InterestDetails);
        return;
      }
      form.validateFields().then(values => {
        setSubmitButtonLoading(true);
        let request: Promise<number>;
        if (isAdd) {
          request = AccountInterestService.addAccountConfig(values);
        } else {
          request = AccountInterestService.editAccountConfig(values);
        }
        request
          .then(id => {
            message.success(t('Save successfully'));
            setInformationSaved(true);
            setCurrentStep(Step.InterestDetails);
            setInterestConfigId(id);
          })
          .catch((error: Error) => message.error(error?.message))
          .finally(() => setSubmitButtonLoading(false));
      });
    }
  }, [form, isAdd, currentStep, readonly, t]);

  const onCloseDrawer = useCallback(() => {
    if (informationSaved) {
      onSubmitCallback();
    }
    setInformationSaved(false);
    setCurrentStep(Step.AccountInfo);
    form.resetFields();
    closeDrawer();
  }, [informationSaved, onSubmitCallback, closeDrawer]);

  const stepItems = useMemo(
    () => [
      {
        title: t('Account Information'),
      },
      {
        title: t('Interest Details'),
      },
    ],
    [currentStep, t]
  );

  return (
    <Drawer
      title={isAdd ? t('Add New Account') : t('Policy Account Configuration')}
      closable={false}
      open={visible}
      className={styles.accountInterestDrawer}
      onClose={onCloseDrawer}
      onSubmit={handleSubmit}
      submitBtnShow={currentStep === Step.AccountInfo}
      submitBtnProps={{ loading: submitButtonLoading }}
      cancelText={t('Cancel')}
      sendText={t('Next')}
    >
      <Steps current={currentStep} items={stepItems} className="w-[60%]" />
      <div
        className={styles.accountInterestDrawerForm}
        style={{
          display: currentStep === Step.AccountInfo ? 'block' : 'none',
        }}
      >
        <CommonForm fields={fields} formProps={{ form }} use="drawer" />
      </div>
      <div
        style={{
          display: currentStep === Step.InterestDetails ? 'block' : 'none',
        }}
      >
        <InterestConfig
          interestConfigId={interestConfigId}
          readonly={readonly}
        />
      </div>
    </Drawer>
  );
};
