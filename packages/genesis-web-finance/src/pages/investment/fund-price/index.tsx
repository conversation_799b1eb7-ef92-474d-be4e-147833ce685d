import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Popconfirm, Upload, message } from 'antd';
import type { UploadProps } from 'antd';

import { useDispatch, useLocation } from '@umijs/max';

import moment from 'moment';

import {
  AddNewButton,
  CommonIconAction,
  EditableTable,
  ExcelType,
  OperationContainer,
  QueryForm,
  QueryResultContainer,
} from '@zhongan/nagrand-ui';

import type {
  FundPriceCheck,
  FundPriceQuery,
  FundPriceQuerySearchParam,
} from 'genesis-web-service';
import { FundPriceStatus, InvestmentService } from 'genesis-web-service';
import { security } from 'genesis-web-shared';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import ApproveSvg from '@/assets/svg/Approve.svg';
import RejectSvg from '@/assets/svg/Reject.svg';
import { FundPriceRouteType } from '@/types/investment';
import { DefaultTablePaginationV2 } from '@/utils/constants';

import { AddNewFundDrawer } from './AddNewFundDrawer';
import { ToleranceWarningModal } from './ToleranceWarningModalSingle';
import { useColumns } from './hooks/useColumns';
import { useFormFields } from './hooks/useFormFields';
import styles from './index.scss';

const FundPriceRouteMap: Record<string, FundPriceRouteType> = {
  'fund-price-query': FundPriceRouteType.Query,
  'fund-price-adjust': FundPriceRouteType.EnterAndAdjust,
  'fund-price-approve': FundPriceRouteType.Approve,
};
const FundPrice = () => {
  const { t } = useTranslation('finance');
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const [loading, setLoading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [searchParams, setSearchParams] = useState<FundPriceQuerySearchParam>({
    ...DefaultTablePaginationV2,
  } as FundPriceQuerySearchParam);
  const [fundPriceList, setFundPriceList] = useState<FundPriceQuery[]>([]);
  const [total, setTotal] = useState(0);
  const [editingFund, setEditingFund] = useState<Partial<FundPriceQuery>>();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [approveRecord, setApproveRecord] = useState<FundPriceQuery>();
  const [fundPriceCheck, setFundPriceCheck] = useState<FundPriceCheck>();

  const routeType = useMemo(() => {
    const fundPathname = pathname?.split('/investment/')?.[1];
    return FundPriceRouteMap[fundPathname];
  }, [pathname]);

  const fields = useFormFields(routeType);
  const columns = useColumns(editingFund);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'fundStatus',
        'fundPriceStatus',
        'fundRiskLevel',
        'fundOfferPriceCalculateMethod',
      ],
    });
  }, []);

  useEffect(() => {
    // 初始化没有搜索条件时不进行查询
    if (
      Object.entries(searchParams).some(
        ([key, value]) => key !== 'limit' && key !== 'pageIndex' && !!value
      )
    ) {
      setLoading(true);
      const { fundCode, priceEntryDate, priceEffectiveDate, ...resetParams } =
        searchParams;
      const param = {
        ...resetParams,
        fundCode: fundCode !== '' ? fundCode : undefined,
        hasPriceRounding: routeType === FundPriceRouteType.EnterAndAdjust, // 需要返回精度的话这个值传true
        hasBlankRecord: routeType === FundPriceRouteType.EnterAndAdjust, // fund price adjust页面，分页接口都加上 hasBlankRecord
        priceEntryDate: priceEntryDate
          ? dateFormatInstance.formatTz(moment(priceEntryDate).startOf('day'))
          : undefined,
        priceEffectiveDate: priceEffectiveDate
          ? dateFormatInstance.formatTz(
              moment(priceEffectiveDate).startOf('day')
            )
          : undefined,
      };
      InvestmentService.queryFundPrices(param)
        .then(res => {
          setFundPriceList(res?.results);
          setTotal(res?.total);
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setLoading(false));
    }
  }, [searchParams, routeType]);

  const title = useMemo(() => {
    if (routeType === FundPriceRouteType.Query) {
      return t('Fund Price Query');
    }
    if (routeType === FundPriceRouteType.EnterAndAdjust) {
      return t('Enter & Adjust Fund Price');
    }
    return t('Approve Fund Price');
  }, [routeType]);

  const uploadProps: UploadProps = useMemo(
    () => ({
      accept: ExcelType.join(', '),
      name: 'file',
      multiple: false,
      showUploadList: false,
      action: '/api/investment/v2/fund-prices/upload',
      headers: {
        ...security.csrf(),
      },
      onChange: info => {
        if (info.file.status === 'uploading') {
          setUploading(true);
        } else {
          setUploading(false);
        }
        if (info.file.status === 'done') {
          setSearchParams(old => ({
            ...old,
            pageIndex: 1,
          }));
          message.success(t('Upload Success!'));
        } else if (info.file.status === 'error') {
          message.error(
            info.file?.response?.message ||
              `${info.file.name} ${t('Upload failed.')}`
          );
        }
      },
      beforeUpload: file => {
        const fileType = file.name.split('.').pop();
        // 文件类型非xlsx
        if (!ExcelType.includes(`.${fileType}`)) {
          message.error(
            t(
              'System only supports XLS and XLSX file format, please select file again.'
            )
          );
          return false;
        }
        // 大于10M不上传；
        if (file.size > 10 * 1024 * 1024) {
          message.error(t('The document size should not exceed 10MB.'));
          return false;
        }
      },
    }),
    []
  );

  const approveOrReject = useCallback(
    (record: FundPriceQuery, flag: boolean) =>
      new Promise((resolve, reject) => {
        InvestmentService.approvePrices(record.fundPriceId, {
          fundPriceApproveDetail: flag
            ? FundPriceStatus.ApprovePass
            : FundPriceStatus.ApproveReject,
        })
          .then(fundPriceId => {
            if (fundPriceId) {
              setSearchParams(old => ({ ...old }));
              setApproveRecord(undefined);
              setFundPriceCheck(undefined);
              resolve(true);
            } else {
              reject();
            }
          })
          .catch(err => {
            message.error(err?.message);
          });
      }),
    []
  );

  const approveCheck = (record: FundPriceQuery) => {
    setApproveRecord(record);
    InvestmentService.approveCheck({
      fundPriceId: record.fundPriceId,
      fundPriceApproveDetail: FundPriceStatus.ApprovePass,
    })
      .then(data => {
        setFundPriceCheck(data);
      })
      .catch(error => {
        message.error(error.message);
      });
  };
  // 获取approve页面action
  const getApprovePageActions = useCallback(
    (record: FundPriceQuery) => {
      return routeType === FundPriceRouteType.Approve
        ? [
            {
              customerDom: (
                <Popconfirm
                  title={t('Are you sure you want to approve this price?')}
                  placement="topRight"
                  onConfirm={() => approveCheck(record)}
                  onCancel={() => setApproveRecord(undefined)}
                  okText={t('Yes')}
                  cancelText={t('No')}
                >
                  <CommonIconAction
                    icon={<ApproveSvg width="1em" height="1em" />}
                    disabled={record.status !== FundPriceStatus.Input}
                  />
                </Popconfirm>
              ),
            },
            {
              customerDom: (
                <Popconfirm
                  title={t('Are you sure you want to reject this price?')}
                  disabled={record.status !== FundPriceStatus.Input}
                  placement="topRight"
                  okText={t('Yes')}
                  cancelText={t('No')}
                  onConfirm={() => approveOrReject(record, false)}
                >
                  <CommonIconAction
                    icon={<RejectSvg width="1em" height="1em" />}
                    disabled={record.status !== FundPriceStatus.Input}
                  />
                </Popconfirm>
              ),
            },
          ]
        : null;
    },
    [routeType, approveRecord, approveOrReject]
  );

  const onDownload = useCallback(() => {
    setDownloading(true);
    InvestmentService.downloadFundPriceTemplate()
      .then(downloadFile)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setDownloading(false));
  }, []);

  const onSearch = useCallback(
    params => {
      if (Object.values(params ?? []).some(param => !!param)) {
        setSearchParams({
          ...params,
          limit: searchParams.limit,
          pageIndex: 1,
        });
      } else {
        message.error(t('Please enter at least one search conditional fields'));
      }
    },
    [searchParams.limit, t]
  );

  const handleSave = useCallback(
    (record: FundPriceQuery): Promise<boolean> =>
      new Promise((resolve, reject) =>
        InvestmentService.checkFundPriceBeforeSave(record)
          .then(() => {
            setSearchParams(old => ({
              ...old,
            }));
            resolve(true);
          })
          .catch((err: Error) => {
            message.error(err?.message);
            reject();
          })
      ),
    []
  );

  const onCloseDrawer = useCallback(updated => {
    if (updated) {
      setSearchParams(old => ({
        ...old,
        pageIndex: 1,
      }));
    }
    setDrawerVisible(false);
  }, []);

  return (
    <div className={styles.fundPriceQuery}>
      <QueryForm
        queryFields={fields}
        title={title}
        disabled={!!editingFund}
        onSearch={onSearch}
        loading={loading}
      />
      <QueryResultContainer>
        {routeType === FundPriceRouteType.EnterAndAdjust && (
          <OperationContainer>
            <OperationContainer.Left>
              <AddNewButton
                type="primary"
                ghost
                onClick={() => setDrawerVisible(true)}
              >
                {t('Add New')}
              </AddNewButton>
            </OperationContainer.Left>
            <OperationContainer.Right>
              <Button onClick={onDownload} loading={downloading}>
                {t('Download Template')}
              </Button>
              <Upload {...uploadProps}>
                <Button loading={uploading}>{t('Upload')}</Button>
              </Upload>
            </OperationContainer.Right>
          </OperationContainer>
        )}
        <EditableTable
          rowKey="fundPriceId"
          scroll={{ x: 'max-content' }}
          dataSource={fundPriceList}
          columns={columns}
          loading={loading}
          pagination={{
            current: searchParams.pageIndex,
            pageSize: searchParams.limit,
            total,
            onChange: (current: number, pageSize: number) => {
              setSearchParams(old => ({
                ...old,
                pageIndex: current,
                limit: pageSize,
              }));
            },
          }}
          // query页面没有按钮，adjust页面支持edit，approve页面支持approve/reject
          readonly={routeType === FundPriceRouteType.Query}
          customizedRowActionList={getApprovePageActions}
          editBtnProps={{
            visible: routeType === FundPriceRouteType.EnterAndAdjust,
            //  approve_pass状态的不能编辑
            disabled: record => record?.status === FundPriceStatus.ApprovePass,
            handleEdit: setEditingFund,
          }}
          deleteBtnProps={{
            visible: false,
          }}
          addBtnProps={{
            visible: false,
          }}
          setDataSource={setFundPriceList}
          handleConfirm={handleSave}
          handleCancel={() => setEditingFund(undefined)}
          emptyType="icon"
        />
      </QueryResultContainer>
      <AddNewFundDrawer visible={drawerVisible} onClose={onCloseDrawer} />
      <ToleranceWarningModal
        toleranceLevelExceedLimit={fundPriceCheck?.toleranceLevelExceedLimit}
        toleranceLevelEqualZero={fundPriceCheck?.toleranceLevelEqualZero}
        open={
          !!fundPriceCheck?.toleranceLevelEqualZero ||
          !!fundPriceCheck?.toleranceLevelExceedLimit
        }
        onCancel={() => setFundPriceCheck(undefined)}
        onSubmit={() => approveOrReject(approveRecord, true)}
      />
    </div>
  );
};

export default FundPrice;
