import { Fragment, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { StepProps } from 'antd';
import { Skeleton, Steps, message } from 'antd';

import { useRequest } from 'ahooks';
import { groupBy } from 'lodash-es';

import { TextBody } from '@zhongan/nagrand-ui';

import { NoData } from 'genesis-web-component/lib/components/NoData';
import type { FileDetailItem } from 'genesis-web-service';
import {
  BcpService,
  InvoiceFileType,
  InvoiceStatus,
} from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { CommonUploadFileItem } from '@/components/CommonUploadFileItem';
import {
  InvoiceFileTypeMap,
  NumberFieldMap,
  NumberTitleMap,
} from '@/utils/constants';

import styles from './index.scss';

interface InvoiceDetailDrawerProps {
  id: number;
}

/**
 *
 * @param id id
 * @param visible 是否可见
 * @param onClose 关闭回调
 * @description invoice详情抽屉；一定会有invoice no，但是不一定有invoice文件
 */
export const Content = ({ id }: InvoiceDetailDrawerProps) => {
  const { t } = useTranslation('finance');
  const [invoiceNo, setInvoiceNo] = useState<string>();
  const [invoiceFileList, setInvoiceFileList] = useState<FileDetailItem[]>();
  const [otherInvoiceDetail, setOtherInvoiceDetail] = useState<
    Record<string, FileDetailItem[]>
  >({});
  const [invoiceSendData, setInvoiceSendData] = useState(
    {} as Partial<FileDetailItem>
  );

  const { loading } = useRequest(
    () => {
      if (id) {
        return BcpService.getInvoiceManagementDetail(id);
      }
    },
    {
      refreshDeps: [id],
      onSuccess: res => {
        if (res) {
          const { fileDetailList, sendDate, operatorForSend } = res;
          const otherFileDetailList = fileDetailList.filter(
            item => item.invoiceDetailType != InvoiceFileType.Invoice
          );
          const invoiceDetailMap =
            groupBy(fileDetailList, 'invoiceDetailType') ?? {};
          setInvoiceNo(res?.invoiceNo);
          setInvoiceFileList(invoiceDetailMap[InvoiceFileType.Invoice]);
          delete invoiceDetailMap[InvoiceFileType.Invoice];
          const isReversed = res?.status === InvoiceStatus.Reversed;
          let otherInvoiceDetailList = {};
          if (otherFileDetailList.length > 0) {
            if (isReversed) {
              otherInvoiceDetailList =
                // 最后一个是Reverse区块的，其他是Adjustment的
                otherFileDetailList?.length > 1
                  ? {
                      [InvoiceFileType.Adjustment]: otherFileDetailList.slice(
                        0,
                        -1
                      ),
                      [InvoiceFileType.InvoiceCreditNote]: [
                        otherFileDetailList[otherFileDetailList.length - 1],
                      ],
                    }
                  : {
                      [InvoiceFileType.InvoiceCreditNote]: otherFileDetailList,
                    };
            } else {
              // 全都是Adjustment
              otherInvoiceDetailList = {
                [InvoiceFileType.Adjustment]: otherFileDetailList,
              };
            }
          }
          setOtherInvoiceDetail(otherInvoiceDetailList);
          if (sendDate || operatorForSend) {
            setInvoiceSendData({
              invoiceGenerationDate: sendDate,
              operatorForInvoiceGeneration: operatorForSend,
            });
          }
        }
      },
    }
  );

  const onDownload = useCallback((code: string) => {
    BcpService.downloadInvoice(code)
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message));
  }, []);

  const renderInvoiceHistoryItem = useCallback(
    ({
      type,
      fileList,
    }: {
      type: InvoiceFileType;
      fileList?: Partial<FileDetailItem>[];
    }) => {
      return (
        <div className={styles.detailSection} key={type}>
          <TextBody className="text-lg !text-labelColor" weight={700}>
            {InvoiceFileTypeMap[type] ?? type}
          </TextBody>
          {fileList?.length ? (
            fileList?.map(file => (
              <div key={file.fileUnicode} style={{ marginBottom: 16 }}>
                <div className={styles.generationBox}>
                  <p>
                    {t('By', {
                      name: file?.operatorForInvoiceGeneration ?? ' --',
                    })}
                  </p>
                  <p>
                    {dateFormatInstance.getDateString(
                      file.invoiceGenerationDate
                    )}
                  </p>
                </div>
                <p className={styles.numberTitle}>
                  {NumberTitleMap[type as InvoiceFileType]}
                  {type === InvoiceFileType.Invoice
                    ? invoiceNo
                    : file[
                        NumberFieldMap[
                          type as InvoiceFileType
                        ] as keyof FileDetailItem
                      ]}
                </p>
                {file?.fileUnicode && (
                  <CommonUploadFileItem
                    file={file as FileDetailItem}
                    downloadApi={BcpService.downloadInvoice}
                    readOnly={true}
                    needPreview={true}
                  />
                )}

                {file?.reverseReason && (
                  <Fragment>
                    <div className={styles.reasonTitle}>
                      {t('Reverse Reason')}
                    </div>
                    <div className={styles.reasonContent}>
                      {file?.reverseReason}
                    </div>
                  </Fragment>
                )}
              </div>
            ))
          ) : (
            <div className={styles.fileDisabled}>
              <NoData
                disabled
                inTable={false}
                emptyText={t('The invoice file has not been generated.')}
              />
            </div>
          )}
        </div>
      );
    },
    [onDownload, t, invoiceNo]
  );

  const invoiceSteps: StepProps[] = useMemo(() => {
    const steps = [
      {
        description: renderInvoiceHistoryItem({
          type: InvoiceFileType.Invoice,
          fileList: invoiceFileList,
        }),
      },
      ...Object.entries(otherInvoiceDetail)?.map(([type, fileList]) => ({
        description: renderInvoiceHistoryItem({ type, fileList }),
      })),
    ];
    if (invoiceSendData?.invoiceGenerationDate) {
      // 如果有send数据 就显示send部分
      steps.splice(1, 0, {
        description: renderInvoiceHistoryItem({
          type: InvoiceFileType.Send,
          fileList: [invoiceSendData],
        }),
      });
    }
    return steps;
  }, [
    invoiceFileList,
    otherInvoiceDetail,
    invoiceSendData,
    renderInvoiceHistoryItem,
  ]);

  return (
    <Skeleton loading={loading}>
      <Steps
        progressDot
        current={invoiceFileList?.length ? invoiceSteps?.length - 1 : null}
        direction="vertical"
        items={invoiceSteps}
        className={styles.step}
      />
    </Skeleton>
  );
};
