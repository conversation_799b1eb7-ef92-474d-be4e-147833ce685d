import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Steps } from 'antd';

import { Icon, TextBody } from '@zhongan/nagrand-ui';

import { PaymentRequestOperationInfo } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { renderEnumsWithString } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';

export const PaymentRequestHistory = ({
  operationList,
}: {
  operationList?: PaymentRequestOperationInfo[];
}) => {
  const { t } = useTranslation('finance');
  const enums = useTenantBizDict() ?? {};

  const operationSteps = useMemo(() => {
    const steps = [
      ...(operationList ?? [])?.map(
        (
          { paymentRequestOperation, operator, operationDate, comments },
          index
        ) => ({
          description: (
            <div className="bg-formAddonBg p-4 rounded-base">
              <div className="flex flex-row justify-between items-start">
                <div>
                  <TextBody type="body" weight={700} className="!text-text">
                    {renderEnumsWithString(
                      paymentRequestOperation,
                      enums?.bcpPaymentRequestOperation
                    )}
                  </TextBody>
                  <TextBody className="!text-textTertiary">
                    {dateFormatInstance.getDateTimeString(operationDate)}
                  </TextBody>
                </div>
                <TextBody className="!text-textQuaternary">
                  {t('By', { name: operator })}
                </TextBody>
              </div>
              <TextBody className="!text-defaultTextDark">{comments}</TextBody>
            </div>
          ),
          icon:
            index === 0 ? (
              <Icon type="step-current-dot" />
            ) : (
              <div
                style={{
                  backgroundColor: 'var(--primary-light)',
                  height: 10,
                  width: 10,
                  marginTop: 10,
                }}
                className="rounded-full m-auto"
              />
            ),
        })
      ),
    ];
    return steps;
  }, [operationList, t, enums?.bcpPaymentRequestOperation]);

  return (
    <>
      <TextBody type="body" weight={700} className="mb-4">
        {t('Payment Request History')}
      </TextBody>
      <Steps current={0} direction="vertical" items={operationSteps} />
    </>
  );
};
