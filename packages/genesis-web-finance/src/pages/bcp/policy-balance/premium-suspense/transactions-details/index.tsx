import { useCallback, useEffect, useMemo, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { Button, Spin, message } from 'antd';
import type { SorterResult } from 'antd/lib/table/interface';

import { useDispatch, useLocation, useNavigate } from '@umijs/max';

import { useRequest } from 'ahooks';
import { v4 as uuid } from 'uuid';

import {
  Alert,
  ColumnsType,
  Icon,
  Table,
  Tabs,
  lessVars,
} from '@zhongan/nagrand-ui';

import type {
  BalanceDataDetailType,
  BalanceTransactionDetail,
  BalanceTransactionDetailRespType,
  PolicyBalanceAccountBill,
  SuspenseAmountDetail,
} from 'genesis-web-service';
import { BcpService, ManualVerificationStatus } from 'genesis-web-service';

import { useCurrencyAmountRender } from '@/hooks/useCurrencyAmountRender';
import { useNavVersion } from '@/hooks/useNavVersion';
import { usePermission } from '@/hooks/usePermissions';
import type { FormFields, PolicyBalanceAccountRouteState } from '@/types/bcp';
import { BalanceAccountTypeEnum, PolicyBalanceFromType } from '@/types/bcp';
import { DefaultTablePagination } from '@/utils/constants';
import { reduceToSum } from '@/utils/util';

import { RefundDrawer } from '../components/RefundDrawer';
import { SuspenseAmountDrawer } from '../components/SuspenseAmountDrawer';
import { TransactionHeadDetail } from '../components/TransactionHeadDetail';
import { BalanceAccountTypeDesc, DefaultBalanceAccountType } from '../constant';
import { useBalanceRefundApprovalTasks } from '../hooks/useBalanceRefundApprovalTasks';
import {
  useBalanceDetailColumns,
  useBalanceDetailExpandedColumns,
} from '../hooks/useColumns';
import { TransferSuspenseDrawer as TransferDrawer } from '../transfer-suspense-drawer';
import styles from './index.scss';

const TransactionsDetails = () => {
  const { t } = useTranslation('finance');
  const canEdit = usePermission('bcp.policy-balance.edit');
  const { state } = useLocation();
  const { balanceTransactionInfo, from } = (state ??
    {}) as PolicyBalanceAccountRouteState<FormFields>;
  const expandedColumns =
    useBalanceDetailExpandedColumns() as ColumnsType<PolicyBalanceAccountBill>[];
  const { showNewSideNav } = useNavVersion();
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [newBalanceInfo, setNewBalanceInfo] = useState(balanceTransactionInfo);
  const [orderByTransactionDateAsc, setOrderByTransactionDateAsc] =
    useState<boolean>();
  const [orderByGenerateDateAsc, setOrderByGenerateDateAsc] =
    useState<boolean>();
  const [orderByDueDateAsc, setOrderByDueDateAsc] = useState<boolean>();

  const [refundDrawerVisible, setRefundDrawerVisible] = useState(false);
  const [transferDrawerVisible, setTransferDrawerVisible] = useState(false);
  const [balanceAccountType, setBalanceAccountType] = useState(
    DefaultBalanceAccountType
  );

  const [suspenseAmountList, setSuspenseAmountList] = useState(
    [] as SuspenseAmountDetail[]
  );

  const [suspenseAmountDrawerVisible, setSuspenseAmountDrawerVisible] =
    useState(false);
  const [transactionDetails, setTransactionDetails] = useState(
    {} as BalanceTransactionDetailRespType
  );
  const [transTypes, setTransTypes] = useState<string>();
  const [queryApprovalKey, setQueryApprovalKey] = useState<string>();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const formatAmount = useCurrencyAmountRender();
  const approvalTasks = useBalanceRefundApprovalTasks(
    newBalanceInfo?.balanceId,
    ManualVerificationStatus.WaitingForApproval,
    queryApprovalKey
  );
  const amountInApproval = useMemo(
    () => reduceToSum(approvalTasks, 'amount'),
    [approvalTasks]
  );

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'bcpSuspenseDetailPaymentType',
        'transType',
        'bcpSuspenseIndicatorType',
        'transferMethod',
        'balanceType',
        'payMethod',
        'bcpStatementBusinessNoType',
        'bcpSuspenseDetailSourceType',
        'bcpBillStatus',
        'certiType',
        'paymentPaymentMethod',
        'organizationIdType',
        'bcpPolicyBalanceTransferExternalStatementBusinessNoType',
        'manualOperationVerificationStatus',
      ],
    });
  }, [dispatch]);

  const columns =
    useBalanceDetailColumns(
      balanceAccountType,
      setSuspenseAmountList,
      setSuspenseAmountDrawerVisible
    ) ?? ([] as ColumnsType<BalanceDataDetailType>[]);

  const { loading, refresh: refreshTableData } = useRequest(
    () => {
      const baseParams = {
        balanceType: balanceTransactionInfo?.balanceType,
        orderByTransactionDateAsc,
      };

      if (balanceAccountType === BalanceAccountTypeEnum.Comprehensive) {
        return BcpService.getComprehensiveBalanceTransactionDetails({
          ...baseParams,
          policyNo: balanceTransactionInfo?.policyNo,
          proposalNo: balanceTransactionInfo?.proposalNo,
          transTypes,
          orderByGenerateDateAsc,
          orderByDueDateAsc,
        });
      }

      return BcpService.getBalanceTransactionDetails({
        ...baseParams,
        balanceId: newBalanceInfo?.balanceId,
        start: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
      });
    },
    {
      refreshDeps: [
        pagination,
        balanceTransactionInfo,
        orderByGenerateDateAsc,
        orderByTransactionDateAsc,
        orderByDueDateAsc,
        balanceAccountType,
        transTypes,
      ],
      onSuccess: (res: BalanceTransactionDetailRespType) => {
        // comprehensive 返回一个对象，包含头部和列表信息；transaction 返回列表信息
        if (balanceAccountType === BalanceAccountTypeEnum.Comprehensive) {
          // 接口中可能不返回overpaymentAmount字段，需要额外处理一下。 overpaymentAmount在transfer提交到接口时使用
          const { overpaymentAmount = '0' } = res;
          setNewBalanceInfo(pre => ({
            ...pre,
            ...res,
            overpaymentAmount,
            // 接口未返回，需要在transfer drawer展示，手动加上
            balanceType: balanceTransactionInfo?.balanceType,
          }));
        }
        setTransactionDetails(res);
      },
      onError: (err: Error) => {
        message.error(err?.message);
      },
    }
  );

  const { loading: spinning, run: getPolicyBalance } = useRequest(
    () =>
      BcpService.getPolicyBalance({
        policyNo: balanceTransactionInfo?.policyNo,
        proposalNo: balanceTransactionInfo?.proposalNo,
        balanceTypeList: balanceTransactionInfo?.balanceType,
      }),
    {
      manual: true,
      onSuccess: res => {
        const { policyBalanceAccountList, ...rest } = res;
        if (balanceAccountType === BalanceAccountTypeEnum.Transaction) {
          //  overpaymentAmount在transfer提交到接口时使用。 By Transaction tab下从getPolicyBalance接口中获取zhi
          (
            rest as typeof rest & { overpaymentAmount: string }
          ).overpaymentAmount = policyBalanceAccountList?.[0]?.availableAmount;
        }
        setNewBalanceInfo(pre => ({
          ...pre,
          ...rest,
          ...(policyBalanceAccountList?.[0] ?? {}),
        }));
      },
    }
  );

  const goBack = useCallback(() => {
    const url =
      from === PolicyBalanceFromType.BankStatement
        ? '/bcp/bank-statement'
        : '/bcp/policy-balance';
    navigate(url, {
      state,
    });
  }, [navigate, state]);

  const onCloseDrawer = useCallback(
    (updated?: boolean) => {
      if (updated) {
        setPagination({ ...DefaultTablePagination }); // refund/transfer记录会更新在表格里，故需要重新查询表格数据
        getPolicyBalance(); // refund/transfer后需要重新查询余额
        refreshTableData(); // 重新查询列表数据
      }
      setRefundDrawerVisible(false);
      setTransferDrawerVisible(false);
      setSuspenseAmountDrawerVisible(false);
      setQueryApprovalKey(uuid()?.toString());
    },
    [getPolicyBalance, refreshTableData]
  );

  const onExpand = useCallback(
    (key: number) => {
      const clonedKeys = [...expandedRowKeys];
      clonedKeys.push(key);
      setExpandedRowKeys(clonedKeys);
    },
    [expandedRowKeys]
  );

  const onCollapse = useCallback(
    (key: number) => {
      const clonedKeys = [...expandedRowKeys];
      const matchedIndex = clonedKeys.findIndex(rowKey => rowKey === key);
      clonedKeys.splice(matchedIndex, 1);
      setExpandedRowKeys(clonedKeys);
    },
    [expandedRowKeys]
  );

  const combinedColumns: ColumnsType<BalanceDataDetailType>[] = useMemo(
    () =>
      balanceAccountType === BalanceAccountTypeEnum.Comprehensive
        ? [...columns]
        : [
            ...columns,
            {
              key: 'action',
              title: t('Bill Info'),
              fixed: 'right',
              align: 'right',
              render: (record: BalanceDataDetailType) =>
                expandedRowKeys?.includes(record.suspenseDetailId) ? (
                  <Button
                    type="link"
                    icon={<Icon type="up" />}
                    style={{ color: 'var(--primary-color)' }}
                    onClick={() => onCollapse(record.suspenseDetailId)}
                  />
                ) : (
                  <Button
                    type="link"
                    icon={<Icon type="down" />}
                    onClick={() => onExpand(record.suspenseDetailId)}
                  />
                ),
            },
          ],
    [t, columns, expandedRowKeys, onCollapse, onExpand, balanceAccountType]
  );

  const expandedRowRender = useCallback(
    (dataSource: PolicyBalanceAccountBill[]) => (
      <Table
        columns={expandedColumns}
        dataSource={dataSource ?? []}
        pagination={false}
        scroll={{ x: 2200, y: 200 }}
        rowKey="billId"
      />
    ),
    [expandedColumns]
  );

  const onTableChange = useCallback(
    (
      action: string,
      filters,
      sorter: SorterResult<BalanceTransactionDetail>
    ) => {
      if (action === 'sort') {
        const { field, order } = sorter;
        const realOrder = order ? order === 'ascend' : undefined;
        switch (`${field}`) {
          case 'generateDate':
            setOrderByGenerateDateAsc(realOrder);
            break;
          case 'transactionDate':
            setOrderByTransactionDateAsc(realOrder);
            break;
          case 'dueDate':
            setOrderByDueDateAsc(realOrder);
            break;
        }

        return;
      }
      if (action === 'filter') {
        const { transType } = filters;
        setTransTypes(transType ? transType.join(',') : null);
      }
    },
    []
  );

  const onChangeBalanceAccountType = useCallback(
    (targetBalanceType: BalanceAccountTypeEnum) => {
      // 切换页面类型
      if (targetBalanceType === BalanceAccountTypeEnum.Transaction) {
        getPolicyBalance();
      }
      setBalanceAccountType(targetBalanceType);

      // 重置分页数据和表格筛选项 重新请求头部信息
      setTransactionDetails({} as BalanceTransactionDetailRespType);
      setPagination({ ...DefaultTablePagination });
    },
    [getPolicyBalance, setBalanceAccountType, setPagination]
  );

  const tabItems = useMemo(
    () => [
      {
        label: (
          <span>
            {showNewSideNav && <Icon type="manual-underwriting" />}
            {BalanceAccountTypeDesc[BalanceAccountTypeEnum.Comprehensive]}
          </span>
        ),
        key: BalanceAccountTypeEnum.Comprehensive,
      },
      {
        label: (
          <span>
            {showNewSideNav && <Icon type="master-agreement" />}
            {BalanceAccountTypeDesc[BalanceAccountTypeEnum.Transaction]}
          </span>
        ),
        key: BalanceAccountTypeEnum.Transaction,
      },
    ],
    [showNewSideNav, BalanceAccountTypeEnum, BalanceAccountTypeDesc]
  );

  return (
    <div
      className={styles.transactionsDetails}
      id="transactions-detail-container"
    >
      {amountInApproval > 0 && (
        <Alert
          type="warning"
          message={
            <Trans
              i18nKey="There are pending approval tasks under this balance, with Total Refund Amount: <1></1>."
              ns="finance"
            >
              There are pending approval tasks under this balance, with Total
              Refund Amount:
              <span i18nIsDynamicList>
                {['amount'].map(() => (
                  <span style={{ fontWeight: 700 }}>
                    {formatAmount(
                      amountInApproval?.toString(),
                      approvalTasks?.[0]?.currency
                    )}
                  </span>
                ))}
              </span>
              .
            </Trans>
          }
          style={{
            margin: 'var(--gap-md)',
          }}
        />
      )}
      <Tabs
        items={tabItems}
        defaultActiveKey={BalanceAccountTypeEnum.Comprehensive}
        onChange={activeKey =>
          onChangeBalanceAccountType(activeKey as BalanceAccountTypeEnum)
        }
        type="transparent-card"
        className="mx-4 mt-4 mb-0 h-[48px]"
      />
      <div
        className={styles.content}
        style={
          showNewSideNav
            ? {}
            : { margin: `0 ${lessVars['@gap-md']} ${lessVars['@gap-md']}` }
        }
      >
        <Spin spinning={spinning}>
          <TransactionHeadDetail
            type={balanceAccountType}
            dataSource={newBalanceInfo}
          />
        </Spin>
        <Table
          columns={combinedColumns}
          dataSource={
            transactionDetails?.results ||
            transactionDetails?.queryPolicyComprehensiveDetailResponses
          }
          loading={loading}
          scroll={{ x: 'max-content' }}
          rowKey="suspenseDetailId"
          expandable={{
            expandedRowKeys,
            expandedRowRender: (record: BalanceTransactionDetail) =>
              expandedRowRender(record.queryPolicyBalanceAccountBills),
          }}
          expandType="nestedTable"
          onChange={(_, filters, sorter, { action }) =>
            onTableChange(action, filters, sorter)
          }
          pagination={
            balanceAccountType === BalanceAccountTypeEnum.Transaction
              ? {
                  ...pagination,
                  total: transactionDetails?.total,
                  onChange: (current, pageSize) =>
                    setPagination(old => ({
                      ...old,
                      current,
                      pageSize,
                    })),
                }
              : false
          }
        />
      </div>
      <div className={styles.footer}>
        <Button type="link" icon={<Icon type="left" />} onClick={goBack}>
          {from === PolicyBalanceFromType.BankStatement
            ? t('Return to bank statement')
            : t('Back to Search')}
        </Button>

        <div className={styles.rightBtn}>
          {canEdit && (
            <Button
              size="large"
              type="primary"
              ghost
              style={{ marginRight: 16 }}
              onClick={() => setRefundDrawerVisible(true)}
              disabled={loading || spinning}
            >
              {t('Refund')}
            </Button>
          )}
          {canEdit && (
            <Button
              size="large"
              type="primary"
              ghost
              disabled={loading || spinning}
              onClick={() => setTransferDrawerVisible(true)}
            >
              {t('Transfer')}
            </Button>
          )}
        </div>
      </div>
      {refundDrawerVisible && (
        <RefundDrawer
          visible={refundDrawerVisible}
          onClose={onCloseDrawer}
          balanceInfo={newBalanceInfo}
        />
      )}
      {/* transfer drawer */}
      <TransferDrawer
        visible={transferDrawerVisible}
        onClose={onCloseDrawer}
        sourceData={newBalanceInfo}
      />
      {/* suspense detail drawer */}
      <SuspenseAmountDrawer
        dataSource={suspenseAmountList}
        visible={suspenseAmountDrawerVisible}
        onCloseDrawer={onCloseDrawer}
      />
    </div>
  );
};

export default TransactionsDetails;
