@import '@/variables.scss';

.transactions-details {
  display: flex;
  flex-direction: column;
  height: 100%;

  .content {
    flex: 1;
    padding: 0 $gap-big $gap-md;
    background-color: var(--white);
    overflow: scroll;

    :global {
      .#{$antd-prefix}-table-row-expand-icon-cell {
        padding: 0;
      }
      .#{$antd-prefix}-table-expand-icon-col {
        width: 0;
      }
      .#{$antd-prefix}-table-expanded-row {
        > td {
          background-color: var(--collapse-header-bg);
          .#{$antd-prefix}-table-container {
            border: none;
            table {
              border-radius: 0;
            }
          }
        }
        &:hover {
          > td {
            background-color: var(--collapse-header-bg) !important;
          }
        }
      }
    }
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 72px;
    padding: $gap-lg;
    background-color: var(--white);
    box-shadow: 0px -0.5px 0px 0px var(--border-default);
    :global {
      .#{$antd-prefix}-btn-link {
        padding-left: 0;
      }
  }
      }

      :global {
        .#{$antd-prefix}-tabs-tab {
          height: 48px;
        }
      
        .#{$antd-prefix}-tabs-nav-list {
          width: 100%;
        }
      
        .#{$antd-prefix}-tabs-tab {
          flex-grow: 1;
        }
      
        .#{$antd-prefix}-tabs-content-top {
          display: none;
        }
    }
  }