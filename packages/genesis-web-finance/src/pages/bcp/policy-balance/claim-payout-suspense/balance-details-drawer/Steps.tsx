import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Steps as StepsComponent } from '@zhongan/nagrand-ui';

export enum BalanceDetailsSteps {
  balanceDetails = 0,
  paymentDetails = 1,
}

interface StepsProps {
  currentStep: BalanceDetailsSteps;
  onBack: () => void;
  onProceed: () => void;
  paymentStepDisabled: boolean;
}

/**
 * Component for steps in balance details drawer
 * @param currentStep current step, must be one of BalanceDetailsSteps
 * @param onBack callback when back button is clicked
 * @param onProceed callback when proceed button is clicked
 * @param paymentStepDisabled whether payment details step is disabled
 * @returns component
 */
export const Steps = ({
  currentStep,
  onBack,
  onProceed,
  paymentStepDisabled,
}: StepsProps) => {
  const { t } = useTranslation('finance');

  const stepsItem = useMemo(() => {
    return [
      {
        key: BalanceDetailsSteps.balanceDetails,
        title: t('Balance Details'),
        onClick: onBack,
      },
      {
        key: BalanceDetailsSteps.paymentDetails,
        title: t('Payment Details'),
        onClick: onProceed,
        disabled: paymentStepDisabled,
      },
    ];
  }, [t, onBack, onProceed, paymentStepDisabled]);

  return (
    <StepsComponent
      current={currentStep}
      items={stepsItem}
      className="mb-6 w-[50%]"
    />
  );
};
