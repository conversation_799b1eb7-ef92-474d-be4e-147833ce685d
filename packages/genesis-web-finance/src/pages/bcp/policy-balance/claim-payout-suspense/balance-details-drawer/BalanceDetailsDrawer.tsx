import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { Form, message } from 'antd';

import { useRequest } from 'ahooks';
import { cloneDeep, compact, isNil, map } from 'lodash-es';

import {
  CommonForm,
  CommonIconAction,
  Drawer,
  EditAction,
  FieldDataType,
  FieldType,
  Icon,
  Input,
  Modal,
  Popconfirm,
  Table,
} from '@zhongan/nagrand-ui';

import { LabelWithTooltip } from 'genesis-web-component/lib/components/LabelWithTooltip';
import { PaymentMethodForm } from 'genesis-web-component/lib/components/PaymentMethodFormV4';
import {
  BcpService,
  ClaimPayoutSuspenseRecord,
  ClaimPayoutSuspenseRecordDetail,
  FileQueryData,
  ManualVerificationStatus,
  PaymentOrCollection,
  TransactionType,
} from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { useCurrencyAmountRender } from '@/hooks/useCurrencyAmountRender';
import { PaymentDetailsTable } from '@/pages/bcp/components/PaymentDetailsTable';
import { Mode } from '@/types/common';
import { ManualVerificationType } from '@/utils/constants';
import i18nInstance from '@/utils/i18n';
import { formatQueryParams, reduceToSum } from '@/utils/util';

import { Detail } from '../../../components/Detail';
import { UploadAndAttachments } from '../../components/UploadAndAttachments';
import { ViewApprovalHistoryTips } from '../../components/ViewApprovalHistoryTips';
import styles from '../style.scss';
import { HistoryDrawer } from './History';
import { BalanceDetailsSteps, Steps } from './Steps';

interface BalanceDetailsDrawerProps {
  visible: boolean;
  mode: Mode;
  balanceDetail: ClaimPayoutSuspenseRecord;
  onClose: () => void;
  refreshClaimPayoutSuspenseList: () => void;
}

const messageText = i18nInstance.t(
  'Multiple records can be selected, but only the same payee can choose multiple records',
  { ns: 'finance' }
);

/**
 * @description BalanceDetailsDrawer组件
 * @param visible
 * @param mode
 * @param balanceDetail
 * @param onClose
 * @param refreshClaimPayoutSuspenseList
 */
export const BalanceDetailsDrawer = ({
  visible,
  mode,
  balanceDetail,
  onClose,
  refreshClaimPayoutSuspenseList,
}: BalanceDetailsDrawerProps) => {
  const { t } = useTranslation('finance');

  const formatAmount = useCurrencyAmountRender();
  const { policyNo, claimCaseNo } = balanceDetail ?? {};
  const [form] = Form.useForm();
  const attachmentRef = useRef(null);
  const [submitting, setSubmitting] = useState(false);
  const [dataSource, setDataSource] = useState<
    ClaimPayoutSuspenseRecordDetail[]
  >([]);
  const [generatedReferenceNo, setGeneratedReferenceNo] = useState<string>();
  const [suspenseTransactionDetailId, setSuspenseTransactionDetailId] =
    useState<number>();
  const [selectedRows, setSelectedRows] = useState<
    ClaimPayoutSuspenseRecordDetail[]
  >([]);
  const [currentStep, setCurrentStep] = useState(
    BalanceDetailsSteps.balanceDetails
  );
  // 在payment details阶段编辑的支付方式、上传文件
  const [editPaymentDetails, setEditPaymentDetails] = useState<{
    attachments?: FileQueryData[];
    data?: Record<string, string>;
  }>();

  const readOnly = useMemo(() => mode === Mode.Read, [mode]);
  const referenceNo = Form.useWatch('referenceNo', form);

  const balanceAccountInfo = useMemo(
    () => [
      { label: t('Claim Case No.'), value: claimCaseNo },
      { label: t('Policy No.'), value: policyNo },
      {
        label: t('Total Available Amount'),
        value: formatAmount(
          balanceDetail.availableAmount,
          balanceDetail.currency
        ),
      },
    ],
    [balanceDetail, formatAmount]
  );

  const balanceColumns = useMemo(() => {
    const actionColumn = {
      fixed: 'right',
      align: 'right',
      dataIndex: 'actions',
      title: t('Actions'),
      render: (_: string, record: ClaimPayoutSuspenseRecordDetail) => (
        <CommonIconAction
          icon={<Icon type="history" />}
          tooltipTitle={t('History')}
          disabled={record.availableAmount === record.transactionAmount}
          onClick={() =>
            setSuspenseTransactionDetailId(record.suspenseTransactionDetailId)
          }
        />
      ),
    };

    const columnsBasic = [
      {
        title: t('Transaction Date'),
        dataIndex: 'transactionDate',
        render: (transactionDate: string) =>
          dateFormatInstance.getDateString(transactionDate),
      },
      { title: t('Payee'), dataIndex: 'payeeName' },
      {
        title: t('Transaction Amount'),
        dataIndex: 'transactionAmount',
        align: 'right',
        render: (_, record: ClaimPayoutSuspenseRecordDetail) =>
          formatAmount(record.transactionAmount, record.currency),
      },
      {
        title: t('Available Amount'),
        dataIndex: 'availableAmount',
        align: 'right',
        render: (_, record: ClaimPayoutSuspenseRecordDetail) =>
          formatAmount(record.availableAmount, record.currency),
      },
    ];
    return readOnly ? [...columnsBasic, actionColumn] : columnsBasic;
  }, [readOnly, t, formatAmount, setSuspenseTransactionDetailId]);

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys: map(selectedRows, 'suspenseTransactionDetailId'),
      getCheckboxProps: (record: ClaimPayoutSuspenseRecordDetail) => ({
        disabled: !+record?.availableAmount,
      }),
      onSelect(selection: ClaimPayoutSuspenseRecordDetail, selected: boolean) {
        // 用户可以勾选多条记录，但只有同一收款人可以选择多条记录，否则提示以下报错信息
        if (
          !!selectedRows.length &&
          selectedRows[0].payeeId !== selection.payeeId
        )
          return message.warning(messageText);
        if (selected) {
          setSelectedRows([...selectedRows, selection]);
        } else {
          setSelectedRows(
            selectedRows.filter(
              item =>
                item.suspenseTransactionDetailId !==
                selection.suspenseTransactionDetailId
            )
          );
        }
      },
      onSelectAll(
        selected: boolean,
        selections: ClaimPayoutSuspenseRecordDetail[]
      ) {
        const payeeIds = compact(map(selections, 'payeeId'));
        // 用户可以勾选多条记录，但只有同一收款人可以选择多条记录，否则提示以下报错信息
        if (!!payeeIds.length && payeeIds.find(item => item !== payeeIds[0]))
          return message.warning(messageText);

        if (selected) {
          setSelectedRows(selections);
        } else {
          setSelectedRows([]);
        }
      },
    }),
    [selectedRows]
  );

  // 在balance step时选择数据，选择的数据为空时，payment step不可点击
  const paymentStepDisabled = useMemo(() => {
    return (
      currentStep === BalanceDetailsSteps.balanceDetails && !selectedRows.length
    );
  }, [currentStep, selectedRows?.length]);

  const [cancelText, sendText] = useMemo(() => {
    if (readOnly) {
      return [t('Close')];
    } else {
      if (currentStep === BalanceDetailsSteps.balanceDetails) {
        return [t('Cancel'), t('Proceed')];
      } else {
        return [t('Cancel'), t('Pay')];
      }
    }
  }, [currentStep, readOnly, t]);

  const { data: approvalTasks } = useRequest(
    () => {
      return BcpService.queryBalanceRefundVerificationHistory({
        policyNo,
        claimCaseNo,
      });
    },
    {
      refreshDeps: [policyNo, claimCaseNo],
    }
  );

  const approvalTasksInProgress = useMemo(
    () =>
      approvalTasks?.filter(
        item => item?.status === ManualVerificationStatus.WaitingForApproval
      ),
    [approvalTasks]
  );

  const amountInApproval = useMemo(
    () => reduceToSum(approvalTasksInProgress, 'amount'),
    [approvalTasksInProgress]
  );

  useEffect(() => {
    setDataSource(
      selectedRows.map(item => ({
        ...item,
        paymentAmount: item?.availableAmount,
      }))
    );
  }, [selectedRows]);

  const totalPaymentAmount = useMemo(
    () => reduceToSum(dataSource, 'paymentAmount'),
    [dataSource]
  );

  const onChangePaymentAmount = useCallback(
    (value: number, record: ClaimPayoutSuspenseRecordDetail) => {
      const clonedDataSource = cloneDeep(dataSource);
      const currentIndex = clonedDataSource?.findIndex(
        row =>
          row?.suspenseTransactionDetailId ===
          record?.suspenseTransactionDetailId
      );

      if (currentIndex !== -1) {
        clonedDataSource[currentIndex] = {
          ...clonedDataSource[currentIndex],
          paymentAmount: value,
        };
        setDataSource(clonedDataSource);
      }
    },
    [dataSource]
  );

  const totalAvailableAmount = useMemo(
    () => reduceToSum(selectedRows, 'availableAmount'),
    [selectedRows]
  );

  const paymentAccountInfo = useMemo(() => {
    return [
      { label: t('Payee Name'), value: selectedRows[0]?.payeeName },
      {
        label: t('Total Available Amount'),
        value: formatAmount(totalAvailableAmount, selectedRows[0]?.currency),
      },
    ];
  }, [t, selectedRows, formatAmount, totalAvailableAmount]);

  const onBack = useCallback(() => {
    if (currentStep === BalanceDetailsSteps.balanceDetails) return;
    const { data } = form.getFieldsValue();
    const attachments = attachmentRef.current.getData();
    selectedRows?.forEach(item => {
      // back 重置paymentAmount金额
      form.setFieldValue(
        [item.suspenseTransactionDetailId, 'paymentAmount'],
        item?.availableAmount
      );
    });
    // back 的时候保存当前上传的attachments、填写的支付方式，其他表单因为没有reset不会重置
    setEditPaymentDetails({ attachments, data });
    setCurrentStep(BalanceDetailsSteps.balanceDetails);
  }, [currentStep, form, selectedRows]);

  const onCloseDrawer = useCallback(
    (isSubmit?: boolean) => {
      setCurrentStep(BalanceDetailsSteps.balanceDetails);
      if (isSubmit) {
        onClose();
      }
    },
    [onClose]
  );

  const onProceed = useCallback(() => {
    if (
      currentStep === BalanceDetailsSteps.paymentDetails ||
      paymentStepDisabled
    )
      return;
    setCurrentStep(BalanceDetailsSteps.paymentDetails);
  }, [currentStep, paymentStepDisabled]);

  const onSubmitRefund = useCallback(() => {
    const values = form.getFieldsValue();
    const clonedDataSource = cloneDeep(dataSource);
    const { data = {}, comments, referenceNo } = values;
    const refundFileRecordIdList = attachmentRef?.current
      ?.getData?.()
      ?.map((attach: FileQueryData) => attach?.id);
    const params = {
      // 除去payment method data中的空字符串
      ...formatQueryParams(data),
      comments,
      referenceNo,
      payDetailList: clonedDataSource,
      refundFileRecordIdList,
      paymentAmount: totalPaymentAmount,
      availableAmount: totalAvailableAmount,
      payeeName: clonedDataSource?.[0]?.payeeName,
      policyNo,
      claimCaseNo,
      currency: balanceDetail?.currency,
    };
    setSubmitting(true);
    BcpService.submitClaimPayoutSuspenseTransactionHistories(params)
      .then(res => {
        refreshClaimPayoutSuspenseList();
        onCloseDrawer(true);
        // res为true表示开关打开，需要走审核流程
        message.success(
          res
            ? t(
                'The case has been sent for approval, please waiting for the final decision.'
              )
            : t('Submitted successfully')
        );
      })
      .catch((error: Error) => {
        message.error(error.message);
      })
      .finally(() => {
        setSubmitting(false);
      });
  }, [
    form,
    dataSource,
    totalPaymentAmount,
    totalAvailableAmount,
    policyNo,
    claimCaseNo,
    balanceDetail?.currency,
    refreshClaimPayoutSuspenseList,
    onCloseDrawer,
    t,
  ]);

  const onSubmit = useCallback(async () => {
    form.validateFields()?.then(() => {
      if (approvalTasksInProgress?.length) {
        Modal.confirm({
          title: t('Submit'),
          icon: null,
          content: t(
            'There are pending approval tasks, Do you still need to continue?'
          ),
          onOk: () => onSubmitRefund(),
        });
      } else {
        onSubmitRefund();
      }
    });
  }, [onSubmitRefund, form, t, approvalTasksInProgress?.length]);

  useEffect(() => {
    form.setFieldValue('referenceNo', generatedReferenceNo);
  }, [form, generatedReferenceNo]);

  useEffect(() => {
    BcpService.getClaimPayoutSuspenseReferenceNo(policyNo).then(res => {
      setGeneratedReferenceNo(res as string);
    });
  }, [policyNo]);

  const { data: claimPayoutSuspenseDetails = [], loading } = useRequest(
    () =>
      BcpService.getClaimPayoutSuspenseRecordDetail({
        claimCaseNo,
        policyNo,
      }),
    {
      onError: err => {
        message.error(err?.message);
      },
    }
  );

  const onModifyReferenceNo = useCallback(() => {
    form.setFieldValue('referenceNo', form.getFieldValue('referenceNoInput'));
  }, [form]);

  const referenceNoField: FieldDataType[] = useMemo(() => {
    const referenceLabel = (
      <LabelWithTooltip
        title={t('Reference No.')}
        tooltip={t(
          'If cleared, system will also automatically generate a reference number.'
        )}
      />
    );
    return referenceNo
      ? [
          {
            col: 16,
            type: FieldType.Customized,
            render: (
              <div className="flex flex-row justify-start items-top">
                <Form.Item
                  name="referenceNo"
                  label={referenceLabel}
                  layout="horizontal"
                  colon={false}
                ></Form.Item>
                <span className={styles.referenceNoContent}>
                  <span>{referenceNo}</span>
                  <Popconfirm
                    icon={null}
                    getPopupContainer={triggerNode => triggerNode.parentElement}
                    title={
                      <Form.Item
                        name="referenceNoInput"
                        label={t('Reference No.')}
                        colon={false}
                        initialValue={referenceNo}
                      >
                        <Input style={{ width: 280 }} />
                      </Form.Item>
                    }
                    cancelText={t('Cancel')}
                    okText={t('Confirm')}
                    onCancel={() =>
                      form.setFieldValue('referenceNoInput', referenceNo)
                    }
                    onConfirm={onModifyReferenceNo}
                  >
                    <EditAction className="ml-xs" />
                  </Popconfirm>
                </span>
              </div>
            ),
          },
        ]
      : [
          {
            key: 'referenceNo',
            col: 16,
            label: referenceLabel,
            type: FieldType.Input,
          },
        ];
  }, [t, onModifyReferenceNo, form, referenceNo]);

  const fields = useMemo(
    (): FieldDataType[] => [
      ...referenceNoField,
      {
        key: 'paymentMethod',
        type: FieldType.Customized,
        col: 24,
        render: (
          <PaymentMethodForm
            form={form}
            payMethodLabel={t('Payment Method')}
            paymentOrCollection={PaymentOrCollection.Payment}
            transactionType={TransactionType.Claim}
            width={625}
            needClearFromNull={false}
            initialValues={editPaymentDetails?.data}
          />
        ),
      },
      {
        label: t('Comment'),
        key: 'comments',
        type: FieldType.InputTextArea,
        col: 16,
        extraProps: {
          maxLength: 1000,
          showCount: true,
        },
      },
    ],
    [t, form, referenceNoField, editPaymentDetails?.data]
  );

  return (
    <Drawer
      title={readOnly ? t('View Balance Details') : t('Edit Balance Details')}
      open={visible}
      rootClassName="nagrand-drawer"
      destroyOnClose={true}
      submitBtnShow={!readOnly}
      cancelText={cancelText}
      sendText={sendText}
      onClose={onClose}
      onSubmit={
        currentStep === BalanceDetailsSteps.balanceDetails
          ? onProceed
          : onSubmit
      }
      onBack={
        currentStep === BalanceDetailsSteps.paymentDetails ? onBack : null
      }
      submitBtnProps={{ loading: submitting, disabled: !selectedRows.length }}
      className={styles.paymentDetailsWrapper}
    >
      {/* 编辑状态下, 有审核中的task，warning */}
      {amountInApproval > 0 && !readOnly && (
        <ViewApprovalHistoryTips
          type={ManualVerificationType.ClaimPayoutVerification}
          queryApi={() =>
            BcpService.queryBalanceRefundVerificationHistory({
              policyNo,
              claimCaseNo,
            })
          }
          tipsType="warning"
          rightComp={
            <span
              className="mr-1"
              style={{
                color: 'var(--primary-color)',
              }}
            >
              {t('View Approval Task History')}
            </span>
          }
          tipsText={
            <Trans
              i18nKey="There are pending approval tasks for Claim Payout Suspense with amount of <1></1>."
              ns="finance"
            >
              There are pending approval tasks for Claim Payout Suspense with
              amount of, with Total Amount:
              <span i18nIsDynamicList>
                {['amount'].map(() => (
                  <span style={{ fontWeight: 700 }}>
                    {formatAmount(
                      amountInApproval?.toString(),
                      balanceDetail?.currency
                    )}
                  </span>
                ))}
              </span>
              .
            </Trans>
          }
        />
      )}
      {/* 只读或者审核中的金额为0时显示 view history */}
      {(amountInApproval === 0 || readOnly) && !!approvalTasks?.length && (
        <ViewApprovalHistoryTips
          queryApi={() =>
            BcpService.queryBalanceRefundVerificationHistory({
              policyNo,
              claimCaseNo,
            })
          }
          tipsType="info"
          tipsText={t('View Approval Task History')}
          type={ManualVerificationType.BalanceRefundVerification}
        />
      )}
      {/* 编辑状态下才能进入payment阶段，因此编辑时才显示进度条 */}
      {!readOnly && (
        <Steps
          paymentStepDisabled={paymentStepDisabled}
          currentStep={currentStep}
          onBack={onBack}
          onProceed={onProceed}
        />
      )}
      {currentStep === BalanceDetailsSteps.balanceDetails && (
        <>
          <Detail info={balanceAccountInfo} />
          <Table
            rowKey="suspenseTransactionDetailId"
            emptyType="text"
            scroll={{ x: 'max-content' }}
            pagination={false}
            loading={loading}
            rowSelection={!readOnly && rowSelection}
            columns={balanceColumns}
            dataSource={claimPayoutSuspenseDetails}
          />
          {!isNil(suspenseTransactionDetailId) && (
            <HistoryDrawer
              visible={!isNil(suspenseTransactionDetailId)}
              suspenseTransactionDetailId={suspenseTransactionDetailId}
              onClose={() => setSuspenseTransactionDetailId(null)}
            />
          )}
        </>
      )}
      {currentStep === BalanceDetailsSteps.paymentDetails && (
        <>
          <Detail info={paymentAccountInfo} />
          <Form form={form} layout="vertical">
            <PaymentDetailsTable
              dataSource={dataSource}
              onChangePaymentAmount={onChangePaymentAmount}
              readOnly={false}
            />
            <CommonForm
              formProps={{ form }}
              key="basic"
              fields={fields}
              use="drawer"
            />
          </Form>
          <UploadAndAttachments
            ref={attachmentRef}
            initialList={editPaymentDetails?.attachments}
          />
          <div className={styles.totalWrapper}>
            <span>{t('Total Payment Amount')}</span>
            <span>
              {formatAmount(totalPaymentAmount, dataSource?.[0]?.currency)}
            </span>
          </div>
        </>
      )}
    </Drawer>
  );
};
