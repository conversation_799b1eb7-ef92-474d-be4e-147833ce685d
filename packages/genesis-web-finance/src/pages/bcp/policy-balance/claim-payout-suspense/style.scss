@import '@/variables.scss';


.total-wrapper {
  width: 1096px;
  margin-left: -32px;
  height: 54px;
  position: fixed;
  bottom: 72px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: var(--gap-md) var(--gap-big);
  box-shadow: 0px var(--gap-xss) var(--gap-lg) 0px var(--box-shadow-color);
  background: var(--white);
  box-sizing: border-box;

  span:nth-child(1) {
    color: var(--text-color-quaternary);
  }

  span:nth-child(2) {
    font-weight: 700;
    font-size: var(--gap-md);
    margin-left: var(--gap-xs);
  }
}

.reference-no-content {
  margin-left: 54px;
  height: 32px;
  display: inline-flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  >span {
    line-height: 32px;
    display: inline-block;
    height: 32px;
  }

  :global {
    .#{$antd-prefix}-popover-inner {
      padding: 0;
    }

    .#{$antd-prefix}-popconfirm-message {
      margin-bottom: 20px;
    }
  }
}