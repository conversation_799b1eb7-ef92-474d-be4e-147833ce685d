import type { FC } from 'react';
import { useMemo } from 'react';

import { Steps } from '@zhongan/nagrand-ui';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';

interface OperationStepsProps {
  currentStep: number;
}

export const OperationSteps: FC<OperationStepsProps> = ({ currentStep }) => {
  const enums = useTenantBizDict();
  const stepItem = useMemo(
    () =>
      enums?.bcpBankTransferRecordStatus.map(status => ({
        title: status.dictValueName,
        key: status.dictValue,
      })),
    [enums]
  );

  return <Steps current={currentStep} items={stepItem} />;
};
