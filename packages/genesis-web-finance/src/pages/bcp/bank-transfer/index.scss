@import '@/variables.scss';

.operation-drawer {

  :global {
    .#{$antd-prefix}-modal-footer .#{$antd-prefix}-btn {
      padding: 10px $gap-md;
      width: 76px;
      height: 40px;
      border: 1px solid var(--border-default);
      border-radius: var(--checkbox-border-radius);
      margin: 5px $gap-md;
    }

    .#{$antd-prefix}-collapse-header {
      padding: 0 !important;
      margin-top: var(--gap-lg);
    }

    .#{$antd-prefix}-collapse-header-text {
      flex: none !important;
      margin-inline-end: var(---gap-xss) !important;
    }

    .#{$antd-prefix}-collapse-content-box {
      padding: 0 !important;
    }
  }

  .operation-content {
    .bank-file {
      padding-top: $gap-big;

      .upload-time {
        font-size: 12px;
        flex: 1;
        text-align: right;
        color: var(--text-color-tertiary);
      }

      :global {
        .file-item {
          >div:nth-child(2) {
            width: 53% !important;
          }
        }
      }

      .file-disabled {
        height: 120px;
        text-align: center;
        background-color: var(--primary-disabled-color);
        border-radius: var(--border-radius-base);

        :global {
          .anticon {
            margin: -10px 0;
          }

          .no-data-text {
            margin-top: 0;
          }
        }
      }

      .upload-response-bank-file {
        margin-bottom: var(--gap-xs);

        :global {
          .#{$antd-prefix}-upload-drag {
            border: none;

            .#{$antd-prefix}-upload {
              padding: 0;
            }

            .#{$antd-prefix}-upload-disabled {
              .nagrand-upload-drag {
                cursor: not-allowed;
              }
            }
          }
        }
      }

      .upload-log {
        margin-top: var(--gap-xs);

        .title {
          cursor: pointer;

          >span {
            margin-left: 10px;
            font-size: $font-size-lg;
          }
        }
      }
    }
  }
}


.modal-container {
  width: 580px;
  height: 400px;
  overflow-y: auto;
  padding: 5px;
}

.modal-label {
  display: inline-block;
  width: 260px;
  padding-bottom: 22px;

  p {
    margin: 0;
    line-height: 20px;
    color: var(--text-color);
    padding-bottom: var(--gap-xss);
  }

  .detailValue {
    color: var(--text-color-secondary);

    &:empty:before {
      content: '--';
    }
  }
}

.download-request-file-modal {
  :global {
    .#{$antd-prefix}-modal-confirm-title {
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-default);
    }
  }
}