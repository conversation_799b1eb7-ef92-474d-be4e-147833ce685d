import type { FC } from 'react';
import { useCallback, useContext, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Modal, message } from 'antd';

import clsx from 'clsx';
import { uniq } from 'lodash-es';

import { Drawer, Steps } from '@zhongan/nagrand-ui';

import {
  BcpService,
  LatestPackageStatusEnum,
  PackageBillItemNested,
} from 'genesis-web-service';

import { usePermission } from '@/hooks/usePermissions';

import { PackageContext } from '../../index';
import { CandidateDetail } from './CandidateDetail';
import { ToBeSentDetails } from './ToBeSentDetails';
import styles from './index.scss';

enum DetailStep {
  Candidate = 0,
  ToBeSent = 1,
}

interface CanfdiateDrawerProps {
  visible: boolean;
  popContainer?: HTMLDivElement;
  onClose: (refresh: boolean) => void;
}

export const CandidateDrawer: FC<CanfdiateDrawerProps> = ({
  visible,
  popContainer,
  onClose,
}) => {
  const { t } = useTranslation('finance');
  const canEdit = usePermission('bcp.dunning.task.edit');
  const candidateRef = useRef(null);
  const toBeSentRef = useRef(null);
  const drawerRef = useRef(null);
  const { packageReferenceNo, status } = useContext(PackageContext);
  const [currentStep, setCurrentStep] = useState(DetailStep.Candidate);
  const [queryLoading, setQueryLoading] = useState(false);
  const isCandidateStep = useMemo(
    () => currentStep === DetailStep.Candidate,
    [currentStep]
  );
  // 选中候选账单，在commit的时候一起提交
  const [selectedCandidates, setSelectedCandidates] = useState<
    PackageBillItemNested[]
  >([]);
  // 已添加账单，缓存列表，选中后无法去掉勾选，因此不受翻页影响，在commit的时候一起提交
  const [addedList, setAddedList] = useState<PackageBillItemNested[]>([]);
  // 待发送列表长度，用于Steps标题展示
  const [toBeSentListLength, setToBeSentListLength] = useState<number>(0);
  const [messageApi, contextHolder] = message.useMessage();

  const warning = useCallback(() => {
    // 黑底白字message展示，在抽屉中间位置。未选择账单点击下一步时提示
    messageApi.open({
      content: t('Please check the item first.'),
      className: styles.candidateCheckTip,
    });
  }, [messageApi, t]);

  const onSubmitToBeSent = useCallback(() => {
    // 后端可以直接拿到to be sent的数据，不需要前端提交
    BcpService.submitPackage(packageReferenceNo).then(() => {
      message.success(t('Submit successfully!'));
      onClose(true);
    });
  }, [packageReferenceNo, t, onClose]);

  const onSubmit = useCallback(() => {
    if (isCandidateStep) {
      // 如果候选账单和已添加账单都为空，则提示，阻止进行下一步
      if (!selectedCandidates?.length && !addedList?.length) return warning();
      // candidate 阶段，提交数据，置为ToBeSent状态
      const policyNoList = uniq(
        [...addedList, ...selectedCandidates]?.map(
          candidate => candidate?.policyNo
        )
      );
      BcpService.savePackage({
        policyNos: policyNoList,
        packageReferenceNo,
      }).then(() => {
        setCurrentStep(DetailStep.ToBeSent);
        setSelectedCandidates([]);
        setAddedList([]);
      });
    } else {
      // ToBeSent 阶段，提交数据，confirm后置为Sent状态
      Modal.confirm({
        title: t('Submit'),
        content: t(
          "Please confirm the current month's external collection report is complete and ready to send."
        ),
        onOk: onSubmitToBeSent,
        okText: t('Confirm'),
        cancelText: t('Cancel'),
      });
    }
  }, [
    isCandidateStep,
    selectedCandidates,
    addedList,
    warning,
    packageReferenceNo,
    t,
    onSubmitToBeSent,
  ]);

  const onSave = useCallback(() => {
    if (!selectedCandidates?.length) return warning();
    setAddedList(added => [...added, ...selectedCandidates]);
    setSelectedCandidates([]);
  }, [selectedCandidates, warning]);

  const stepItems = useMemo(
    () => [
      {
        title: t('Candidate Details'),
      },
      {
        title: t('External Report Sending ({{number}})', {
          number: toBeSentListLength,
        }),
      },
    ],
    [toBeSentListLength, t]
  );

  const onCloseDrawer = useCallback(() => {
    setSelectedCandidates([]);
    setAddedList([]);
    setToBeSentListLength(0);
    setCurrentStep(DetailStep.Candidate);
    onClose(false);
  }, [onClose]);

  const onBack = useCallback(() => {
    setCurrentStep(DetailStep.Candidate);
    candidateRef?.current?.queryPackageBillList();
  }, []);

  const onChangeStep = useCallback((step: DetailStep) => {
    setCurrentStep(step);
    if (step === DetailStep.Candidate) {
      candidateRef?.current?.queryPackageBillList();
    } else if (step === DetailStep.ToBeSent) {
      toBeSentRef?.current?.queryPackageBillList();
    }
  }, []);

  return (
    <Drawer
      title={t('Confirm Candidate')}
      open={visible}
      sendText={isCandidateStep ? t('Commit Selections') : t('Submit')}
      cancelText={t('Cancel')}
      onSubmit={onSubmit}
      onClose={onCloseDrawer}
      onClear={
        canEdit &&
        status !== LatestPackageStatusEnum.Confirm &&
        isCandidateStep &&
        !queryLoading &&
        onSave
      }
      clearText={t('Add to Download Queue')}
      getContainer={() => popContainer}
      submitBtnShow={canEdit && status !== LatestPackageStatusEnum.Confirm}
      submitBtnProps={{
        disabled:
          queryLoading ||
          (currentStep === DetailStep.ToBeSent && !toBeSentListLength),
      }}
      onBack={!isCandidateStep && onBack}
      rootClassName="nagrand-drawer"
      ref={drawerRef}
    >
      {contextHolder}
      <Steps
        current={currentStep}
        items={stepItems}
        className={clsx(styles.detailsSteps, 'w-[600px] mb-4')}
        onChange={onChangeStep}
      />
      {currentStep === DetailStep.Candidate && (
        <CandidateDetail
          loading={queryLoading}
          setLoading={setQueryLoading}
          addedList={addedList}
          selectedCandidates={selectedCandidates}
          setSelectedCandidates={setSelectedCandidates}
          ref={candidateRef}
        />
      )}
      {currentStep === DetailStep.ToBeSent && (
        <ToBeSentDetails
          loading={queryLoading}
          setLoading={setQueryLoading}
          selectedCandidates={selectedCandidates}
          setSelectedCandidates={setSelectedCandidates}
          ref={toBeSentRef}
          setToBeSentListLength={setToBeSentListLength}
        />
      )}
    </Drawer>
  );
};
