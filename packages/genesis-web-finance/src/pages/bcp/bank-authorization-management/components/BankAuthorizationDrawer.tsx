import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { Col, ConfigProvider, message } from 'antd';

import {
  type ColumnsType,
  Drawer,
  QueryForm,
  Steps,
  Table,
} from '@zhongan/nagrand-ui';

import type {
  BankAuthorization,
  PolicySelectionRes,
} from 'genesis-web-service';
import { BcpService } from 'genesis-web-service';

import { Mode } from '@/types/common';

import { BankAuthorizationDetail } from './BankAuthorizationDetail';

interface BankAccountDetailDrawerProps {
  visible: boolean;
  bankAuthorization: BankAuthorization;
  mode: Mode;
  onClose: (isRefresh?: boolean) => void;
}
enum Step {
  SelectBusinessCase = 0,
  Application = 1,
}

/**
 * Bank Account 的详情页，是一个抽屉
 * @param visible
 * @param mode
 * @param bankAuthorization
 * @param onClose
 * @constructor
 */
export const BankAuthorizationDetailDrawer = ({
  visible,
  mode,
  bankAuthorization = {},
  onClose,
}: BankAccountDetailDrawerProps) => {
  const { t } = useTranslation('finance');

  const { getPopupContainer } = useContext(ConfigProvider.ConfigContext);
  const [currentStep, setCurrentStep] = useState<Step>();
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([] as React.Key[]);
  const [tableData, setTableData] = useState([]);
  const contentRef = useRef(null);
  const [submitting, setSubmitting] = useState(false);
  const readOnly = useMemo(() => !mode || mode === Mode.Read, [mode]);

  useEffect(() => {
    setCurrentStep(
      mode === Mode.Add ? Step.SelectBusinessCase : Step.Application
    );
    setTableData([]);
    setSelectedRowKeys([]);
  }, [mode]);

  const policySelection = useMemo(() => {
    const { bankAuthorizationId, proposalNo, policyNo, policyHolderName } =
      mode === Mode.Add ? tableData?.[0] || {} : bankAuthorization;
    return {
      bankAuthorizationId,
      policyHolderName,
      proposalNo,
      policyNo,
    };
  }, [mode, tableData, bankAuthorization]);

  const onSubmit = useCallback(() => {
    if (currentStep === Step.SelectBusinessCase) {
      setCurrentStep(Step.Application);
    } else {
      contentRef.current.getData().then((values: BankAuthorization) => {
        setSubmitting(true);
        const updateRequest =
          mode === Mode.Add
            ? BcpService.addBankAuthorization
            : BcpService.updateBankAuthorization;
        updateRequest({
          ...policySelection,
          ...values,
          modifyRelation: mode === Mode.Edit ? false : undefined,
        })
          .then(() => {
            message.success(t('Save successfully'));
            onClose(true);
          })
          .catch(error => message.error(error.message))
          .finally(() => setSubmitting(false));
      });
    }
  }, [mode, onClose, currentStep, policySelection]);

  const drawerTitle = useMemo(
    () =>
      ({
        [Mode.Add]: t('Add Authorization'),
        [Mode.Read]: t('View'),
        [Mode.Edit]: t('Editing'),
      })[mode],
    [mode, t]
  );

  const stepItems = useMemo(
    () => [
      {
        title: t('Select Business Case'),
      },
      {
        title: t('Application'),
      },
    ],
    [currentStep, t]
  );

  const tableColumns = useMemo(
    (): ColumnsType<PolicySelectionRes> => [
      {
        dataIndex: 'proposalNo',
        title: t('Proposal No.'),
      },
      {
        dataIndex: 'policyNo',
        title: t('Policy No.'),
      },
      {
        dataIndex: 'policyHolderName',
        title: t('Policy Holder Name'),
      },
    ],
    []
  );

  const fields = useMemo(
    () => [
      {
        key: 'policyNo',
        col: 8,
        label: t('Policy No.'),
        extraProps: {
          disabled: mode === Mode.Edit,
        },
      },
      {
        key: 'proposalNo',
        col: 8,
        label: t('Proposal No.'),
        extraProps: {
          disabled: mode === Mode.Edit,
        },
      },
    ],
    [mode]
  );

  const topFields = useMemo(() => {
    const { bankAuthorizationId, proposalNo, policyNo } = policySelection;
    return [
      {
        value: bankAuthorizationId,
        visible: !!bankAuthorizationId,
        label: t('Record ID'),
      },
      {
        value: proposalNo,
        visible: true,
        label: t('Proposal No.'),
      },
      {
        value: policyNo,
        visible: true,
        label: t('Policy No.'),
      },
    ];
  }, [policySelection]);

  const onSearch = useCallback((values: Record<string, unknown>) => {
    if (mode !== Mode.Edit) {
      setLoading(true);
      BcpService.queryPolicySelection(values)
        .then(res => {
          setTableData(res?.policyNo ? [res] : []);
          setSelectedRowKeys([res?.policyNo]);
        })
        .catch(error => message.error(error.message))
        .finally(() => setLoading(false));
    }
  }, []);

  const onBack = useCallback(() => {
    if (mode === Mode.Edit) {
      setTableData([{ ...policySelection }]);
      setSelectedRowKeys([policySelection.policyNo] as React.Key[]);
    }
    setCurrentStep(Step.SelectBusinessCase);
  }, [mode, policySelection]);

  return (
    <Drawer
      open={visible}
      title={drawerTitle}
      cancelText={readOnly ? t('Close') : t('Cancel')}
      sendText={
        currentStep === Step.SelectBusinessCase ? t('Next') : t('Submit')
      }
      backText={
        currentStep === Step.Application && mode !== Mode.Read && t('Back')
      }
      onBack={onBack}
      submitBtnProps={{
        loading: submitting,
        disabled:
          currentStep === Step.SelectBusinessCase && !selectedRowKeys[0],
      }}
      onClose={() => onClose()}
      onSubmit={onSubmit}
      getContainer={() => getPopupContainer() as HTMLDivElement}
      readonly={readOnly}
    >
      {!readOnly && (
        <Steps
          current={currentStep}
          items={stepItems}
          className="mb-6 w-[60%]"
        />
      )}
      {currentStep === Step.Application && (
        <>
          <div className="rounded p-4 flex mb-6 bg-tableHoverBg">
            {topFields.map(
              ({ value, label, visible }) =>
                visible && (
                  <Col span={8}>
                    <div className="font-bold mt-1 text-label">{label}</div>
                    <div className="font-bold text-labelColor">{value}</div>
                  </Col>
                )
            )}
          </div>
          <BankAuthorizationDetail
            ref={contentRef}
            disabled={readOnly}
            bankAuthorization={bankAuthorization}
          />
        </>
      )}
      {mode !== Mode.Read && currentStep === Step.SelectBusinessCase && (
        <>
          <QueryForm
            queryFields={fields}
            disabled={mode === Mode.Edit}
            onSearch={onSearch}
            loading={loading}
          />
          <Table
            rowSelection={{
              selectedRowKeys,
              type: 'radio',
              onChange: (selectedKeys: React.Key[]) => {
                if (mode === Mode.Add) setSelectedRowKeys([...selectedKeys]);
              },
            }}
            columns={tableColumns}
            dataSource={tableData}
            pagination={false}
            loading={loading}
            rowKey="policyNo"
            emptyType="icon"
          />
        </>
      )}
    </Drawer>
  );
};
