import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Tooltip, message } from 'antd';

import { useDispatch } from '@umijs/max';

import { useRequest, useUpdateEffect } from 'ahooks';

import {
  AddNewButton,
  type ColumnsType,
  EditAction,
  FieldType,
  OperationContainer,
  QueryForm,
  QueryResultContainer,
  StatusTag,
  Table,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';
import { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';

import type {
  BankAuthorization,
  QueryBankAuthorizationParam,
  RelationEnum,
} from 'genesis-web-service';
import { BankAuthorizationStatus, SourceEnum } from 'genesis-web-service';
import { BcpService } from 'genesis-web-service/lib/bcp/bcp.service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { RenderEnums, renderEnumsWithString } from '@/components/RenderEnums';
import { usePermission } from '@/hooks/usePermissions';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { Mode } from '@/types/common';
import { DefaultTablePagination } from '@/utils/constants';

import { BankType, DynamicSelect } from '../components/DynamicSelect';
import { BankAuthorizationDetailDrawer } from './components/BankAuthorizationDrawer';
import { EditConfirm, EditModeEnum } from './components/EditConfirm';

const BankAuthorizationEnumStatus: Record<BankAuthorizationStatus, StatusType> =
  {
    [BankAuthorizationStatus.Approved]: 'success',
    [BankAuthorizationStatus.WaitingForApproval]: 'info',
    [BankAuthorizationStatus.Rejected]: 'error',
    [BankAuthorizationStatus.Cancel]: 'no-status',
    [BankAuthorizationStatus.Terminated]: 'no-status',
    [BankAuthorizationStatus.WaitingForApplication]: 'warning',
  };

const dynamicSelectConfig = {
  [BankType.BankName]: { visible: false, required: false },
  [BankType.BankCode]: { visible: true, required: false },
};
/**
 * Bank Authorization Management入口文件
 */
export default () => {
  const { t } = useTranslation('finance');
  const canEdit = usePermission('bcp.bank-authorization-management.edit');
  const enums = useTenantBizDict();
  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [searchParams, setSearchParams] =
    useState<QueryBankAuthorizationParam>();
  const [form] = Form.useForm();
  const [downloading, setDownloading] = useState(false);
  const [isExistFields, setIsExistFields] = useState(true);
  const [editingRecord, setEditingRecord] = useState<BankAuthorization>();
  const [drawerMode, setDrawerMode] = useState<Mode>();
  const [editVisible, setEditVisible] = useState<boolean>(false);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'collectionPaymentMethod',
        'certiType',
        'relationship',
        'bankAuthorizationStatus',
      ],
    });
  }, [dispatch]);

  const { data: bankAuthorizationData, loading } = useRequest(
    () =>
      BcpService.queryBankAuthorization({
        ...searchParams,
        limit: pagination.pageSize,
        start: (pagination.current - 1) * pagination.pageSize,
      }),
    { refreshDeps: [pagination] }
  );

  const fields = useMemo(
    () => [
      {
        key: 'policyNo',
        label: t('Policy No.'),
      },
      {
        key: 'proposalNo',
        label: t('Proposal No.'),
      },
      {
        key: 'bankAccountNo',
        label: t('Bank Account No.'),
      },
      {
        key: 'status',
        label: t('Authorization Status'),
        type: FieldType.Select,
        extraProps: {
          options: enums?.bankAuthorizationStatus,
        },
      },
      {
        key: 'collectionMethod',
        label: t('Collection Method'),
        type: FieldType.Select,
        extraProps: {
          options: enums?.collectionPaymentMethod,
        },
      },
      {
        key: 'bankName',
        type: FieldType.Customized,
        col: 8,
        render: (
          <DynamicSelect dynamicConfig={dynamicSelectConfig} form={form} />
        ),
      },
    ],
    [t, enums?.bankAuthorizationStatus, enums?.collectionPaymentMethod, form]
  );

  useUpdateEffect(() => {
    setPagination(({ pageSizeOptions }) => ({
      ...DefaultTablePagination,
      pageSizeOptions,
    }));
  }, [searchParams]);

  const transferQueryParams = useCallback(values => {
    setSearchParams(values);
  }, []);

  const onChangePagination = useCallback(
    (current: number, pageSize: number) => {
      setPagination(_pagination => ({
        ..._pagination,
        current,
        pageSize,
      }));
    },
    []
  );

  const onOpenDetailDrawer = useCallback(
    (mode: Mode, record?: BankAuthorization) => {
      record && queryEditingRecord(record);
      setDrawerMode(mode);
    },
    []
  );

  const onCloseDrawer = useCallback((isRefresh?: boolean) => {
    setEditingRecord(undefined);
    setDrawerMode(undefined);
    if (isRefresh) {
      setPagination(_pagination => ({
        ..._pagination,
      }));
    }
  }, []);

  const tableColumns = useMemo(
    (): ColumnsType<BankAuthorization> => [
      {
        dataIndex: 'collectionMethod',
        title: t('Collection Method'),
        render: collectionMethod => (
          <RenderEnums
            enums={enums?.collectionPaymentMethod}
            keyName={collectionMethod}
          />
        ),
      },
      {
        dataIndex: 'source',
        title: t('Source of Authorization'),
      },
      {
        dataIndex: 'agreementNo',
        title: t('Authorization Agreement No.'),
      },
      {
        dataIndex: 'status',
        title: t('Authorization Status'),
        render: (status: BankAuthorizationStatus) =>
          status && (
            <StatusTag
              statusI18n={
                <Fragment>
                  {renderEnumsWithString(
                    status,
                    enums?.bankAuthorizationStatus
                  )}
                </Fragment>
              }
              type={BankAuthorizationEnumStatus[status]}
              needDot
            />
          ),
      },
      {
        dataIndex: 'bankAuthorizationId',
        title: t('Record ID'),
      },
      {
        dataIndex: 'proposalNo',
        title: t('Proposal No.'),
      },
      {
        dataIndex: 'policyNo',
        title: t('Policy No.'),
      },
      {
        dataIndex: 'bankAccountNo',
        title: t('Bank Account No.'),
      },
      {
        dataIndex: 'bankCode',
        title: t('Bank Code'),
      },
      {
        dataIndex: 'idType',
        title: t('Account Holder ID Type'),
        render: idType => (
          <RenderEnums enums={enums?.certiType} keyName={idType} />
        ),
      },
      {
        dataIndex: 'fullName',
        title: t('Account Holder Name'),
      },
      {
        dataIndex: 'policyHolderRelation',
        title: t('Policyholder Relationship to Accountholder'),
        render: policyHolderRelation => (
          <RenderEnums
            enums={enums?.relationship}
            keyName={policyHolderRelation}
          />
        ),
      },
      {
        key: 'actions',
        title: t('Actions'),
        align: 'right',
        fixed: 'right',
        render: (_, record) => (
          <TableActionsContainer>
            <ViewAction onClick={() => onOpenDetailDrawer(Mode.Read, record)} />
            <EditAction
              disabled={
                !canEdit || record.source === SourceEnum.E_Authorization
              }
              onClick={() => {
                setEditVisible(true);
                queryEditingRecord(record);
              }}
            />
          </TableActionsContainer>
        ),
      },
    ],
    [canEdit, enums, onOpenDetailDrawer, t]
  );

  const queryEditingRecord = useCallback(
    ({ bankAuthorizationId, policyNo, proposalNo }) => {
      BcpService.queryAuthorizationInfo(
        bankAuthorizationId,
        policyNo,
        proposalNo
      ).then(record => setEditingRecord(record));
    },
    [BcpService, setEditingRecord]
  );

  const onSubmit = (editMode: EditModeEnum, relation: RelationEnum) => {
    if (editMode === EditModeEnum.EditApplication) {
      setDrawerMode(Mode.Edit);
    } else {
      BcpService.updateBankAuthorization({
        ...editingRecord,
        policyHolderRelation: relation,
        bankAuthorizationId: editingRecord.bankAuthorizationId,
        modifyRelation: true,
      })
        ?.then(() => {
          onCloseDrawer(true);
          message.success(t('Save successfully'));
        })
        .catch((error: Error) => {
          message.error(error.message);
        });
    }
    setEditVisible(false);
  };

  const onExtract = () => {
    setDownloading(true);
    BcpService.authorizationExtract({ ...form.getFieldsValue() })
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message))
      .finally(() => setDownloading(false));
  };

  const onValuesChange = useCallback(
    (_, allValues) => {
      const someConditionsInput = Object.values(allValues).some(
        value => value !== undefined && value !== ''
      );
      if (someConditionsInput) {
        setIsExistFields(false);
      } else {
        setIsExistFields(true);
      }
    },
    [setIsExistFields]
  );

  return (
    <>
      <QueryForm
        title={t('Bank Authorization Management')}
        queryFields={fields}
        formProps={{
          form,
          onValuesChange,
        }}
        onSearch={transferQueryParams}
        loading={loading}
      />
      <QueryResultContainer>
        <OperationContainer>
          <OperationContainer.Left>
            <AddNewButton
              type="primary"
              ghost
              onClick={() => onOpenDetailDrawer(Mode.Add)}
            />
          </OperationContainer.Left>
          <OperationContainer.Right>
            <Tooltip
              title={
                isExistFields &&
                t(
                  'Kindly choose at least 1 searching condition before the searching result extraction.'
                )
              }
            >
              <Button
                onClick={() => onExtract()}
                loading={downloading}
                disabled={
                  isExistFields || !bankAuthorizationData?.results?.length
                }
              >
                {t('Extract')}
              </Button>
            </Tooltip>
          </OperationContainer.Right>
        </OperationContainer>
        <Table
          scroll={{ x: 'max-content' }}
          rowKey="bankAuthorizationId"
          columns={tableColumns}
          dataSource={bankAuthorizationData?.results}
          loading={loading}
          emptyType="icon"
          pagination={{
            ...pagination,
            total: bankAuthorizationData?.total,
            show: true,
            onChange: onChangePagination,
          }}
        />
      </QueryResultContainer>
      <BankAuthorizationDetailDrawer
        visible={!!drawerMode}
        mode={drawerMode}
        bankAuthorization={editingRecord}
        onClose={onCloseDrawer}
      />
      <EditConfirm
        onOk={onSubmit}
        open={editVisible}
        onCancel={() => setEditVisible(false)}
        relationship={enums?.relationship}
        bankAuthorization={editingRecord}
      />
    </>
  );
};
