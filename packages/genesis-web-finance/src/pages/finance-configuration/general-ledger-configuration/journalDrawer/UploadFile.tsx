import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Spin, Tooltip, Upload, message } from 'antd';
import type { RcFile } from 'antd/lib/upload';

import { ExcelType, Icon } from '@zhongan/nagrand-ui';

import { FileItem } from 'genesis-web-component/lib/components/FileItem';
import { FinanceService } from 'genesis-web-service';
import { security } from 'genesis-web-shared';

import { useFinanceFileDownload } from '../hooks/common';
import styles from './index.scss';

interface IProps {
  canEdit?: boolean;
  fileRecord: { fileUniqueCode: string; fileName: string };
  onChange?: (resFile: string) => void;
  setFileRecord: (resFile: {
    fileUniqueCode: string;
    fileName: string;
  }) => void;
}

/**
 *
 * @description 自定义formItem：upload file
 */
export const UploadFile = ({
  canEdit,
  fileRecord,
  setFileRecord,
  onChange,
}: IProps) => {
  const { t } = useTranslation('finance');
  const [uploading, setUploading] = useState(false);
  const [curFile, setCurFile] = useState<RcFile>();
  const { download } = useFinanceFileDownload();
  // 自定义上传
  const handleUpload = useCallback(() => {
    const formData = new FormData();
    formData.append('file', curFile);
    setUploading(true);

    FinanceService.uploadJournalEntryAccountFile(formData)
      .then(res => {
        onChange?.(JSON.stringify(res));
        setFileRecord(res);
        message.success(t('Uploaded successfully'));
      })
      .catch(error => {
        message.error(error?.message || t('Uploaded failed'));
      })
      .finally(() => {
        setUploading(false);
      });
  }, [t, curFile, onChange]);

  const uploadProps = useMemo(
    () => ({
      headers: { ...security.csrf() },
      showUploadList: false,
      multiple: false,
      acceptFileTypeList: ExcelType,
      beforeUpload: (file: RcFile) => {
        let existFileTypeError = false;
        let existFileSizeError = false;
        const fileType = file.name.split('.').pop();
        const size = file.size / 1024 / 1024;
        if (ExcelType.indexOf(`.${fileType}`) === -1) {
          existFileTypeError = true;
        } else if (size > 10) {
          existFileSizeError = true;
        }
        if (existFileTypeError) {
          message.error(t('You can only upload XLS/XLSX'));
        } else if (existFileSizeError) {
          message.error(
            t('Upload Size Limit', {
              size: 10,
            })
          );
        }
        setCurFile(file);
        return !existFileTypeError && !existFileSizeError;
      },
      customRequest: handleUpload,
    }),
    [handleUpload, setCurFile]
  );

  const onDeleteFile = useCallback(() => {
    setFileRecord(undefined);
    onChange?.(JSON.stringify({}));
  }, [setFileRecord, onChange]);

  return (
    <div className={styles.attachment}>
      <Spin spinning={uploading}>
        {!fileRecord?.fileUniqueCode && (
          <Upload.Dragger
            disabled={!canEdit}
            {...uploadProps}
            className={styles.dragUploadBox}
          >
            <Icon type="drag-upload" style={{ fontSize: '50px' }} />
            <p className={styles.uploadText}>
              {t('Click or drag the file here to upload')}
            </p>
            <p className={styles.uploadHint}>
              {t('Please Upload the Completed Template')}
            </p>
          </Upload.Dragger>
        )}
      </Spin>
      {fileRecord?.fileUniqueCode && (
        <FileItem
          fileName={fileRecord.fileName}
          isShowHover={true}
          style={{ marginTop: 16 }}
          modalGetContainer={false}
          hoverInfoList={[
            {
              key: 'download',
              icon: (
                <div>
                  <Tooltip title={t('Download')}>
                    <Icon
                      type="download"
                      onClick={() => download(fileRecord.fileUniqueCode)}
                      className={styles.downloadAction}
                    />
                  </Tooltip>
                </div>
              ),
            },
            {
              key: 'delete',
              icon: <Icon type="delete" />,
              onClick: onDeleteFile,
              disabled: uploading || !canEdit,
            },
          ]}
        />
      )}
    </div>
  );
};
