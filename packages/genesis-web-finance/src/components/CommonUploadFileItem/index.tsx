import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import { AxiosResponse } from 'axios';

import {
  DeleteAction,
  Icon,
  TextBody,
  TextEllipsisDetect,
  UploadFileItem,
} from '@zhongan/nagrand-ui';
import { FileProp } from '@zhongan/nagrand-ui/dist/components/UploadFileItem/UploadFileItem';

import { FileResponseItem } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { getFileUrlWithCleanup } from 'genesis-web-shared/lib/util/fileType';

import styles from './index.scss';

interface IProps {
  file: FileResponseItem;
  downloadApi: (id: number) => Promise<AxiosResponse>;
  readOnly: boolean;
  onDeleteConfirm?: () => void;
  hoverInfoList?: FileProp['hoverInfoList'];
  customerDom?: JSX.Element;
  needPreview?: boolean;
}
/**
 * @description 抽取的finance域通用的上传file展示组件，默认显示上传user和time
 * hoverInfoList: 编辑态下显示下载和删除按钮，readOnly 只显示下载按钮
 * @param file
 * @param downloadApi
 * @param readOnly
 * @param onDeleteConfirm
 */
export const CommonUploadFileItem = ({
  file,
  hoverInfoList,
  customerDom,
  downloadApi,
  readOnly = true,
  onDeleteConfirm,
  needPreview = false,
  ...rest
}: IProps) => {
  const { t } = useTranslation('finance');
  const { id, fileName, uploadUser, uploadTime, fileUnicode } = file;
  const revokeRef = useRef<(() => void) | null>(null);

  const [isBlobUrl, setIsBlobUrl] = useState(false);

  // 创建文件预览服务
  const createFileViewService = useCallback(async (): Promise<string> => {
    const {
      url,
      revoke,
      isBlobUrl: isBlob,
    } = await getFileUrlWithCleanup(fileName, fileUnicode, downloadApi);

    setIsBlobUrl(isBlob);
    revokeRef.current = revoke;

    return url;
  }, [fileName, fileUnicode, downloadApi]);

  const onDownload = useCallback(
    id => {
      downloadApi(id)
        .then(downloadFile)
        .catch((error: Error) =>
          message.error(error?.message || t('Download failed'))
        );
    },
    [t]
  );

  // 组件卸载时清理 blob URL
  useEffect(() => {
    return () => {
      if (revokeRef.current && isBlobUrl) {
        revokeRef.current();
      }
    };
  }, [isBlobUrl, file.fileUnicode]);

  return (
    <UploadFileItem
      key={id}
      needPreview={needPreview}
      fileName={fileName}
      fileNameRender={<TextEllipsisDetect text={fileName} />}
      className={styles.uploadFile}
      fileViewService={createFileViewService}
      hoverInfoList={
        hoverInfoList ?? [
          {
            icon: <Icon type="download" />,
            onClick: () => onDownload(file?.id),
          },
          !readOnly &&
            onDeleteConfirm && {
              icon: (
                <DeleteAction
                  onClick={onDeleteConfirm}
                  doubleConfirmType="modal"
                />
              ),
            },
        ]
      }
      customerDom={
        customerDom ?? (
          <div>
            <TextBody type="caption">
              {!!file?.uploadUser && t('By:', { name: uploadUser })}
            </TextBody>
            <TextBody type="caption">
              {!!file?.uploadTime &&
                dateFormatInstance.getDateTimeString(uploadTime)}
            </TextBody>
          </div>
        )
      }
      {...rest}
    />
  );
};
