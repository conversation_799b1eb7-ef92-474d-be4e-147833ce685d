/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { Moment } from 'moment';
import { PaginationReq, PaginationResultBase } from '../common.interface';

export interface GlAccount {
  accountId: number;
  accountCode: string;
  accountName: string;
  accountType: string;
  description: string;
  canDelete: boolean;
  bankAccountInfo: {
    bankAccountId: string;
    bankCode: string;
    bankName: string;
    bankAccountName: string;
    bankAccountNo: string;
  }[];
}

export interface GlSegmentation {
  segmentationId: number;
  segmentationCode: string;
  segmentationName: string;
  description: string;
  canDelete: boolean;
}

export type AccountsRequest = Partial<GlAccount> &
  PaginationReq & {
    fuzzyAccountCode?: string; // 用于模糊查询account code
    fuzzyAccountName?: string; // 用于模糊查询account name
  };

export type SegmentationsRequest = Partial<GlSegmentation> &
  PaginationReq & {
    fuzzySegmentationCode?: string;
    fuzzySegmentationName?: string;
  };

export interface FileUploadResp {
  success: boolean;
  filename: string;
  totalCount: number;
  failedCount: number;
  fileUniqueCode: string;
  errorFileUniqueCode: string;
}

export enum GlTemplateType {
  AccountTemplate = 'account-template',
  SegmentationTemplate = 'segmentation-template',
  MappingTableTemplate = 'mapping-table-template',
}
export interface SegmentationType extends GlSegmentation {
  valueType?: string;
  checked?: boolean;
}
export enum DebitOrCreditEnum {
  Debit = 'D',
  Credit = 'C',
}

// todo: 补齐
export enum TrigerFrequencyType {
  Month = 'MONTH',
}

export interface JournalEntriesRequest {
  entryCode?: string;
  entryName?: string;
  fuzzyEntryCode?: string;
  fuzzyEntryName?: string;
  entryDescription?: string;
  start: number;
  limit: number;
}

export interface JournalEntryConditions {
  id: number;
  glFinanceFieldId: number;
  filterType: string;
  filterValue: string;
  order: number;
}

export interface JournalEntryAccounts {
  id?: number;
  glFinanceFieldId: number[];
  accountCode: string;
  segmentationCode: string;
  valueType: string;
  value: string;
  mappingData?: string;
  mappingDataFileRecordId?: number;
  order: number;
  segmentationName: string;
  accountName: string;
  mappingDataFileRecord?: FileUploadResp;
  dC: DebitOrCreditEnum;
  rowSpan?: number;
  fileUniqueCode?: string;
}

export interface JournalEntry {
  id: number;
  entryCode: string;
  entryName: string;
  entryDescription: string;
  status: string;
  journalEntryConditions: JournalEntryConditions[];
  debitJournalEntryAccounts: JournalEntryAccounts[];
  creditJournalEntryAccounts: JournalEntryAccounts[];
  boundByScenario?: boolean;
  glJournalEntryId?: string;
}

export type JournalEntriesResponse = PaginationResultBase<JournalEntry>;

export type JournalEntryDetailResponse = JournalEntry;

export interface SaveJournalEntryRequest {
  id?: number;
  entryCode: string;
  entryName: string;
  entryDescription?: string;
  journalEntryConditions?: JournalEntryConditions[];
  dcAccounts?: JournalEntryAccounts[];
}

export type SaveJournalEntryResponse = JournalEntry;

export interface GLFinanceField {
  financeFiledId: number;
  tableField: string;
  fieldDesc: string;
  fieldName: string;
  dataType: string;
  dataFormat: string;
  enumName: string;
  i18nFieldName: string;
  bizDictKey: string;
}

export interface mappingDataFileRecordType {
  id: number;
  fileName: string;
  fileUniqueCode: string;
  mappingData: string;
}
export interface JournalEntryAccount {
  id: number;
  glFinanceFieldId: number[];
  accountCode: string;
  segmentationCode: string;
  valueType: string;
  value: string;
  mappingData: string;
  mappingDataFileRecordId: number;
  order: number;
  segmentationName: string;
  accountName: string;
  mappingDataFileRecord: mappingDataFileRecordType;
  dC: string;
  checked?: boolean;
  fileUniqueCode?: string;
  description: string;
}

export enum ValueType {
  FixedValue = 'FIXED_VALUE',
  RelationalFieldValue = 'RELATIONAL_FIELD_VALUE',
  ComplexValue = 'COMPLEX_VALUE',
}

export interface businessScenarioCondition {
  businessScenarioConditionId: string;
  glBusinessScenarioId: string;
  filterType: string;
  filterValue: string;
  order: number;
}

export interface businessScenarioEntry extends Partial<JournalEntry> {
  start: number;
  limit: number;
  businessScenarioId: string;
  glJournalEntryId: string;
  businessScenarioEntryId?: number;
  glBusinessScenarioId?: number;
}
export interface BusinessScenarioDetail {
  businessScenarioId: string;
  scenarioCode: string;
  scenarioName: string;
  triggerFrequencyType: string;
  triggerFrequencyTime: number;
  triggerFrequencyTimeType: string;
  scenarioDescription: string;
  businessScenarioConditions: string[];
  businessScenarioEntryResponses: businessScenarioEntry[];
}

export interface BusinessScenariosRequest {
  scenarioCode: string;
  scenarioName: string;
  triggerFrequencyType: string;
  triggerFrequencyTime: string | number;
  triggerFrequencyTimeType: string;
  scenarioDescription: string;
}

export interface EntriesMapBusinessScenarioRequest {
  start: number;
  limit: number;
  fuzzyEntryCode: string;
  fuzzyEntryName: string;
  businessScenarioId: number;
}

export interface BindEntriesToBusinessScenarioRequest {
  businessScenarioId: number;
  businessScenarioEntryRequests: Partial<businessScenarioEntry>[];
}

export type BusinessScenarioRequest = Partial<BusinessScenarios> &
  PaginationReq & {
    fuzzyBusinessScenarioCode?: string;
    fuzzyBusinessScenarioName?: string;
  };

export type BusinessScenarioResponse = PaginationResultBase<BusinessScenarios>;

export interface BusinessScenarios {
  start: number;
  limit: number;
  businessScenarioId: number;
  scenarioCode: string;
  scenarioName: string;
  triggerFrequencyType: string;
  triggerFrequencyTime: number;
  scenarioDescription: string;
  businessScenarioConditionResponses: [];
  businessScenarioEntryResponses: [];
  status?: string;
}
export interface EntryUsedCheckResult {
  whetherUsed: boolean;
  usedBusinessScenarioCodes: string[];
}

export interface GeneralLedgerDrawerData {
  start: number;
  limit: number;
  id?: number;
  dc?: DebitOrCreditEnum;
  businessScenarioCode?: string;
  businessScenarioName?: string;
  dataStatus?: unknown;
  generateDate?: Date;
  voucherCode?: string;
  accountCode?: string;
  currency?: string;
  businessTransactionNo?: string;
  feeAmount?: string;
  accountName?: string;
  segCodeToValueMap?: {
    [K in string]: string;
  };
}

export enum SortWithoutDescendEnum {
  ASC = 'ascend',
  UNDEFINED = 'undefined',
}

export enum BiznoRuleTypeForFinanceEnum {
  InvoiceNo = '533',
  InvoiceCreditNoteNo = '534',
  ReferenceNo = '601',
  ReceiptNo = '605',
  Renewal = '606',
  GroupEBCertificateNo = '613',
}

export enum BiznoDynamicRuleElementStrategyEnum {
  FixedValue = 'FIXED_VALUE',
  TimeStamp = 'TIME_STAMP',
  Sequence = 'SEQUENCE',
  MatrixWithBusinessElements = 'MATRIX_WITH_BUSINESS_ELEMENTS',
  BusinessElements = 'BUSINESS_ELEMENTS',
  PreviousPolicy = 'PREVIOUS_POLICY',
}

// field type用于字段render
export enum GLTableColumnType {
  String = 'STRING',
  Enum = 'ENUM',
  Amount = 'AMOUNT',
  Date = 'DATE',
}

export interface GLTableColumnItem {
  code: string;
  name: string;
  type: GLTableColumnType;
  dictKey?: string;
}
export interface GLListQueryParams extends PaginationReq {
  startAccountingDate?: string;
  endAccountingDate?: string;
  startGenerationDate?: string;
  endGenerationDate?: string;
}

export interface GLListItem {
  postingDate: string;
  accountingDate: string;
  currency: string;
  feeType: string;
  feeAmount: string;
  Business_Unit: string;
}
