import { ColumnsType } from 'antd/lib/table';

import { ISchema } from '@formily/react';

import { compact, omit } from 'lodash-es';

import { SectionFieldsConfigItem } from 'genesis-web-service';

import { AntdQueryFormField, SectionFieldsConfigProps } from './interface';

/**
 * 轻量级的schema遍历工具类 ，用来解耦schema属性处理逻辑
 */
export class SchemaWalk {
  walker!: (schema: ISchema, keyword?: string) => void;
  rootSchema!: ISchema;

  constructor({ schema }: { schema: ISchema }) {
    this.rootSchema = schema;
  }

  public walk(processor: (schema: ISchema) => void): ISchema {
    this.walker = processor;
    this.walker(this.rootSchema);
    this.subSchemaWalk(this.rootSchema);

    return this.rootSchema;
  }

  private subSchemaWalk(schema: ISchema) {
    for (const key in schema.properties) {
      const subSchema = schema.properties[key];
      this.walker(subSchema, key);

      if (subSchema.properties) {
        this.subSchemaWalk(subSchema);
      }
    }
  }
}

// 是否是组合组件 （目前claim中组合组件都是以GridColumnFormItem作为装饰器后续可补充
export const isGroupInSchema = (schema: ISchema): boolean =>
  schema['x-decorator'] === 'GridColumnFormItem';

export const getSchemaFieldProps = (
  schema: ISchema,
  fieldConfig?: SectionFieldsConfigItem
): ISchema => {
  if (!fieldConfig)
    return {
      'x-visible': false, // 不配置的话默认不显示
    };

  return {
    'x-index': fieldConfig.order,
    default: fieldConfig.defaultValue ?? schema.default,
    title: fieldConfig.displayName ?? schema.title,
    'x-visible': true,
  };
};

export const getGroupFieldsProps = (
  subSchemaKeys: string[],
  getFieldConfig: (k: string) => SectionFieldsConfigItem
): ISchema => {
  const subConfigs = compact(subSchemaKeys.map(getFieldConfig));

  // 如果子项都没有配置，那么该组件不显示
  if (!subConfigs.length) {
    return {
      'x-visible': false,
    };
  }

  // 如果子项有配置，那么取最小的order作为当前父schema的x-index
  const xIndex = subConfigs.map(item => item.order).sort()[0];

  return {
    'x-visible': true,
    'x-index': xIndex,
  };
};

/**
 * 用来判断当前schema的key是否是解构的key
 * 类似 '[docLastUpdateStartDate,docLastUpdateEndDate]': {
 */
export const isArrayStringKey = (key: string): boolean => key.startsWith('[');

export const getArrayFromString = (keyword: string): string[] =>
  keyword.match(/\[(.+)\]/)?.[1]?.split(',') || [];

export const isGroupInAntdFields = (items: AntdQueryFormField) =>
  !!items.groupItems?.length;

export const getAntdGroupFieldsProps = (
  field: AntdQueryFormField,
  getFieldConfig: (k: string) => SectionFieldsConfigItem,
  formatInitialValue = (_: AntdQueryFormField, preValue?: string) => preValue
): Pick<AntdQueryFormField, 'visible' | 'order' | 'label' | 'groupItems'> & {
  col?: number;
} => {
  const subKeys = field.groupItems?.map(({ key }) => key);
  const subConfigs = compact(subKeys?.map(getFieldConfig)) ?? [];

  // 如果子项都没有配置，那么该组件不显示
  if (!subConfigs.length) {
    return {
      visible: false,
    };
  }

  // 如果子项有配置，那么取最小的order作为当前父节点的order
  const order = subConfigs.map(item => item.order).sort()[0];
  // 在给定的 subConfigs 数组中查找具有 label 属性的第一个对象，并将其 label 值赋给变量 label
  const label = subConfigs.find(item => !!item.label)?.label;
  // 填充默认值，并过滤掉没有配置的字段
  const groupItems = field.groupItems
    ?.map(item => ({
      ...item,
      key: item.name ?? item.key,
      defaultValue: formatInitialValue(
        item,
        getFieldConfig(item.key)?.defaultValue ?? item.defaultValue
      ),
    }))
    ?.filter(item => !!getFieldConfig(item.key)); // 只保留有配置的字段

  // 根据实际的 groupItems 数量动态计算 col 值
  // 基于原来的 col 和 length 算出单位占比，再根据实际 length 计算
  const originalLength = field.groupItems?.length ?? 1;
  const unitCol = field.col / originalLength;
  const dynamicCol = (groupItems?.length ?? 1) * unitCol;

  return {
    visible: true,
    label: label ?? field?.label,
    order,
    groupItems,
    col: dynamicCol,
  };
};

/**
 * 获取useRequest的cacheKey, 作用约等于swr的key
 */
export const getConfigCacheKey = ({
  pageCode,
  section,
  businessModule,
}: SectionFieldsConfigProps['fetchParams']): string => {
  return `query-fields-and-columns-${pageCode}-${section}-${businessModule}`;
};

export const getSerialFixedColumnList = <T>(columns: ColumnsType<T> = []) => {
  const sortedFormFirstColumns = columns?.reduce((cols, currentCol) => {
    // fixed: 'left'(true 等效于left)，允许从数组开头连续排列
    if (cols.length) {
      const last = cols[cols.length - 1];
      const col =
        last.fixed !== true && last.fixed !== 'left'
          ? omit(currentCol, 'fixed')
          : currentCol;
      return cols.concat([col]);
    }
    return [currentCol];
  }, []);

  const sortedFormLastColumns = columns
    ?.reverse()
    ?.reduce((cols, currentCol) => {
      // fixed: 'right'，允许从数组连续排列到结尾
      if (cols.length) {
        const last = cols[cols.length - 1];
        const col =
          last.fixed !== 'right' ? omit(currentCol, 'fixed') : currentCol;
        return cols.concat([col]);
      }
      return [currentCol];
    }, [])
    ?.reverse();

  return sortedFormFirstColumns?.map((col, index) => {
    return {
      ...col,
      fixed: col?.fixed ?? sortedFormLastColumns?.[index]?.fixed,
    };
  });
};
