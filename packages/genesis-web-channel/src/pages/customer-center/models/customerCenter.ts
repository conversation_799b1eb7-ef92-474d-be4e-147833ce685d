import type { Effect, Reducer } from '@umijs/max';

import { groupBy, keyBy } from 'lodash-es';

import { LinkageWithZipCode } from 'genesis-web-component/lib/components/Address';
import type {
  BizDictItem,
  CustomerComment,
  CustomerPersonStatistics,
  CustomerPolicyAndClaim,
  PaginationResultBase,
} from 'genesis-web-service';
import {
  CompanyCustomerService,
  CustomerQueryType,
  MetadataService,
  YesOrNo,
  claimService,
  partyService,
} from 'genesis-web-service';

import {
  judgeDropDownByAddressModel,
  judgeDropDownByBankModel,
  judgeGoogleMapByAddressModel,
} from '@/pages/customer-center/utils';
import type { ConnectModel } from '@/types/common';
import type { CustomerNameGroupFormat, LinkageMap } from '@/types/customer';
import { CustomerNameConnectedType } from '@/types/customer';
import {
  CustomerNameGroupChild,
  CustomerNameGroupFullNameMap,
} from '@/utils/constants';

export interface CustomerCenterState {
  comments: PaginationResultBase<CustomerComment>;
  policyAndClaim: CustomerPolicyAndClaim;
  dictValueMap: Record<string, BizDictItem[]>;
  linkageMap: LinkageMap;
  customerType: CustomerQueryType;
  nameGroupsFormat: Record<string, CustomerNameGroupFormat>;
}

export interface CustomerCenterModelType
  extends ConnectModel<CustomerCenterState> {
  namespace: string;
  effects: {
    getPolicyAndClaim: Effect;
    getLinkageMap: Effect;
    getComments: Effect;
    getNameGroupsFormat: Effect;
  };
  reducers: {
    saveLinkageMap: Reducer;
    saveDictValueMap: Reducer;
    saveCustomerType: Reducer;
    savePolicyAndClaim: Reducer;
    saveComments: Reducer;
    saveNameGroupsFormat: Reducer;
  };
}

const CustomerCenterModel: CustomerCenterModelType = {
  namespace: 'customerCenter',
  state: {
    comments: null,
    policyAndClaim: null,
    linkageMap: null,
    dictValueMap: null,
    customerType: null,
    nameGroupsFormat: null,
  },
  effects: {
    *getComments({ payload }, { call, put }) {
      const {
        customerId: partyRelationId,
        customerType,
        depositCustomerType,
      } = payload;
      if (!partyRelationId || !customerType) return;

      const requestPrams = {
        limit: 10,
        pageIndex: 1,
        condition: { partyRelationId },
      };

      let comments;

      if (customerType === CustomerQueryType.INDIVIDUAL) {
        const res = yield call(partyService.operatorTrace, requestPrams);
        comments = res?.value;
      }

      if (customerType === CustomerQueryType.COMPANY) {
        comments = yield call(
          CompanyCustomerService.getCompanyComments,
          depositCustomerType,
          requestPrams
        );
      }

      yield put({ type: 'saveComments', payload: comments });
    },
    *getPolicyAndClaim({ payload }, { call, put }) {
      const { customerId, customerType, depositCustomerType } = payload;
      if (!customerType) return;
      let data: CustomerPolicyAndClaim;

      if (customerType === CustomerQueryType.INDIVIDUAL) {
        const res: CustomerPersonStatistics = yield call(
          partyService.queryPersonStatistics,
          customerId
        );

        // claim的统计数据需要单独获取
        const claimNums = yield call(
          claimService.queryClaimStatisticByPersonId,
          customerId

          // 目前因为organization和individual的接口数据返回结构不一致，
          // 暂时转换一下，等organization的接口重构为一致的时候删除转换
        );
        data = {
          policyNum: res?.policyStatistics.totalPolicyNumber,
          effectivePolicyNum: res?.policyStatistics.effectivePolicyNumber,
          goodsNum: res?.goodsStatistics.totalGoodsNumber,
          goodsNameList: res?.goodsStatistics.goodsNames,
          claimNum: claimNums?.totalCaseCount,
          effectiveClaimNum: claimNums?.processingCaseCount,
        };
      } else {
        data = yield call(
          CompanyCustomerService.queryCompanyBusinessData,
          customerId,
          depositCustomerType
        );
      }

      yield put({ type: 'savePolicyAndClaim', payload: data });
    },
    // 用来获取code的级联关系，因为code有联动的值，所以全段需要写死需要联动的code值
    *getLinkageMap(_, { call, put }) {
      // address和bank需要根据配置中心的设置来进行设置。
      // addressModel和bankModel是dropDown的时候需要配置联动
      // addressCascadedLevel和bankCascadedLevel用来设置是联动的级别
      // linkageWithZipcode是否启用zipCode的联动
      const res: BizDictItem[] = yield call(MetadataService.queryBizDict, {
        dictKeys: [
          'addressModel',
          'bankModel',
          'addressCascadedLevel',
          'bankCascadedLevel',
          'linkageWithZipcode',
          'highestEditableAddressLevel',
        ],
      });

      const dictValueMap = (res || []).reduce(
        (old, current) => ({
          ...old,
          [current.dictKey]: [
            ...((old[current.dictKey] || []) as BizDictItem[]),
            current,
          ],
        }),
        {} as Record<string, BizDictItem[]>
      );

      const addressModel = dictValueMap.addressModel;
      const bankModel = dictValueMap.bankModel;
      const addressCascadedLevel = dictValueMap.addressCascadedLevel;
      const bankCascadedLevel = dictValueMap.bankCascadedLevel;
      const linkageWithZipcode =
        dictValueMap.linkageWithZipcode?.[0]?.dictValue;
      const highestEditableAddressLevel = Number(
        dictValueMap.highestEditableAddressLevel?.[0]?.dictValue ?? 0
      );

      const addressCascadedLevelNum =
        +addressCascadedLevel?.[0]?.dictValue || 0;
      const bankCascadedLevelNum = +bankCascadedLevel?.[0]?.dictValue || 0;

      // 需要联动的字段
      // industry和occupation是上下级联动关系
      // accountType和accountSubType是上下级联动关系
      // organizationType和userInputOrganizationType是上下级联动关系
      // userInputOrganizationType是只有organizationType为others的时候才会显示
      const childCodeMaps: Record<
        string,
        { code: string; showCondition?: string }
      >[] = [
        { industryCode: { code: 'occupationCode' } },
        { accountType: { code: 'accountSubType' } },
        {
          organizationType: {
            code: 'userInputOrganizationType',
            showCondition: 'OTHERS',
          },
        },
      ];

      // addresses中的address需要的联动字段
      // address11和address12是上下级联动
      // address12和address13是上下级联动
      // address13和address13是上下级联动
      const addressMaps = [
        { address11: { code: 'address12' } },
        { address12: { code: 'address13' } },
        { address13: { code: 'address14' } },
        { address14: { code: 'address15' } },
      ];

      // addresses中的address(local)需要的联动字段
      const addressLocalMaps = [
        { address21: { code: 'address22' } },
        { address22: { code: 'address23' } },
        { address23: { code: 'address24' } },
        { address24: { code: 'address25' } },
      ];

      // bank需要联动的字段
      const bankMaps = [
        {
          bankName: { code: 'bankBranchName' },
          bankCode: { code: 'bankBranchCode' },
        },
      ];

      // 因为配置中心设置的是text input类型，这里需要配置对应的dictKey，用来获取对应的下拉框的值
      const addressCodeMaps = [
        { address11: 'address1' },
        { address12: 'address2' },
        { address13: 'address3' },
        { address14: 'address4' },
        { address15: 'address5' },
      ];
      // 配置对应的dictKey
      const addressLocalCodeMaps = [
        { address21: 'address1' },
        { address22: 'address2' },
        { address23: 'address3' },
        { address24: 'address4' },
        { address25: 'address5' },
      ];
      // 配置对应的dictKey
      const bankCodeMaps = [
        {
          bankName: 'bankName',
          bankCode: 'bankCode',
          bankAddress: 'bankAddress',
        },
        {
          bankBranchName: 'bankBranchName',
          bankBranchCode: 'bankBranchCode',
          bankBranchAddress: 'bankBranchAddress',
        },
      ];

      const addressDictMaps: Record<string, string>[] = []; // 用来存放address中code对应的dictKey，根据联动等级来设置
      const bankDictMaps: Record<string, string>[] = []; // 用来存放bank中code对应的dictKey，根据联动等级来设置
      let zipCodeAssociatedCodes: string[] = []; // 需要联动zipCode的字段，根据联动等级来设置
      let sameLevelRelationCodeMap: Record<
        string,
        { code: string; field: keyof BizDictItem }
      > = {}; // 同级联动的字段
      let editableAddressCodes: string[] = []; // 可编辑的address

      // 根据addressModel是否为dropDown来判断是否需要配置联动信息
      if (judgeDropDownByAddressModel(addressModel)) {
        zipCodeAssociatedCodes = [
          'address11',
          'address12',
          'address13',
          'address14',
          'address15',
        ].slice(0, addressCascadedLevelNum);

        childCodeMaps.push(
          ...addressMaps.slice(0, addressCascadedLevelNum - 1)
        );
        childCodeMaps.push(
          ...addressLocalMaps.slice(0, addressCascadedLevelNum - 1)
        );

        addressDictMaps.push(
          ...addressCodeMaps.slice(0, addressCascadedLevelNum)
        );
        addressDictMaps.push(
          ...addressLocalCodeMaps.slice(0, addressCascadedLevelNum)
        );

        // 若配置的highestEditableAddressLevel为3，表示address13-15可以编辑
        if (
          linkageWithZipcode === LinkageWithZipCode.ZipCodeToAddress &&
          highestEditableAddressLevel > 0
        ) {
          editableAddressCodes = [
            'address11',
            'address12',
            'address13',
            'address14',
            'address15',
          ].slice(highestEditableAddressLevel - 1);
        }
      }
      // 根据dropModel是否为dropDown来判断是否需要配置联动信息
      if (judgeDropDownByBankModel(bankModel)) {
        sameLevelRelationCodeMap = {
          bankName: { code: 'bankCode', field: 'dictValue' },
          bankCode: { code: 'bankName', field: 'dictValueName' },
          bankAddress: { code: 'bankAddress', field: 'itemExtend1' },
          bankBranchName: { code: 'bankBranchCode', field: 'dictValue' },
          bankBranchCode: { code: 'bankBranchName', field: 'dictValueName' },
          bankBranchAddress: {
            code: 'bankBranchAddress',
            field: 'itemExtend1',
          },
        };
        childCodeMaps.push(...bankMaps.slice(0, bankCascadedLevelNum - 1));
        bankDictMaps.push(...bankCodeMaps.slice(0, bankCascadedLevelNum));
      }

      const linkageMap = {
        childCodeMapping: childCodeMaps.reduce(
          (out, current) => ({ ...out, ...current }),
          {}
        ),
        addressCodeMapping: addressDictMaps.reduce(
          (out, current) => ({ ...out, ...current }),
          {}
        ),
        bankDictMapping: bankDictMaps.reduce(
          (out, current) => ({ ...out, ...current }),
          {}
        ),
        zipCodeAssociatedCodes,
        sameLevelRelationCodeMap,
        isGoogleMap: judgeGoogleMapByAddressModel(addressModel),
        editableAddressCodes,
      };

      yield put({ type: 'saveLinkageMap', payload: linkageMap });
      yield put({ type: 'saveDictValueMap', payload: dictValueMap });
    },
    *getNameGroupsFormat(_, { call, put }) {
      const data: BizDictItem[] = yield call(MetadataService.queryBizDict, {
        dictKeys: ['customerNameGroup', 'nameFormat'],
      }) ?? [];

      const { customerNameGroup = [], nameFormat = [] } = groupBy(
        data,
        'dictKey'
      );

      const nameFormatMap = keyBy(nameFormat, 'dictValue');

      // 由于后端没有关联把customer name group和name format的关系关联起来
      // 这里关联下他们的关系，name format属于customer name group
      customerNameGroup.forEach(group => {
        group.childList = (
          CustomerNameGroupChild[
            CustomerNameGroupFullNameMap?.[+group.dictValue]
          ] ?? []
        )
          .map(childKey => nameFormatMap[childKey])
          .filter(child => !!child);
      });

      const nameGroupsFormat: Record<string, CustomerNameGroupFormat> =
        Object.entries(CustomerNameGroupChild).reduce(
          (out, [key, value]) => ({
            ...out,
            [key]: {
              character: CustomerNameConnectedType.HalfWidth,
              childKeys: value,
              sequence: value,
            },
          }),
          {}
        );

      customerNameGroup.forEach(
        ({ isOptional, childList, itemExtend1, dictValue: groupDictValue }) => {
          const fullNameKey = CustomerNameGroupFullNameMap[+groupDictValue];
          if (!fullNameKey || isOptional !== YesOrNo.YES) return;
          const sequence: string[] = [];
          childList
            .sort((fieldA, fieldB) => +fieldA.itemExtend1 - +fieldB.itemExtend1)
            .forEach(({ dictValue, isOptional: childIsOptional }) => {
              if (childIsOptional === YesOrNo.YES) {
                sequence.push(dictValue.toString());
              }
            });

          console.log(nameGroupsFormat, 'nameGroupsFormat');
          nameGroupsFormat[fullNameKey].character =
            itemExtend1 as CustomerNameConnectedType;
          if (sequence.length) {
            nameGroupsFormat[fullNameKey].childKeys =
              nameGroupsFormat[fullNameKey].sequence;
            nameGroupsFormat[fullNameKey].sequence = sequence;
          }
        }
      );

      yield put({ type: 'saveNameGroupsFormat', payload: nameGroupsFormat });
    },
  },
  reducers: {
    savePolicyAndClaim(state: CustomerCenterState, { payload }) {
      return {
        ...state,
        policyAndClaim: payload,
      };
    },
    saveLinkageMap(state: CustomerCenterState, { payload }) {
      return {
        ...state,
        linkageMap: payload,
      };
    },
    saveDictValueMap(state: CustomerCenterState, { payload }) {
      return {
        ...state,
        dictValueMap: {
          ...state.dictValueMap,
          ...payload,
        },
      };
    },
    saveCustomerType(state: CustomerCenterState, { payload }) {
      return {
        ...state,
        customerType: payload,
      };
    },
    saveComments(state: CustomerCenterState, { payload }) {
      return {
        ...state,
        comments: payload,
      };
    },
    saveNameGroupsFormat(state: CustomerCenterState, { payload }) {
      return {
        ...state,
        nameGroupsFormat: payload,
      };
    },
  },
};

export default CustomerCenterModel;
