/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import {
  BizDictItem,
  CampaignPartnerManagement,
  CampaignSubPartnerManagement,
  QueryChannelResult,
} from 'genesis-web-service';
import { ChannelResponse } from 'genesis-web-service/service-types/channel-types/package';

/**
 * EditTable 字段属性
 */
export interface EditTableProps {
  editing?: boolean;
  key?: number | string;
}

export interface ZoneOption {
  key: string;
  name: string;
  value: string;
}

export interface TimeZoneProps {
  defaultTimeStamp: number;
  defaultZoneInfo: Record<string, string>;
  defaultZoneOptionList: ZoneOption[];
  zoneInfoList: Record<string, string>;
  zoneOptionList: ZoneOption[];
}

export enum DetailPageMode {
  add = 'add',
  edit = 'edit',
  view = 'view',
  copy = 'copy',
}

export enum ResultKeyEnum {
  management = '1',
  result = '2',
}

export interface CampaignSubPartnerManagementTableData extends CampaignSubPartnerManagement {
  selected?: boolean;
}

export interface CampaignPartnerManagementTableData extends CampaignPartnerManagement {
  name?: string;
  subPartnerManagements: CampaignSubPartnerManagementTableData[];
}

export interface TicketTypeInfo {
  geoCode: string;
  stationId: string;
  startHour: string;
  endHour: string;
}

export interface BizDict extends BizDictItem {
  geoCode: string;
  stationId: string;
  startHour: string;
  endHour: string;
  itemName: string;
  itemExtend1: string;
  dictValueName: string;
  dictValue: string;
  dictKeyName: string;
  dictDesc: string;
  enumItemName: string;
  itemCode?: string;
  key?: string;
  extraInfo: {
    ticketType: TicketTypeInfo;
  };
}

export interface TransBizDictOption {
  labelKey: keyof BizDict;
  valueKey: keyof BizDict;
}

export interface SelectOption {
  label: string;
  value: string | number | boolean;
}

export enum TaxTypeKeyType {
  Levy = '1',
  StampDuty = '2',
}

export enum TaxValueTypeKeyType {
  ByFormula = '1',
  ByAmount = '2',
}

export enum UsageType {
  All = 'All',
  Collection = 'Collection',
  Gift = 'Gift',
  Alone = 'Alone',
  Goods = 'Goods',
  Channel = 'Channel',
  SubChannel = 'SubChannel',
}

export enum FactorType {
  Person = 1,
  Company = 2,
}

export interface MaxAllowedNumberList {
  schemaDefType?: number;
  objectCategory?: number;
  objectSubCategory: number;
  maxAllowedNumber?: number;
}

export interface EnumerationTableList {
  key?: string;
  editing?: boolean;
  fieldValue: number;
  fieldNo: number;
}

export interface PreDefineValues {
  minValue?: number;
  maxValue?: number;
  code?: string;
  valueType?: number;
  valueList?: number[];
  value?: number;
}

export interface ProductSASettingFormFields {
  fixedAmount: number | undefined;
  minProductDeductibleAmount: number | undefined;
  maxProductDeductibleAmount: number | undefined;
  ProductDeductibleAmount: number;
}

export enum ProductDeductibleAmountType {
  FixedAmount = 1,
  Range = 2,
  Enumeration = 3,
}

export type FEChannel = ChannelResponse & {
  /**
   * sub channel上会放父级channel的code
   */
  parentCode?: string;
  parentName?: string;
};

export interface PaginationConfig {
  total: number;
  pageSize: number;
  current?: number;
}

export interface PackageProductPaymentMethodInfo {
  packageId: number;
  productId: number;
  paymentMethod?: string | number;
}

export enum CampaignFEStatus {
  Effective = 'effective',
  Ready = 'ready',
  Expired = 'expired',
}

export enum RuleCategory {
  Underwriting = 'UNDERWRITING',
  Claim = 'CLAIM',
  Compliance = 'COMPLIANCE',
  NB_Operation = 'NB_OPERATION',
  POS = 'POS',
  Campaign = 'CAMPAIGN',
  Dunning = 'DUNNING',
  ILP = 'ILP',
  PremiumCalc = 'PREMIUM_CALC',
  PolicyEffectiveWithoutCollection = 'POLICY_EFF_WO_COLL',
}

export enum ApplicationScopeEnum {
  Library = 'LIBRARY',
  Instance = 'INSTANCE',
}
