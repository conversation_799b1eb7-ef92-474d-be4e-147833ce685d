import { useEffect, useState } from 'react';

import { Button, Form, Table, message } from 'antd';

import { useRequest } from 'ahooks';
import { keyBy } from 'lodash-es';

import { DeleteAction, Drawer, Icon } from '@zhongan/nagrand-ui';

import { ProductTypeEnum } from 'genesis-web-service/lib/common.interface';
import { IsOptional } from 'genesis-web-service/lib/market';
import { IsVirtualType, ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product';
import {
  PackageProductInfoResponseDTO,
  PackageProductSaveResponseDTO,
  PackageWaiverProductRelationDTO,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import Collapse from '@market/components/Collapse';
import GeneralSelect from '@market/components/GeneralSelect';
import ProductTagLabel from '@market/components/ProductTagLabel';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { t } from '@market/i18n';
import { NewMarketService } from '@market/services/market/market.service.new';

import { LiabilityAndInterestTable } from '../LiabilityAndInterestTable/LiabilityAndInterestTable';
import styles from '../PlanConfigurationDrawer/PlanConfigurationDrawer.module.scss';

interface Props {
  packageId: number;
  record: PackageProductInfoResponseDTO;
  visible: boolean;
  onClose: () => void;
  mode: DetailPageMode;
  productType: ProductTypeEnum;
  configedProducts: PackageProductInfoResponseDTO[];
}

export const MainBenefitDrawer = ({
  packageId,
  visible,
  onClose,
  mode,
  record,
  productType,
  configedProducts,
}: Props): JSX.Element => {
  const isOptionalOptions = useBizDictAsOptions('isOptional');
  const [productId, setProductId] = useState<number>();
  const [productLiabilityList, setProductLiabilityList] = useState<PackageProductSaveResponseDTO[]>([]);
  const [form] = Form.useForm();

  const readOnly = mode === DetailPageMode.view;

  const { data: mainProductList, loading } = useRequest(
    () => {
      if (!visible) {
        return Promise.resolve([]);
      }

      return NewMarketService.PackageProductMgmtService.queryProductByCondition({
        productType: productType.toString(),
      }).then(res => res?.value?.filter(item => item.isVirtual === IsVirtualType.No) ?? []);
    },
    {
      refreshDeps: [visible],
    }
  );

  const onSelectMainProduct = async (_productId?: number) => {
    if (!_productId) {
      setProductLiabilityList([]);
      setProductId(undefined);
      return;
    }
    const res = await NewMarketService.PackageProductMgmtService.queryLiabilitys({
      packageId,
      productIds: [_productId],
    });
    if (res && res.products) {
      const arr = res.products.filter(product => product.packageLiabilityList?.length === 0);
      if (arr.length > 0) {
        setProductLiabilityList([]);
        setProductId(undefined);
        message.error(t('The product lacks liabilities, and please add the configuration in the product center'));
        return;
      }

      const product = res.products.find(_product => _product.productType === productType)!;

      // waiver必须设置要waive的附加险，设置初始值
      if (product.productCategoryId === ProductCategoryItemExtend1.Waiver) {
        product.waiverProductRelationDTOList ??= [{}];
      }

      if (record) {
        product.isOptional = record.isOptional;
        const liabilityMap = keyBy(record.packageLiabilitys ?? [], 'liabilityId');
        // 编辑、查看回显
        product.packageLiabilityList?.forEach(liability => {
          if (liabilityMap[liability.liabilityId!]) {
            liability.isSelect = true;
            liability.isOptional = liabilityMap[liability.liabilityId!].isOptional;
          }
        });

        product.waiverProductRelationDTOList = record.waiverProductRelationDTOList;
      }

      setProductLiabilityList(res.products);
      setProductId(_productId);
    }
  };

  useEffect(() => {
    if (visible && record) {
      onSelectMainProduct(record.productId);
    }
  }, [visible, record]);

  const optionalOrRequired = (
    _record: {
      canEdit: boolean;
      isOptional?: IsOptional;
    },
    onChange?: (isOptional: IsOptional) => void
  ) => (
    <GeneralSelect
      onClick={e => e.stopPropagation()}
      disabled={_record.canEdit === false || readOnly}
      allowClear={false}
      defaultValue={_record.isOptional?.toString()}
      onChange={(isOptional: IsOptional) => {
        onChange?.(isOptional);
        _record.isOptional = isOptional;
      }}
      option={isOptionalOptions.map(item => ({
        value: item.value,
        label: Number(item.value) === 1 ? t('Optional') : t('Mandatory'),
      }))}
      showSearch={false}
    />
  );

  const closeDrawer = () => {
    setProductLiabilityList([]);
    setProductId(undefined);
    onClose();
  };

  const submitDrawer = async () => {
    await form.validateFields();
    const product: PackageProductSaveResponseDTO = productLiabilityList[0];
    let hasWaiverDuplicated = false;
    const waiverAttachToProductIdSet: Record<number, boolean> = {};

    if (product.productCategoryId === ProductCategoryItemExtend1.Waiver) {
      hasWaiverDuplicated = !hasWaiverDuplicated
        ? product.waiverProductRelationDTOList?.findIndex(item => {
            if (item.attachToProductId) {
              const isDuplicated = waiverAttachToProductIdSet[item.attachToProductId];
              waiverAttachToProductIdSet[item.attachToProductId] = true;
              return isDuplicated;
            }
            return false;
          }) !== -1
        : true;
    }

    if (hasWaiverDuplicated) {
      message.error(t('Waiver Product Duplicated'));
      return;
    }

    // 过滤责任的isSelect
    productLiabilityList[0].packageLiabilityList = productLiabilityList[0].packageLiabilityList?.filter(
      liability => liability.isSelect
    );

    const api =
      mode === DetailPageMode.edit
        ? NewMarketService.PackageProductMgmtService.singleUpdatePackageProduct(productLiabilityList[0])
        : NewMarketService.PackageProductMgmtService.addPackageProducts({
            packageId,
            products: productLiabilityList,
          });

    api.then(() => {
      message.success(t('Submit successfully!'));
      closeDrawer();
    });
  };

  const waiverColumns = (waiverList: PackageWaiverProductRelationDTO[], productIndex: number, _productId: number) => [
    {
      title: t('Attach to Product'),
      dataIndex: 'attachToProductId',
      key: 'attachToProductId',
      width: 256,
      render: (text: number | undefined, _record: PackageWaiverProductRelationDTO, index: number) => {
        const options = configedProducts
          .filter(product => product.productCategoryId !== ProductCategoryItemExtend1.Waiver)
          .map(product => ({
            label: product.productName,
            value: product.productId,
          }));

        // 枚举没有对应product时清空value
        if (options.findIndex(option => option.value === text) === -1) {
          form.setFieldValue(`attachToProductId ${productIndex} ${index}`, undefined);
          text = undefined;
        }
        return (
          <Form.Item
            rules={[
              {
                message: t('Please select'),
                validator: (_, value) => {
                  if (!value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              },
            ]}
            name={`attachToProductId ${productIndex} ${index}`}
            initialValue={text}
            className="!mb-0"
          >
            <GeneralSelect
              option={options}
              disabled={readOnly}
              onChange={val => {
                _record.attachToProductId = val;
              }}
            />
          </Form.Item>
        );
      },
    },
    {
      title: t('Optional/Mandatory'),
      dataIndex: 'isOptional',
      key: 'isOptional',
      width: 256,
      render: (_: any, _record: PackageWaiverProductRelationDTO) => {
        _record.isOptional = _record.isOptional ?? IsOptional.Optional;
        return <span className="absolute top-2">{optionalOrRequired(_record)}</span>;
      },
    },
    {
      render: (_: any, _record: any, recordIndex: number) => (
        <DeleteAction
          disabled={(waiverList.length === 1 && recordIndex === 0) || readOnly}
          className="float-end"
          onClick={() => {
            productLiabilityList[productIndex].waiverProductRelationDTOList = productLiabilityList[
              productIndex
            ].waiverProductRelationDTOList?.filter((row, index) => index !== recordIndex);

            setProductLiabilityList([...productLiabilityList]);
          }}
        />
      ),
    },
  ];

  return (
    <Drawer
      open={visible}
      title={mode === DetailPageMode.edit ? t('Edit Product And Liability') : t('Add Product And Liability')}
      onClose={closeDrawer}
      readonly={readOnly}
      submitBtnProps={{
        disabled: productLiabilityList.length === 0,
      }}
      onSubmit={submitDrawer}
    >
      <Form form={form} layout="vertical">
        <div>
          <Form.Item
            label={
              productType === ProductTypeEnum.MAIN ? t('Main Product Code / Name') : t('Rider Product Code / Name')
            }
          >
            <GeneralSelect
              loading={loading}
              value={productId}
              options={mainProductList
                ?.filter(product => {
                  // 只有附加险可以选到Waiver
                  if (product.productTypeCode === ProductTypeEnum.MAIN) {
                    return product.productCategoryId !== ProductCategoryItemExtend1.Waiver;
                  }

                  return true;
                })
                .map(item => ({
                  value: item.productId,
                  label: `${item.insuranceProductCode!} / ${item.productName!}`,
                }))}
              style={{ width: 640 }}
              onChange={onSelectMainProduct}
              disabled={mode !== DetailPageMode.add}
            />
          </Form.Item>

          <div className={styles['add-liability-table-wrap']}>
            {/* 产品列表 */}
            {productLiabilityList.map((product, productIndex) => {
              const mandatoryLiability = product.packageLiabilityList?.filter(
                liability => liability.isOptional === IsOptional.Required
              );
              const optionalLiability =
                product.packageLiabilityList?.filter(liability => liability.isOptional === IsOptional.Optional) ?? [];

              return (
                <Collapse
                  key={product.productId}
                  title={
                    <div className={`flex items-center gap-4 ${styles.productCheckBoxStyle}`}>
                      <div className="w-full relative">
                        <div>
                          <div className="flex">
                            <span className="font-bold max-w-[60%] inline-block whitespace-nowrap overflow-hidden text-ellipsis">
                              {t('Product Name')} : {product.productName}
                            </span>
                            {product.productType === ProductTypeEnum.MAIN ? (
                              <ProductTagLabel type="main" />
                            ) : (
                              <ProductTagLabel type="rider" />
                            )}
                          </div>
                          <div className="text-xs font-medium text-@label max-w-[60%] whitespace-nowrap overflow-hidden text-ellipsis">
                            {t('Product Code')} : {product.productCode}
                          </div>
                        </div>
                        <span className={`absolute left-[656px] top-2 ${styles['product-table']}`}>
                          {optionalOrRequired(product)}
                        </span>
                      </div>
                    </div>
                  }
                  visible
                >
                  {optionalLiability?.length ? (
                    <Collapse subTitle={`${t('Optional Liability')}(${optionalLiability.length})`} visible>
                      <LiabilityAndInterestTable
                        liabilityList={optionalLiability}
                        optionalOrRequired={optionalOrRequired}
                        disabled={readOnly}
                      />
                    </Collapse>
                  ) : null}
                  {mandatoryLiability?.length ? (
                    <Collapse subTitle={`${t('Mandatory Liability')}(${mandatoryLiability.length})`}>
                      <LiabilityAndInterestTable
                        liabilityList={mandatoryLiability}
                        optionalOrRequired={optionalOrRequired}
                        disabled={readOnly}
                      />
                    </Collapse>
                  ) : null}
                  {product.productCategoryId === ProductCategoryItemExtend1.Waiver ? (
                    <div>
                      <div className="border-@disabled-color divide-x-half" />
                      <Table
                        // 纯定制样式，不需要替换nagrand表格
                        className={styles['product-waiver-table']}
                        key={productIndex}
                        columns={waiverColumns(
                          product.waiverProductRelationDTOList ?? [],
                          productIndex,
                          product.productId!
                        )}
                        rowKey={(_record, index) => `${index!} ${_record.attachToProductId!}`}
                        size="small"
                        dataSource={product.waiverProductRelationDTOList}
                        pagination={false}
                      />
                      <Button
                        disabled={readOnly}
                        onClick={() => {
                          product.waiverProductRelationDTOList = [
                            ...product.waiverProductRelationDTOList!,
                            {
                              isOptional: IsOptional.Optional,
                            },
                          ];

                          setProductLiabilityList([...productLiabilityList]);
                        }}
                        className="text-@primary-color border-@primary-color m-2"
                      >
                        <Icon type="add" />
                        {t('Add')}
                      </Button>
                    </div>
                  ) : null}
                </Collapse>
              );
            })}
          </div>
        </div>
      </Form>
    </Drawer>
  );
};

export default MainBenefitDrawer;
