import { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Tooltip, message } from 'antd';

import { cloneDeep, forEach, intersection } from 'lodash-es';

import { EditableTable, FieldType, Icon, NewPosition } from '@zhongan/nagrand-ui';

import {
  FormulaProps,
  GoodsSalesPartner,
  MktYesNo,
  PartnerFeeType,
  PartnerType,
  SalesPartner,
  SalesPartnerChannel,
  SalesPartnerFormula,
  SettlementRule,
} from 'genesis-web-service';

import { EMPTY_PLACEHOLDER, NAME_CONNECTOR_STR } from '@market/common/constant';
import { BizTopic } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import { useBizDict } from '@market/hook/bizDict';
import { useChannelAndSubChannelList } from '@market/hook/channel.serivce';
import { OptEnum } from '@market/request/interface';
import { mapBizdictToOldBizdict, mapChannelToOldBizdict, renderOldBizdictName } from '@market/utils/enum';

import ClawbackCommissionFormula from '../ClawbackCommissionFormula';
import CommissionFormulaTable from '../CommissionFormulaTable';

interface Props {
  goodsId: number | string;
  disabled: boolean;
  loading: boolean;
  totalFormulaList: FormulaProps[];
  platformSalesPartner?: SalesPartner;
  onEditStateChange: (isEditing: boolean) => void;
  submitService: (salesPartner: SalesPartner) => Promise<unknown>;
  reload: () => void;
}

export const ChannelConfigTable = ({
  goodsId,
  totalFormulaList,
  platformSalesPartner,
  submitService,
  reload,
  disabled,
  loading,
  onEditStateChange,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const settlementRuleBizDicts = useBizDict('settlementRule');
  const yesNoOptions = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [channelPartnerForm] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const [channelPartnerList, setChannelPartnerList] = useState<Record<string, any>[]>([]);

  const { channelList: totalChannelList, loading: channelLoading } = useChannelAndSubChannelList();

  const partnerType = PartnerType.salesChannel;
  const goodsSalesPartnerList = (platformSalesPartner?.goodsSalesPartnerList as SalesPartnerChannel[]) || [];

  useEffect(() => {
    const channelPartnerListData =
      goodsSalesPartnerList
        .filter(item => item.partnerType === PartnerType.salesChannel)
        .filter(item => item.settlementRule) // 没有settlementRule说明是拆出来的关联的channel
        .map(item => {
          // 合并channel
          const code = item.partnerCodes[0];
          let partnerCodes: string[] = item.partnerCodes;
          if (item.relatedChannelCodeList) {
            partnerCodes = [
              item.subPartnerCode ? `${code}-${item.subPartnerCode}` : `${code}`,
              ...item.relatedChannelCodeList,
            ];
          }
          return {
            key: Math.random(),
            ...item,
            partnerCodes,
            accumulatedToPremium: `${item.accumulatedToPremium!}`,
            settlementRule: `${item.settlementRule!}`,
          };
        }) || [];
    setChannelPartnerList(channelPartnerListData);
  }, [platformSalesPartner?.goodsSalesPartnerList]);

  const totalChannelOptions = useMemo(
    () =>
      totalChannelList
        .map(channel => ({
          ...channel,
          code: channel.parentCode ? `${channel.parentCode}-${channel.code || ''}` : channel.code,
        }))
        .map(mapChannelToOldBizdict) || [],
    [totalChannelList]
  );

  const settlementRuleOptions = useMemo(
    () => settlementRuleBizDicts.map(mapBizdictToOldBizdict),
    [settlementRuleBizDicts]
  );

  const channelPartnerColumns = useMemo(
    () => [
      {
        title: (
          <span className="market-ant4-legacy-form-item-required">
            {t('Sales Platform')}
            <Tooltip
              title={() =>
                t(
                  'If sales platform is configured, all sub channels under this sales platform could sell this product.'
                )
              }
            >
              <Icon type="info-circle" style={{ marginLeft: 5, marginTop: '-3px' }} />
            </Tooltip>
          </span>
        ),
        dataIndex: 'partnerCodes',
        editable: true,
        width: 300,
        render: (partnerCodes: string[], record: SalesPartnerChannel) =>
          partnerCodes
            ?.map(partnerCode => renderOldBizdictName(partnerCode, totalChannelOptions || []) || EMPTY_PLACEHOLDER)
            ?.join(NAME_CONNECTOR_STR),
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: totalChannelOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            mode: 'multiple',
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Settlement Rule')}</span>,
        editable: true,
        dataIndex: 'settlementRule',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: settlementRuleOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
        render: (settlementRule: string): string =>
          renderOldBizdictName(settlementRule, settlementRuleOptions) || EMPTY_PLACEHOLDER,
        width: 240,
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Accumulated to Premium')}</span>,
        editable: true,
        dataIndex: 'accumulatedToPremium',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: yesNoOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
        render: (accumulatedToPremium: string): string =>
          accumulatedToPremium ? renderOldBizdictName(accumulatedToPremium, yesNoOptions) : EMPTY_PLACEHOLDER,
        width: 240,
      },
    ],
    [settlementRuleOptions, t, totalChannelList, totalChannelOptions, yesNoOptions]
  );

  // 多选的channel 一条拆多条数据
  const fillRelatedChannelCodeList = useCallback(
    (list: SalesAttributesNS.FESalesPartnerChannel[]) => {
      const tempList: SalesAttributesNS.FESalesPartnerChannel[] = [];
      // 多选的channel 一条拆多条数据
      list?.forEach(row => {
        const partnerCodes = row.partnerCodes;
        if (partnerCodes.length === 1) {
          tempList.push(row);
        } else {
          const restCodes = partnerCodes.slice(1);
          tempList.push({
            ...row,
            partnerCodes: [partnerCodes[0]],
            relatedChannelCodeList: restCodes,
          });

          restCodes.forEach(code => {
            tempList.push({
              partnerCodes: [code],
              partnerCode: code,
              goodsId,
              partnerType,
            });
          });
        }
      });

      // 如果下拉框里面选的是二级渠道，partnerCodes里面存一级渠道，subPartnerCode里面存二级渠道，relatedChannelCodeList里面存 parent-child 渠道数据
      tempList.forEach(item => {
        const partnerCode = item.partnerCodes[0];

        if (partnerCode.includes('-')) {
          const [parent, child] = partnerCode.split('-');
          item.partnerCodes = [parent];
          item.partnerCode = parent;
          item.subPartnerCode = child;
        }
      });

      return tempList;
    },
    [goodsId, partnerType]
  );

  const saveSalesPartnerNew = useCallback(
    async (list: SalesAttributesNS.FESalesPartnerChannel[] | null, formulalist: SalesPartnerFormula[] | null) => {
      try {
        // 如果没传取原始数据
        let tempGoodsSalesPartnerList = platformSalesPartner?.goodsSalesPartnerList || [];
        if (list) {
          tempGoodsSalesPartnerList = list;
        }

        const goodsSalesPartnerFormulaList = formulalist
          ? formulalist.map(row => {
              row.goodsId = goodsId;
              row.partnerType = partnerType;

              return row;
            })
          : platformSalesPartner?.goodsSalesPartnerFormulaList;

        await submitService({
          goodsId,
          partnerType,
          goodsSalesPartnerList: tempGoodsSalesPartnerList || [],
          goodsSalesPartnerFormulaList: goodsSalesPartnerFormulaList || [],
        });
        message.success(t('Submit Successfully'));
        reload();
      } catch {
        throw new Error();
      }
    },
    [platformSalesPartner, submitService, goodsId, partnerType, t, reload]
  );

  const saveFormulaList = useCallback(
    (list: SalesPartnerFormula[]) => {
      saveSalesPartnerNew(null, list);
    },
    [saveSalesPartnerNew]
  );

  const handleDelete = async (index: number) => {
    const newData = cloneDeep(channelPartnerList) as SalesAttributesNS.FESalesPartnerChannel[];
    newData.splice(index, 1);

    const tempList: GoodsSalesPartner[] = fillRelatedChannelCodeList(newData);
    await saveSalesPartnerNew([...tempList], null);
  };

  const handleConfirm = (value: SalesAttributesNS.FESalesPartnerChannel, key: number | string) => {
    const newData = cloneDeep(channelPartnerList) as SalesAttributesNS.FESalesPartnerChannel[];

    return new Promise<void>(async (resolve, reject) => {
      try {
        await channelPartnerForm.validateFields();

        if (
          newData.find(row => value?.key !== row?.key && intersection(row.partnerCodes, value.partnerCodes)?.length > 0)
        ) {
          message.error(t('Duplicate Sales Platform. Please change.'));
          reject();
          return;
        }

        const parentCodes = value.partnerCodes.map(partnerCode =>
          partnerCode.includes('-') ? partnerCode.split('-')[0] : partnerCode
        );

        forEach(parentCodes, parentCode => {
          const existFirstLevelChannel = newData?.find(
            row => value?.key !== row?.key && row.partnerCodes?.some(item => item.includes(parentCode))
          );

          // 同一个一级渠道下可以配置多个二级渠道，但是不能同时配置一级渠道和对应二级渠道，二级渠道必有subPartnerCode
          if (existFirstLevelChannel) {
            message.error(t('Channel and channel - sub channel could not co-exist. Please change.'));
            reject();
          }
        });

        value.goodsId = goodsId;
        value.partnerType = partnerType;
        if (key === OptEnum.Add) {
          const addIndex = newData.findIndex(item => item.key === OptEnum.Add);
          newData[addIndex] = value;
        } else {
          const editIndex = newData.findIndex(row => row.key === key);
          newData[editIndex] = value;
        }
        // accumulatedToPremium 所有数据保持一致
        newData.forEach(existRow => {
          existRow.accumulatedToPremium = value.accumulatedToPremium;
        });
        const tempList = fillRelatedChannelCodeList(newData);

        await saveSalesPartnerNew([...tempList], null);
        onEditStateChange(false);
        resolve();
      } catch (e) {
        reject(e);
      }
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-return
  const commissionFormulaList = useMemo(
    () =>
      ((platformSalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item => `${item.feeType}` === PartnerFeeType.commission && `${item.formulaCategory}` === BizTopic.Commission
      ) || [],
    [platformSalesPartner?.goodsSalesPartnerFormulaList]
  );

  const clawbackcommissionFormulaList = useMemo(
    () =>
      ((platformSalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission && `${item.formulaCategory}` === BizTopic.CommissionClawback
      ) || [],
    [platformSalesPartner?.goodsSalesPartnerFormulaList]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <EditableTable
        columns={channelPartnerColumns}
        dataSource={channelPartnerList}
        setDataSource={setChannelPartnerList}
        outForm={channelPartnerForm}
        loading={channelLoading || loading}
        initializeAddData={{
          partnerCodes: [],
          settlementRule: SettlementRule.basedOnPremiumOffset,
          accumulatedToPremium: MktYesNo.No,
        }}
        addBtnProps={{
          type: 'dashed',
          addTitle: t('Add'),
          disabled,
          handleAdd: () => onEditStateChange(true),
        }}
        editBtnProps={{ disabled: () => disabled, handleEdit: () => onEditStateChange(true) }}
        deleteBtnProps={{ disabled: () => disabled, handleDelete, doubleConfirmPlacement: 'topRight' }}
        handleCancel={() => onEditStateChange(false)}
        scroll={{ x: 'max-content' }}
        handleConfirm={handleConfirm}
        newPosition={NewPosition.Bottom}
        pagination={{
          hideOnSinglePage: true,
          defaultPageSize: 10,
        }}
      />
      <CommissionFormulaTable
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={commissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...formulaList, ...clawbackcommissionFormulaList]);
        }}
      />
      <ClawbackCommissionFormula
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={clawbackcommissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...commissionFormulaList, ...formulaList]);
        }}
      />
    </div>
  );
};

export default forwardRef(ChannelConfigTable);
