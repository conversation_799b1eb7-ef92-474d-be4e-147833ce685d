import { Ref, forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import { cloneDeep, intersection } from 'lodash-es';

import { EditableTable, FieldType, NewPosition } from '@zhongan/nagrand-ui';

import {
  FormulaProps,
  MktYesNo,
  PartnerFeeType,
  PartnerType,
  SalesPartner,
  SalesPartnerFormula,
  SettlementRule,
} from 'genesis-web-service';

import { EMPTY_PLACEHOLDER, NAME_CONNECTOR_STR } from '@market/common/constant';
import { BizTopic } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import { useBizDict } from '@market/hook/bizDict';
import { useBankList } from '@market/hook/channel.serivce';
import { OptEnum } from '@market/request/interface';
import { mapBizdictToOldBizdict, mapChannelToOldBizdict, renderOldBizdictName } from '@market/utils/enum';

import ClawbackCommissionFormula from '../ClawbackCommissionFormula';
import CommissionFormulaTable from '../CommissionFormulaTable';

interface Props {
  goodsId: number | string;
  disabled: boolean;
  loading: boolean;
  totalFormulaList: FormulaProps[];
  salesPartner?: SalesPartner;
  onEditStateChange: (isEditing: boolean) => void;
  submitService: (salesPartner: SalesPartner) => Promise<unknown>;
  reload: () => void;
}

export const BankConfigSection = ({
  goodsId,
  disabled,
  loading,
  salesPartner,
  totalFormulaList,
  onEditStateChange,
  submitService,
  reload,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const settlementRuleBizDicts = useBizDict('settlementRule');
  const yesNoOptions = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const partnerType = PartnerType.bank;

  const [bankConfigList, setBankConfigList] = useState<Record<string, any>[]>([]);

  const bankList = useBankList();

  useEffect(() => {
    const bankConfigListData =
      salesPartner?.goodsSalesPartnerList?.map(item => ({
        key: Math.random(),
        ...item,
        accumulatedToPremium: `${item.accumulatedToPremium || ''}`,
        settlementRule: `${item.settlementRule || ''}`,
      })) || [];
    setBankConfigList(bankConfigListData);
  }, [salesPartner?.goodsSalesPartnerList]);

  const bankOptions = useMemo(() => bankList.map(mapChannelToOldBizdict), [bankList]);
  const settlementRuleOptions = useMemo(
    () => settlementRuleBizDicts.map(mapBizdictToOldBizdict),
    [settlementRuleBizDicts]
  );

  const bankColumns = useMemo(
    () => [
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Bank')}</span>,
        dataIndex: 'partnerCodes',
        editable: true,
        width: 300,
        render: (partnerCodes: string[]): string =>
          partnerCodes?.map(partnerCode => renderOldBizdictName(partnerCode, bankOptions)).join(NAME_CONNECTOR_STR),
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: bankOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            mode: 'multiple',
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Settlement Rule')}</span>,
        editable: true,
        dataIndex: 'settlementRule',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: settlementRuleOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
        render: (settlementRule: string): string =>
          renderOldBizdictName(settlementRule, settlementRuleOptions) || EMPTY_PLACEHOLDER,
        width: 240,
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Accumulated to Premium')}</span>,
        editable: true,
        dataIndex: 'accumulatedToPremium',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: yesNoOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
        render: (accumulatedToPremium: string): string =>
          accumulatedToPremium ? renderOldBizdictName(accumulatedToPremium, yesNoOptions) : EMPTY_PLACEHOLDER,
        width: 240,
      },
    ],
    [settlementRuleOptions, t, yesNoOptions, bankOptions]
  );

  const saveSalesPartnerNew = useCallback(
    async (list: SalesAttributesNS.FESalesPartnerAgency[] | null, formulalist: SalesPartnerFormula[] | null) => {
      try {
        let goodsSalesPartnerList = salesPartner?.goodsSalesPartnerList;
        if (list) {
          goodsSalesPartnerList = list;
        }
        const goodsSalesPartnerFormulaList = formulalist
          ? formulalist.map(row => {
              row.goodsId = goodsId;
              row.partnerType = partnerType;

              return row;
            })
          : salesPartner?.goodsSalesPartnerFormulaList;

        await submitService({
          goodsId,
          partnerType,
          goodsSalesPartnerList: goodsSalesPartnerList || [],
          goodsSalesPartnerFormulaList: goodsSalesPartnerFormulaList || [],
        });
        message.success(t('Submit Successfully'));
        reload();
      } catch {
        throw new Error();
      }
    },
    [salesPartner, goodsId, partnerType, reload, submitService, t]
  );

  const saveFormulaList = useCallback(
    (list: SalesPartnerFormula[]) => {
      saveSalesPartnerNew(null, list);
    },
    [saveSalesPartnerNew]
  );

  const handleDelete = (index: number) => {
    const newData = cloneDeep(bankConfigList) as SalesAttributesNS.FESalesPartnerAgency[];
    newData.splice(index, 1);

    saveSalesPartnerNew(newData, null);
  };

  const handleConfirm = (value: SalesAttributesNS.FESalesPartnerAgency, key: number | string) => {
    const newData = cloneDeep(bankConfigList) as SalesAttributesNS.FESalesPartnerAgency[];

    return new Promise<void>(async (resolve, reject) => {
      try {
        if (
          newData.find(row => value?.key !== row?.key && intersection(row.partnerCodes, value.partnerCodes)?.length > 0)
        ) {
          message.error(t('Duplicate Bank. Please change.'));
          reject();
          return;
        }

        value.goodsId = goodsId;
        value.partnerType = partnerType;
        if (key === OptEnum.Add) {
          const addIndex = newData.findIndex(item => item.key === OptEnum.Add);
          newData[addIndex] = value;
        } else {
          const editIndex = newData.findIndex(row => row.key === key);
          newData[editIndex] = value;
        }
        // accumulatedToPremium 所有数据保持一致
        newData.forEach(existRow => {
          existRow.accumulatedToPremium = value.accumulatedToPremium;
        });

        await saveSalesPartnerNew(newData, null);
        onEditStateChange(false);
        resolve();
      } catch (e) {
        reject(e);
      }
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-return
  const commissionFormulaList = useMemo(
    () =>
      ((salesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.Commission &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [salesPartner?.goodsSalesPartnerFormulaList]
  );

  const clawbackcommissionFormulaList = useMemo(
    () =>
      ((salesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.CommissionClawback &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [salesPartner?.goodsSalesPartnerFormulaList]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <EditableTable
        columns={bankColumns}
        dataSource={bankConfigList}
        setDataSource={setBankConfigList}
        loading={loading}
        initializeAddData={{
          partnerCodes: [],
          settlementRule: SettlementRule.basedOnPremiumOffset,
          accumulatedToPremium: MktYesNo.No,
        }}
        addBtnProps={{
          type: 'dashed',
          addTitle: t('Add'),
          disabled,
          handleAdd: () => onEditStateChange(true),
        }}
        editBtnProps={{ disabled: () => disabled, handleEdit: () => onEditStateChange(true) }}
        deleteBtnProps={{ disabled: () => disabled, handleDelete, doubleConfirmPlacement: 'topRight' }}
        handleCancel={() => onEditStateChange(false)}
        scroll={{ x: 'max-content' }}
        handleConfirm={handleConfirm}
        newPosition={NewPosition.Bottom}
        pagination={{
          hideOnSinglePage: true,
          defaultPageSize: 10,
        }}
      />
      <CommissionFormulaTable
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={commissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...formulaList, ...clawbackcommissionFormulaList]);
        }}
      />
      <ClawbackCommissionFormula
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={clawbackcommissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...commissionFormulaList, ...formulaList]);
        }}
      />
    </div>
  );
};

export default forwardRef(BankConfigSection);
