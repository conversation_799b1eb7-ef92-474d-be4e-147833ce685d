import { Ref, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Tooltip, message } from 'antd';

import { cloneDeep } from 'lodash-es';

import { EditableTable, FieldType, NewPosition } from '@zhongan/nagrand-ui';

import { FormulaProps, PartnerFeeType, SalesPartnerFormula } from 'genesis-web-service';

import { EMPTY_PLACEHOLDER } from '@market/common/constant';
import { BizTopic, CommissionSubCategory } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import { useBizDict } from '@market/hook/bizDict';
import { useCommissionFormulaOptions } from '@market/hook/goods.service';
import { OptEnum } from '@market/request/interface';
import { mapFormulaToOldBizdict, renderEnumName, renderOldBizdictName } from '@market/utils/enum';

interface Props {
  goodsId: string | number;
  disabled: boolean;
  totalFormulaList: FormulaProps[];
  refInstance?: Ref<{
    getCommissionFormulaList: () => SalesPartnerFormula[];
  }>;
  initData?: SalesPartnerFormula[];
  onEditStateChange: (isEditing: boolean) => void;
  saveFormulaList: (list: SalesPartnerFormula[]) => void;
}

export const ClawbackCommissionFormula = ({
  goodsId,
  disabled,
  totalFormulaList,
  refInstance,
  initData,
  onEditStateChange,
  saveFormulaList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const bizTopicBizDicts = useBizDict('bizTopic');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef(null);
  const [t] = useTranslation(['market', 'common']);
  const [commissionForm] = Form.useForm();
  const [tableData, setTableData] = useState<SalesAttributesNS.FESalesPartnerFormula[]>([]);
  const formulaCategory = BizTopic.CommissionClawback;

  const editFormulaSubCategory = Form.useWatch('formulaSubCategory', commissionForm);

  const formulaSubCategoryOptions = useMemo(
    () =>
      bizTopicBizDicts
        ?.find(bizdict => bizdict.dictValue === BizTopic.CommissionClawback)
        ?.childList?.map(bizdict => ({
          itemExtend1: bizdict.dictValue,
          itemName: bizdict.dictValueName,
        })) || [],
    [bizTopicBizDicts]
  );

  const formulaOptionsBySubCategory = useCommissionFormulaOptions(
    totalFormulaList,
    BizTopic.CommissionClawback,
    editFormulaSubCategory
  );

  const totalCommissionFormulaOptions = useMemo(
    () =>
      totalFormulaList
        .filter(formula => `${formula.formulaTableTypeId}` === BizTopic.CommissionClawback)
        .map(mapFormulaToOldBizdict),
    [totalFormulaList]
  );

  useEffect(() => {
    if (initData) {
      setTableData(
        initData.map(formula => {
          const key = Math.random();
          return {
            ...formula,
            formulaSubCategory: `${formula?.formulaSubCategory || ''}`,
            key,
            id: key,
          };
        })
      );
    }
  }, [initData]);

  useImperativeHandle(refInstance, () => ({
    getCommissionFormulaList: () =>
      tableData.map(
        row =>
          ({
            formulaCode: row.formulaCode,
            formulaCategory,
            formulaSubCategory: row?.formulaSubCategory,
            feeType: PartnerFeeType.commission,
            goodsId,
          }) as SalesPartnerFormula
      ),
  }));

  const renderClawbackFormulaLabel = useCallback(
    () => (
      <span>
        {t('Formula Name (Clawback)')}
        <Tooltip
          placement="top"
          title={t(
            'Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.'
          )}
        >
          <QuestionCircleOutlined style={{ marginLeft: '5px' }} />
        </Tooltip>
      </span>
    ),
    [t]
  );

  const columns = useMemo(
    () => [
      {
        title: t('Formula Category (Clawback)'),
        dataIndex: 'formulaCategory',
        editable: false,
        width: '220px',
        render: () =>
          renderEnumName(formulaCategory, bizTopicBizDicts, {
            labelKey: 'dictValueName',
            valueKey: 'dictValue',
          }),
      },
      {
        title: t('Formula Sub Category (Clawback)'),
        editable: true,
        dataIndex: 'formulaSubCategory',
        width: '240px',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: formulaSubCategoryOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        render: (formulaSubCategory = CommissionSubCategory.GrossCommission): string =>
          renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions) || EMPTY_PLACEHOLDER,
      },
      {
        title: renderClawbackFormulaLabel(),
        editable: true,
        dataIndex: 'formulaCode',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: formulaOptionsBySubCategory?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
            disabled: !editFormulaSubCategory,
          },
        },
        width: '240px',
        render: (text: string): string =>
          renderOldBizdictName(text, totalCommissionFormulaOptions) || EMPTY_PLACEHOLDER,
      },
    ],
    [
      t,
      formulaSubCategoryOptions,
      renderClawbackFormulaLabel,
      formulaOptionsBySubCategory,
      editFormulaSubCategory,
      formulaCategory,
      bizTopicBizDicts,
      totalCommissionFormulaOptions,
    ]
  );

  const handleDelete = (index: number) => {
    const newData = cloneDeep(tableData || []);
    newData.splice(index, 1);

    setTableData([...newData]);
    saveFormulaList([...newData]);
  };

  const handleConfirm = (value: SalesAttributesNS.FESalesPartnerFormula, key: number | string) => {
    const newData = cloneDeep(tableData || []);

    return new Promise<void>((resolve, reject) => {
      try {
        // FormulaCategory + FormulaSubCategory确定唯一性
        if (
          newData.find(
            row =>
              value.key !== row.key &&
              row.formulaSubCategory &&
              `${row.formulaSubCategory}` === `${value.formulaSubCategory || ''}`
          )
        ) {
          message.error(
            t('Duplicate formula configuration for same formula category and sub category. Please edit original one.')
          );
          reject();
          return;
        }
        value.editing = false;
        value.feeType = PartnerFeeType.commission;
        value.formulaCategory = BizTopic.CommissionClawback;

        if (key === OptEnum.Add) {
          const addIndex = newData.findIndex(item => item.key === OptEnum.Add);
          newData[addIndex] = value;
        } else {
          const editIndex = newData.findIndex(row => row.key === key);
          newData[editIndex] = value;
        }

        saveFormulaList(newData);
        onEditStateChange(false);

        resolve();
      } catch (e) {
        reject();
      }
    });
  };

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <div style={{ marginBottom: 12 }}>{t('Please select related formula for Commission Clawback')}</div>
      <EditableTable
        columns={columns}
        dataSource={tableData}
        setDataSource={setTableData}
        outForm={commissionForm}
        addBtnProps={{
          type: 'dashed',
          addTitle: t('Add'),
          disabled,
          handleAdd: () => onEditStateChange(true),
        }}
        editBtnProps={{ disabled: () => disabled, handleEdit: () => onEditStateChange(true) }}
        deleteBtnProps={{ disabled: () => disabled, handleDelete, tooltipPlacement: 'topRight' }}
        handleCancel={() => onEditStateChange(false)}
        scroll={{ x: 'max-content' }}
        handleConfirm={handleConfirm}
        newPosition={NewPosition.Bottom}
        pagination={{
          hideOnSinglePage: true,
          defaultPageSize: 10,
        }}
      />
    </div>
  );
};

export default forwardRef(ClawbackCommissionFormula);
