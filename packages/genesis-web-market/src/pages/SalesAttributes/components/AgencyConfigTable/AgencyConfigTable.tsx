import { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Tooltip, message } from 'antd';

import { useRequest } from 'ahooks';
import { cloneDeep, intersection } from 'lodash-es';

import { EditableTable, FieldType, NewPosition } from '@zhongan/nagrand-ui';

import {
  FormulaProps,
  GoodsSalesPartner,
  MktYesNo,
  PartnerFeeType,
  PartnerType,
  SalesPartner,
  SalesPartnerFormula,
  SettlementRule,
} from 'genesis-web-service';

import { EMPTY_PLACEHOLDER, NAME_CONNECTOR_STR } from '@market/common/constant';
import { BizTopic } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import { useBizDict } from '@market/hook/bizDict';
import { useAgencyList } from '@market/hook/channel.serivce';
import { queryTotalChannelList } from '@market/marketService/channel.service';
import { OptEnum } from '@market/request/interface';
import { mapBizdictToOldBizdict, mapChannelToOldBizdict, renderOldBizdictName } from '@market/utils/enum';

import ClawbackCommissionFormula from '../ClawbackCommissionFormula';
import CommissionFormulaTable from '../CommissionFormulaTable';

interface Props {
  goodsId: number | string;
  disabled: boolean;
  loading: boolean;
  totalFormulaList: FormulaProps[];
  agencySalesPartner?: SalesPartner;
  onEditStateChange: (isEditing: boolean) => void;
  submitService: (salesPartner: SalesPartner) => Promise<unknown>;
  reload: () => void;
}

export const AgencyConfigTable = ({
  goodsId,
  disabled,
  loading,
  agencySalesPartner,
  totalFormulaList,
  onEditStateChange,
  submitService,
  reload,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const settlementRuleBizDicts = useBizDict('settlementRule');
  const yesNoOptions = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const totalAgencyList = useAgencyList();
  const partnerType = PartnerType.agencyCompany;
  const [agencyPartnerForm] = Form.useForm();
  const [agencyPartnerList, setAgencyPartnerList] = useState<Record<string, any>[]>([]);

  useEffect(() => {
    const newAgencyPartnerList = agencySalesPartner?.goodsSalesPartnerList
      .filter(item => item.partnerType === PartnerType.agencyCompany)
      .map(item => ({
        key: Math.random(),
        ...item,
        accumulatedToPremium: `${item.accumulatedToPremium!}`,
        settlementRule: `${item.settlementRule!}`,
      }));
    setAgencyPartnerList(newAgencyPartnerList || []);
  }, [agencySalesPartner?.goodsSalesPartnerList]);

  const { data: totalChannelOptions = [] } = useRequest(() =>
    queryTotalChannelList().then(res => res.map(mapChannelToOldBizdict))
  );

  const totalAgencyOptions = useMemo(() => totalAgencyList.map(mapChannelToOldBizdict), [totalAgencyList]);
  const settlementRuleOptions = useMemo(
    () => settlementRuleBizDicts.map(mapBizdictToOldBizdict),
    [settlementRuleBizDicts]
  );

  const salesPartnerColumns = useMemo(
    () => [
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Company Name')}</span>,
        dataIndex: 'partnerCodes',
        editable: true,
        width: 240,
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: totalAgencyOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            mode: 'multiple',
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
        render: (partnerCodes: string[]): string =>
          partnerCodes
            ?.map(partnerCode => renderOldBizdictName(partnerCode, totalAgencyOptions))
            .join(NAME_CONNECTOR_STR),
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Sales Platform')}</span>,
        editable: true,
        dataIndex: 'relatedChannelCodeList',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: totalChannelOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            mode: 'multiple',
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        render: (relatedChannelCodes: string[]): JSX.Element => {
          const codeText = relatedChannelCodes
            ?.map(code => renderOldBizdictName(code, totalChannelOptions))
            .join(NAME_CONNECTOR_STR);
          return (
            <Tooltip title={codeText}>
              <span
                style={{
                  display: 'inline-block',
                  maxWidth: 240,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {codeText || EMPTY_PLACEHOLDER}
              </span>
            </Tooltip>
          );
        },
        width: '240px',
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Settlement Rule')}</span>,
        editable: true,
        dataIndex: 'settlementRule',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: settlementRuleOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        render: (settlementRule: string): string => renderOldBizdictName(settlementRule, settlementRuleOptions),
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
        width: '240px',
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Accumulated to Premium')}</span>,
        editable: true,
        dataIndex: 'accumulatedToPremium',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: yesNoOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        render: (accumulatedToPremium: string): string =>
          accumulatedToPremium ? renderOldBizdictName(accumulatedToPremium, yesNoOptions) : t('- -'),
        width: '240px',
        formItemProps: { rules: [{ required: true, message: t('Please select') }] },
      },
    ],
    [settlementRuleOptions, t, totalAgencyOptions, totalChannelOptions, yesNoOptions]
  );

  const saveSalesPartnerNew = useCallback(
    async (list: SalesAttributesNS.FESalesPartnerAgency[] | null, formulalist: SalesPartnerFormula[] | null) => {
      // 如果没传取原始数据
      let goodsSalesPartnerList = agencySalesPartner?.goodsSalesPartnerList;
      if (list) {
        goodsSalesPartnerList = list;
      }
      try {
        const goodsSalesPartnerFormulaList = formulalist
          ? formulalist.map(row => {
              row.goodsId = goodsId;
              row.partnerType = partnerType;

              return row;
            })
          : agencySalesPartner?.goodsSalesPartnerFormulaList;

        await submitService({
          goodsId,
          partnerType,
          goodsSalesPartnerList: goodsSalesPartnerList || [],
          goodsSalesPartnerFormulaList: goodsSalesPartnerFormulaList || [],
        });
        message.success(t('Submit Successfully'));
        reload();
      } catch {
        throw new Error();
      }
    },
    [agencySalesPartner, goodsId, partnerType, reload, submitService, t]
  );

  const saveFormulaList = useCallback(
    (list: SalesPartnerFormula[]) => {
      saveSalesPartnerNew(null, list);
    },
    [saveSalesPartnerNew]
  );

  // 把RelatedChannelCodeList里面的数据拆出来
  const fillRelatedChannelCodeList = useCallback(
    (tempData: SalesAttributesNS.FESalesPartnerAgency[]) => {
      const tempList: GoodsSalesPartner[] = [];
      // 补充关联的channel信息
      tempData?.forEach(row => {
        tempList.push(row);
        row.relatedChannelCodeList?.forEach(channelCode => {
          tempList.push({
            key: Math.random(),
            partnerType: PartnerType.salesChannel,
            partnerCode: channelCode,
            partnerCodes: [channelCode],
            goodsId,
          });
        });
      });

      return tempList;
    },
    [goodsId]
  );

  const handleConfirm = (value: SalesAttributesNS.FESalesPartnerAgency, key: number | string) => {
    const newData = cloneDeep(agencyPartnerList) as SalesAttributesNS.FESalesPartnerAgency[];

    return new Promise<void>(async (resolve, reject) => {
      try {
        await agencyPartnerForm.validateFields();

        if (
          newData.find(row => value?.key !== row?.key && intersection(row.partnerCodes, value.partnerCodes)?.length > 0)
        ) {
          message.error(t('Duplicate Sales Platform. Please change.'));
          reject();
          return;
        }

        value.goodsId = goodsId;
        value.partnerType = partnerType;
        if (key === OptEnum.Add) {
          const addIndex = newData.findIndex(item => item.key === OptEnum.Add);
          newData[addIndex] = value;
        } else {
          const editIndex = newData.findIndex(row => row.key === key);
          newData[editIndex] = value;
        }

        // accumulatedToPremium 所有数据保持一致
        newData.forEach(existRow => {
          existRow.accumulatedToPremium = value.accumulatedToPremium;
        });

        const tempList: GoodsSalesPartner[] = fillRelatedChannelCodeList(newData);

        await saveSalesPartnerNew([...tempList], null);
        onEditStateChange(false);
        resolve();
      } catch (e) {
        reject(e);
      }
    });
  };

  const handleDelete = (index: number) => {
    const newData = cloneDeep(agencyPartnerList) as SalesAttributesNS.FESalesPartnerAgency[];
    newData.splice(index, 1);

    const tempList: GoodsSalesPartner[] = fillRelatedChannelCodeList(newData);

    saveSalesPartnerNew([...tempList], null);
  };

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-return
  const commissionFormulaList = useMemo(
    () =>
      ((agencySalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.Commission &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [agencySalesPartner?.goodsSalesPartnerFormulaList]
  );

  const clawbackcommissionFormulaList = useMemo(
    () =>
      ((agencySalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.CommissionClawback &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [agencySalesPartner?.goodsSalesPartnerFormulaList]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <EditableTable
        columns={salesPartnerColumns}
        dataSource={agencyPartnerList}
        setDataSource={setAgencyPartnerList}
        outForm={agencyPartnerForm}
        loading={loading}
        initializeAddData={{
          partnerCodes: undefined,
          relatedChannelCodeList: undefined,
          settlementRule: SettlementRule.basedOnPremiumOffset,
          accumulatedToPremium: MktYesNo.No,
        }}
        addBtnProps={{
          type: 'dashed',
          addTitle: t('Add'),
          disabled,
          handleAdd: () => onEditStateChange(true),
        }}
        editBtnProps={{ disabled: () => disabled, handleEdit: () => onEditStateChange(true) }}
        deleteBtnProps={{ disabled: () => disabled, handleDelete, tooltipPlacement: 'topRight' }}
        handleCancel={() => onEditStateChange(false)}
        scroll={{ x: 'max-content' }}
        handleConfirm={handleConfirm}
        newPosition={NewPosition.Bottom}
        pagination={{
          hideOnSinglePage: true,
          defaultPageSize: 10,
        }}
      />
      <CommissionFormulaTable
        goodsId={goodsId}
        // refInstance={commissionFormulaRef}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={commissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...formulaList, ...clawbackcommissionFormulaList]);
        }}
      />
      <ClawbackCommissionFormula
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={clawbackcommissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...commissionFormulaList, ...formulaList]);
        }}
      />
    </div>
  );
};

export default forwardRef(AgencyConfigTable);
