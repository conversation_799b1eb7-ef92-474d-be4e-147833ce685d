import { useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { Tabs, Tooltip, message } from 'antd';

import { useRequest } from 'ahooks';
import classNames from 'classnames/bind';
import { keyBy } from 'lodash-es';

import {
  FormulaProps,
  PartnerType,
  SalesPartner,
  SalesPartnerServiceCompany,
  ServiceFeeFormula,
} from 'genesis-web-service';
import { SaveOrUpdateGoodsSalesPartnerRequest } from 'genesis-web-service/service-types/market-types/package';

import UncheckedSvg from '@market/asset/svg/check.svg';
import CheckedSvg from '@market/asset/svg/checkSucc.svg';
import { DetailPageMode } from '@market/common/interface';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useTiedAgentChannelList } from '@market/hook/channel.serivce';
import { useActiveStatus } from '@market/hook/utils';
import { NewMarketService } from '@market/services/market/market.service.new';

import AgencyConfigTable from '../AgencyConfigTable';
import AgentConfigTable from '../AgentConfigTable';
import BankConfigSection from '../BankConfigSection';
import BrokerCompanyConfigSection from '../BrokerCompanyConfigSection';
import ChannelConfigTable from '../ChannelConfigTable';
import FrontLineChannel from '../FrontLineChannel/FrontLineChannel';
import KeyAccountChannel from '../KeyAccountChannel/KeyAccountChannel';
import LeasingChannelConfigSection from '../LeasingChannelConfigSection';
import ServiceCompanyConfigTable from '../ServiceCompanyConfigTable';
import styles from './PartnerFeeCalculation.module.scss';

const cx = classNames.bind(styles);

enum PartnerFeeConfigTable {
  agency = 1,
  channel,
  serviceComapny,
  commissionFormula,
  clawbackFormula,
}

enum SalesChannelType {
  Insurance = 1,
  SALES_PLATFORM = 2,
  AGENCY_COMPANY = 3,
  Service_Company = 4,
  AGENT = 6,
  BANK = 7,
  LEASE_CHANNEL = 8,
  BROKER_COMPANY = 9,
  FRONT_LINE_CHANNEL = 14,
  KEY_ACCOUNT_CHANNEL = 15,
}

interface Props {
  goodsId: string;
  mode: DetailPageMode;
  totalFormulaList: FormulaProps[];
  onSubmit: () => void;
  initData: SalesPartner[];
}

export const PartnerFeeCalculation = ({
  goodsId,
  mode,
  totalFormulaList,
  onSubmit: querySalesPartner,
  initData: newSalesPartnerList,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const salesChannelTypeOptions = useBizDictAsOptions('channelType');
  /* ============== 枚举使用end ============== */
  const salesChannelTypeMap = useMemo(() => keyBy(salesChannelTypeOptions, 'value'), [salesChannelTypeOptions]);

  const tiedAgentChannelList = useTiedAgentChannelList();
  const tiedAgentChannelCodeList = useMemo(
    () => (tiedAgentChannelList || []).map(item => item.code) || [],
    [tiedAgentChannelList]
  );
  // tied Agent channel 必须有且只有一条
  const disableTiedAgent = tiedAgentChannelCodeList.length !== 1;

  const configuredPartnerTypes = useMemo(
    () => newSalesPartnerList.map(partner => partner.partnerType),
    [newSalesPartnerList]
  );

  const serviceComapnyConfig = newSalesPartnerList?.find(item => item.partnerType === PartnerType.serviceComapny);

  // 多个表格之间存在联动关系，当编辑一个表格时候需要disable其他表格
  const { isDisable: isTableDisable, setCurrent: setCurrentEditTable, hasActive } = useActiveStatus();

  const { runAsync: submitService, loading: submitLoading } = useRequest(
    (salesPartner: SaveOrUpdateGoodsSalesPartnerRequest) =>
      NewMarketService.GoodsSalesPartnerMgmtService.updateSalesPartner(salesPartner),
    {
      manual: true,
    }
  );

  const serviceCompanyRef = useRef<{
    getServiceCompanyPartnerList: () => SalesPartnerServiceCompany[];
    getServiceFeeFormulaList: () => ServiceFeeFormula[];
    getServiceFeeClawbackFormulaList: () => ServiceFeeFormula[];
  }>(null);

  const disabled = mode === DetailPageMode.view;

  const onEditStateChange = useCallback(
    (table: PartnerFeeConfigTable) => (isEditing: boolean) => {
      if (isEditing) {
        setCurrentEditTable(table);
      } else {
        setCurrentEditTable(undefined);
      }
    },
    [setCurrentEditTable]
  );

  const renderTabsLabel = useCallback(
    (label: string, partnerType: PartnerType) => (
      <div className={styles.tabWrapper}>
        {label}
        {configuredPartnerTypes.includes(partnerType) ? <CheckedSvg /> : <UncheckedSvg />}
      </div>
    ),
    [configuredPartnerTypes]
  );

  const items = useMemo(() => {
    const defaultItems: {
      label: JSX.Element;
      key: string;
      children: JSX.Element;
      disabled?: boolean;
    }[] = [];
    if (salesChannelTypeMap[SalesChannelType.AGENCY_COMPANY]) {
      defaultItems.push({
        label: renderTabsLabel(t('Agency Company'), PartnerType.agencyCompany),
        key: `${PartnerType.agencyCompany}`,
        children: (
          <AgencyConfigTable
            key={PartnerType.agencyCompany}
            disabled={disabled || submitLoading || isTableDisable(PartnerFeeConfigTable.agency)}
            loading={submitLoading}
            totalFormulaList={totalFormulaList}
            agencySalesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.agencyCompany)}
            onEditStateChange={onEditStateChange(PartnerFeeConfigTable.agency)}
            goodsId={goodsId}
            submitService={submitService}
            reload={querySalesPartner}
          />
        ),
      });
    }

    if (salesChannelTypeMap[SalesChannelType.SALES_PLATFORM]) {
      defaultItems.push({
        label: renderTabsLabel(t('Sales Platform'), PartnerType.salesChannel),
        key: `${PartnerType.salesChannel}`,
        children: (
          <ChannelConfigTable
            key={PartnerType.salesChannel}
            disabled={disabled || submitLoading || isTableDisable(PartnerFeeConfigTable.channel)}
            loading={submitLoading}
            onEditStateChange={onEditStateChange(PartnerFeeConfigTable.channel)}
            platformSalesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.salesChannel)}
            totalFormulaList={totalFormulaList}
            goodsId={goodsId}
            submitService={submitService}
            reload={querySalesPartner}
          />
        ),
      });
    }

    if (salesChannelTypeMap[SalesChannelType.AGENT]) {
      defaultItems.push({
        label: disableTiedAgent ? (
          <Tooltip title={t('Sales Channel is empty or multiple sales channels exist.')}>
            {renderTabsLabel(t('Tied Agent'), PartnerType.agent)}
          </Tooltip>
        ) : (
          renderTabsLabel(t('Tied Agent'), PartnerType.agent)
        ),
        key: `${PartnerType.agent}`,
        children: (
          <AgentConfigTable
            key={PartnerType.agent}
            disabled={disabled || submitLoading || isTableDisable(PartnerFeeConfigTable.channel)}
            submitLoading={submitLoading}
            totalFormulaList={totalFormulaList}
            tiedAgentChannelCodeList={tiedAgentChannelCodeList}
            submitService={submitService}
            reload={querySalesPartner}
            agentSalesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.agent)}
            goodsId={goodsId}
          />
        ),
        disabled: disableTiedAgent,
      });
    }

    if (salesChannelTypeMap[SalesChannelType.LEASE_CHANNEL]) {
      defaultItems.push({
        label: renderTabsLabel(t('Leasing Channel'), PartnerType.leaseChannel),
        key: `${PartnerType.leaseChannel}`,
        children: (
          <LeasingChannelConfigSection
            key={PartnerType.leaseChannel}
            disabled={disabled || submitLoading}
            loading={submitLoading}
            totalFormulaList={totalFormulaList}
            salesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.leaseChannel)}
            onEditStateChange={() => {}}
            goodsId={goodsId}
            submitService={submitService}
            reload={querySalesPartner}
          />
        ),
      });
    }

    if (salesChannelTypeMap[SalesChannelType.BANK]) {
      defaultItems.push({
        label: renderTabsLabel(t('Bank'), PartnerType.bank),
        key: `${PartnerType.bank}`,
        children: (
          <BankConfigSection
            key={PartnerType.bank}
            disabled={disabled || submitLoading}
            loading={submitLoading}
            totalFormulaList={totalFormulaList}
            salesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.bank)}
            onEditStateChange={() => {}}
            goodsId={goodsId}
            submitService={submitService}
            reload={querySalesPartner}
          />
        ),
      });
    }

    if (salesChannelTypeMap[SalesChannelType.BROKER_COMPANY]) {
      defaultItems.push({
        label: renderTabsLabel(t('Broker Company'), PartnerType.brokerCompany),
        key: `${PartnerType.brokerCompany}`,
        children: (
          <BrokerCompanyConfigSection
            key={PartnerType.brokerCompany}
            disabled={disabled || submitLoading}
            loading={submitLoading}
            totalFormulaList={totalFormulaList}
            salesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.brokerCompany)}
            onEditStateChange={() => {}}
            goodsId={goodsId}
            submitService={submitService}
            reload={querySalesPartner}
          />
        ),
      });
    }

    if (salesChannelTypeMap[SalesChannelType.FRONT_LINE_CHANNEL]) {
      defaultItems.push({
        label: renderTabsLabel(t('Front Line Channel'), PartnerType.frontLineChannel),
        key: `${PartnerType.frontLineChannel}`,
        children: (
          <FrontLineChannel
            key={PartnerType.frontLineChannel}
            disabled={disabled || submitLoading}
            loading={submitLoading}
            totalFormulaList={totalFormulaList}
            salesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.frontLineChannel)}
            onEditStateChange={() => {}}
            goodsId={goodsId}
            submitService={submitService}
            reload={querySalesPartner}
          />
        ),
      });
    }

    if (salesChannelTypeMap[SalesChannelType.KEY_ACCOUNT_CHANNEL]) {
      defaultItems.push({
        label: renderTabsLabel(t('Key Account Channel'), PartnerType.keyAccountChannel),
        key: `${PartnerType.keyAccountChannel}`,
        children: (
          <KeyAccountChannel
            key={PartnerType.keyAccountChannel}
            disabled={disabled || submitLoading}
            loading={submitLoading}
            totalFormulaList={totalFormulaList}
            salesPartner={newSalesPartnerList?.find(item => item.partnerType === PartnerType.keyAccountChannel)}
            onEditStateChange={() => {}}
            goodsId={goodsId}
            submitService={submitService}
            reload={querySalesPartner}
          />
        ),
      });
    }

    return defaultItems;
  }, [salesChannelTypeMap, renderTabsLabel, onEditStateChange, querySalesPartner, submitService, newSalesPartnerList]);

  return (
    <div className={cx('partner_fee_calculation')}>
      <div style={{ marginBottom: 24, fontWeight: 'bold', fontSize: 16 }}>{t('Partner Fee Calculation')}</div>
      <div
        style={{
          marginBottom: 24,
          fontWeight: 'bold',
          fontSize: 14,
          color: styles.textColorSecondary,
        }}
      >
        {t('Agency Company or Sales Platform')}
      </div>
      <Tabs
        className={cx('custom-tabs')}
        defaultActiveKey={`${newSalesPartnerList[0]?.partnerType || PartnerType.agencyCompany}`}
        type="card"
        items={items}
      />
      <ServiceCompanyConfigTable
        goodsId={goodsId}
        disabled={disabled || submitLoading || isTableDisable(PartnerFeeConfigTable.serviceComapny)}
        refInstance={serviceCompanyRef}
        totalFormulaList={totalFormulaList}
        initData={(serviceComapnyConfig?.goodsSalesPartnerList as SalesPartnerServiceCompany[]) || []}
        initServiceFeeFormulaList={(serviceComapnyConfig?.goodsSalesPartnerFormulaList as ServiceFeeFormula[]) || []}
        onEditStateChange={onEditStateChange(PartnerFeeConfigTable.serviceComapny)}
        saveSalesPartner={() => {
          submitService({
            goodsId,
            partnerType: PartnerType.serviceComapny,
            goodsSalesPartnerList: serviceCompanyRef.current?.getServiceCompanyPartnerList() || [],
            goodsSalesPartnerFormulaList: [
              ...(serviceCompanyRef.current?.getServiceFeeFormulaList() || []),
              ...(serviceCompanyRef.current?.getServiceFeeClawbackFormulaList() || []),
            ],
          }).then(() => {
            message.success(t('Submit Successfully'));
            querySalesPartner();
          });
        }}
      />
    </div>
  );
};

export default PartnerFeeCalculation;
