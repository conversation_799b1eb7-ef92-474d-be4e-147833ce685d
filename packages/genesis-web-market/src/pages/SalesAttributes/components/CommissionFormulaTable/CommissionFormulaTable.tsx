import { Ref, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Popover, message } from 'antd';

import { cloneDeep } from 'lodash-es';

import { EditableTable, FieldType, NewPosition, Table } from '@zhongan/nagrand-ui';

import { FormulaProps, PartnerFeeType, SalesPartnerFormula } from 'genesis-web-service';

import { EMPTY_PLACEHOLDER } from '@market/common/constant';
import { BizTopic, CommissionSubCategory } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import { useBizDict } from '@market/hook/bizDict';
import { useCommissionFormulaOptions } from '@market/hook/goods.service';
import { OptEnum } from '@market/request/interface';
import { mapFormulaToOldBizdict, renderEnumName, renderOldBizdictName, renderOptionName } from '@market/utils/enum';

interface Props {
  goodsId: string | number;
  disabled: boolean;
  totalFormulaList: FormulaProps[];
  refInstance?: Ref<{
    getCommissionFormulaList: () => SalesPartnerFormula[];
  }>;
  initData?: SalesPartnerFormula[];
  onEditStateChange: (isEditing: boolean) => void;
  saveFormulaList: (list: SalesPartnerFormula[]) => void;
}

export const CommissionFormulaTable = ({
  goodsId,
  disabled,
  totalFormulaList,
  refInstance,
  initData,
  onEditStateChange,
  saveFormulaList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const bizTopicBizDicts = useBizDict('bizTopic');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const [formulaForm] = Form.useForm();

  const [tableData, setTableData] = useState<SalesAttributesNS.FESalesPartnerFormula[]>([]);
  const formulaCategory = BizTopic.Commission;

  const editFormulaSubCategory = Form.useWatch('formulaSubCategory', formulaForm);

  const formulaSubCategoryOptions = useMemo(
    () =>
      bizTopicBizDicts
        ?.find(bizdict => bizdict.dictValue === BizTopic.Commission)
        ?.childList?.map(bizdict => ({
          itemExtend1: bizdict.dictValue,
          itemName: bizdict.dictValueName,
        })) || [],
    [bizTopicBizDicts]
  );

  const formulaOptionsBySubCategory = useCommissionFormulaOptions(
    totalFormulaList,
    BizTopic.Commission,
    editFormulaSubCategory
  );

  const totalCommissionFormulaOptions = useMemo(
    () =>
      totalFormulaList
        .filter(formula => `${formula.formulaTableTypeId}` === BizTopic.Commission)
        .map(mapFormulaToOldBizdict),
    [totalFormulaList]
  );

  useEffect(() => {
    if (initData) {
      setTableData(
        initData.map(formula => {
          const key = Math.random();
          return {
            ...formula,
            formulaSubCategory: `${formula?.formulaSubCategory || ''}`,
            key,
            id: key,
          };
        })
      );
    }
  }, [initData]);

  useImperativeHandle(refInstance, () => ({
    getCommissionFormulaList: () =>
      tableData.map(
        row =>
          ({
            formulaCode: row.formulaCode,
            formulaOrder: row.formulaOrder,
            formulaCategory,
            formulaSubCategory: row?.formulaSubCategory,
            feeType: PartnerFeeType.commission,
            goodsId,
          }) as SalesPartnerFormula
      ),
  }));

  const renderOrderTooltip = useCallback(
    () => (
      <div>
        <p style={{ marginBottom: 6 }}>
          {t('If Commission GST is configured based on NetCommission, then we could configure as')}
        </p>
        <Table
          columns={[
            {
              title: t('Formula Category'),
              dataIndex: 'formulaType',
            },
            {
              title: t('Formula Sub Category'),
              dataIndex: 'formulaSubCategory',
            },
            {
              title: t('Formula Code'),
              dataIndex: 'formulaCode',
            },
            {
              title: t('Order'),
              dataIndex: 'formulaOrder',
            },
          ]}
          dataSource={[
            {
              formulaType: 'Service Fee & Commission',
              formulaCode: 'commNet',
              formulaOrder: 1,
              formulaSubCategory: 'Net Commission',
            },
            {
              formulaType: 'Service Fee & Commission',
              formulaCode: 'commGST',
              formulaOrder: 2,
              formulaSubCategory: 'Commission GST',
            },
          ]}
          pagination={false}
        />
        <p style={{ marginTop: 6 }}>
          {t(
            'and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula'
          )}
        </p>
      </div>
    ),
    [t]
  );

  const columns = useMemo(
    () => [
      {
        title: t('Formula Category'),
        dataIndex: 'formulaCategory',
        editable: false,
        width: '220px',
        render: () =>
          renderEnumName(formulaCategory, bizTopicBizDicts, {
            labelKey: 'dictValueName',
            valueKey: 'dictValue',
          }),
      },
      {
        title: t('Formula Sub Category'),
        editable: true,
        dataIndex: 'formulaSubCategory',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: formulaSubCategoryOptions?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
          },
        },
        formItemProps: { required: true },
        width: '240px',
        render: (formulaSubCategory = CommissionSubCategory.GrossCommission): string =>
          renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions),
      },
      {
        title: t('Formula Name'),
        editable: true,
        dataIndex: 'formulaCode',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: formulaOptionsBySubCategory?.map(item => ({
              value: item.itemExtend1,
              label: item.itemName,
            })),
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => containerEl.current || document.body,
            disabled: !editFormulaSubCategory,
          },
        },
        width: '240px',
        render: (text: string): string =>
          renderOldBizdictName(text, totalCommissionFormulaOptions) || EMPTY_PLACEHOLDER,
      },
      {
        title: (
          <span>
            {t('Order')}
            <Popover placement="topRight" content={renderOrderTooltip()} getPopupContainer={() => document.body}>
              <QuestionCircleOutlined style={{ marginLeft: '5px' }} />
            </Popover>
          </span>
        ),
        editable: true,
        fieldProps: {
          type: FieldType.InputNumber,
          extraProps: {
            style: { width: 160 },
            min: 1,
            precision: 0,
            placeholder: t('Please input'),
          },
        },
        formItemProps: { required: true },
        dataIndex: 'formulaOrder',
        inputType: 'number',
        width: '100px',
        render: (text: string) => text || EMPTY_PLACEHOLDER,
      },
    ],
    [
      t,
      formulaSubCategoryOptions,
      formulaOptionsBySubCategory,
      editFormulaSubCategory,
      renderOrderTooltip,
      formulaCategory,
      bizTopicBizDicts,
      totalCommissionFormulaOptions,
    ]
  );

  const handleConfirm = (value: SalesAttributesNS.FESalesPartnerFormula, key: number | string) => {
    const newData = cloneDeep(tableData || []);

    return new Promise<void>((resolve, reject) => {
      try {
        // Order 不可以填写相同的数字
        if (newData.find(row => value.key !== row.key && row.formulaOrder === value.formulaOrder)) {
          message.error(t('Duplicate Order. Please change.'));
          reject();
          return;
        }
        // FormulaCategory + FormulaSubCategory确定唯一性
        if (
          newData.find(
            row =>
              value.key !== row.key &&
              row.formulaSubCategory &&
              `${row.formulaSubCategory}` === `${value.formulaSubCategory || ''}`
          )
        ) {
          message.error(
            t('Duplicate formula configuration for same formula category and sub category. Please edit original one.')
          );
          reject();
          return;
        }
        value.editing = false;
        value.feeType = PartnerFeeType.commission;
        value.formulaCategory = BizTopic.Commission;

        if (key === OptEnum.Add) {
          const addIndex = newData.findIndex(item => item.key === OptEnum.Add);
          newData[addIndex] = value;
        } else {
          const editIndex = newData.findIndex(row => row.key === key);
          newData[editIndex] = value;
        }

        saveFormulaList(newData);
        onEditStateChange(false);

        resolve();
      } catch (e) {
        reject();
      }
    });
  };

  const handleDelete = (index: number) => {
    const newData = cloneDeep(tableData || []);
    newData.splice(index, 1);

    setTableData([...newData]);
    saveFormulaList([...newData]);
  };

  return (
    <div
      style={{
        marginBottom: 24,
        marginTop: 24,
      }}
      ref={containerEl}
    >
      <div style={{ marginBottom: 12 }}>{t('Please select related formula for Commission')}</div>
      <EditableTable
        columns={columns}
        dataSource={tableData}
        setDataSource={setTableData}
        outForm={formulaForm}
        addBtnProps={{
          type: 'dashed',
          addTitle: t('Add'),
          disabled,
          handleAdd: () => onEditStateChange(true),
        }}
        newPosition={NewPosition.Bottom}
        editBtnProps={{ disabled: () => disabled, handleEdit: () => onEditStateChange(true) }}
        deleteBtnProps={{ disabled: () => disabled, handleDelete, tooltipPlacement: 'topRight' }}
        handleCancel={() => onEditStateChange(false)}
        scroll={{ x: 'max-content' }}
        handleConfirm={handleConfirm}
        pagination={{
          hideOnSinglePage: true,
          defaultPageSize: 10,
        }}
      />
    </div>
  );
};

export default forwardRef(CommissionFormulaTable);
