/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { BizDict, SelectOption, TransBizDictOption } from '@market/common/interface';

export const renderEnumName = (
  key?: string | number,
  enumList: BizDict[] = [],
  transBizDictOption: TransBizDictOption = {
    labelKey: 'itemName',
    valueKey: 'itemExtend1',
  }
): string => {
  if (!key) return '';

  const renderEnum = enumList.find(option => `${option[transBizDictOption.valueKey] as string}` === `${key}`);
  if (renderEnum) {
    return renderEnum[transBizDictOption.labelKey] as string;
  }
  return '';
};

export const findOptionNameFromList = <T>(value: any, optionList: T[] = [], labelKey: keyof T, valueKey: keyof T) => {
  const renderEnum = optionList.find(option => option[valueKey] === value);
  if (renderEnum) {
    return renderEnum[labelKey];
  }
  return '';
};

export const renderOptionName = (key: string, optionList: SelectOption[]): string | number =>
  findOptionNameFromList<SelectOption>(key, optionList, 'label', 'value');

/**
 * 枚举渲染函数
 */
export const renderBizdictName = (
  key: string,
  optionList: {
    dictValueName: string;
    dictValue: string;
  }[]
): string => {
  const renderEnum = optionList.find(option => `${option.dictValue}` === `${key}`);
  if (renderEnum) {
    return renderEnum.dictValueName;
  }
  return '';
};

/**
 * 历史枚举渲染函数，只有使用Edittable组件的时候需要使用
 */
export const renderOldBizdictName = (
  key: string,
  optionList: {
    itemName?: string;
    itemExtend1?: string;
  }[]
): string => {
  const renderEnum = optionList?.find(option => option?.itemExtend1 && `${option?.itemExtend1}` === `${key}`);
  if (renderEnum) {
    return renderEnum.itemName || '';
  }
  return '';
};

export const renderChildBizDictName = (key: string, childKey: string, enumList: BizDict[] = []) => {
  const parentEnum = enumList.find(option => option.dictValue === key);
  if (parentEnum) {
    const childEnum = parentEnum?.childList?.find(option => option.dictValue === childKey);
    return childEnum?.dictValueName;
  }
  return '';
};

export const mapChannelToOldBizdict = (channel: { code?: string; name?: string; parentName?: string }) => ({
  itemExtend1: channel.code,
  itemName: channel.parentName ? `${channel.parentName} - ${channel.name!}` : channel.name,
});

export const mapBizdictToOldBizdict = (channel: { dictValue: string; dictValueName: string }) => ({
  itemExtend1: channel.dictValue,
  itemName: channel.dictValueName,
});

export function mapFormulaToOldBizdict(formula: { formulaCode: string; formulaName: string }) {
  return {
    itemExtend1: formula.formulaCode,
    itemName: formula.formulaName,
  };
}
export const mapBizDictToOptions = (
  enumList: BizDict[] = [],
  optionKey?: {
    optionValue?: keyof BizDict;
    optionName?: keyof BizDict;
  }
) =>
  enumList.map(i => ({
    label: i[optionKey?.optionName ?? 'dictValueName'],
    value: i[optionKey?.optionValue ?? 'dictValue'],
  }));
